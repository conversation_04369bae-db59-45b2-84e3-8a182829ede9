client:495 [vite] connecting...
client:614 [vite] connected.
react-dom.development.js:29895 Download the React DevTools for a better development experience: https://reactjs.org/link/react-devtools
HybridModelViewer.tsx:256 HybridModelViewer Debug: {hasValidSTLModels: undefined, hasValidSketchfabUrl: true, modelsCount: 0, modelType: 'sketchfab', modelUrl: 'https://sketchfab.com/models/5a7af325a7db4e1a91c01a8f93c18598/embed', …}
HybridModelViewer.tsx:293 Renderizando Sketchfab Viewer: https://sketchfab.com/models/5a7af325a7db4e1a91c01a8f93c18598/embed
ModelViewer.tsx:135 State update: {initialLoad: true, firstModelLoaded: false, isTransitioning: false, customTransition: false, modelLoading: true, …}
use-analytics.ts:193 📊 Analytics Event: {type: 'page_view', action: 'view', data: {…}}
HybridModelViewer.tsx:256 HybridModelViewer Debug: {hasValidSTLModels: undefined, hasValidSketchfabUrl: true, modelsCount: 0, modelType: 'sketchfab', modelUrl: 'https://sketchfab.com/models/5a7af325a7db4e1a91c01a8f93c18598/embed', …}
HybridModelViewer.tsx:293 Renderizando Sketchfab Viewer: https://sketchfab.com/models/5a7af325a7db4e1a91c01a8f93c18598/embed
HybridModelViewer.tsx:256 HybridModelViewer Debug: {hasValidSTLModels: undefined, hasValidSketchfabUrl: true, modelsCount: 0, modelType: 'sketchfab', modelUrl: 'https://sketchfab.com/models/5a7af325a7db4e1a91c01a8f93c18598/embed', …}
HybridModelViewer.tsx:293 Renderizando Sketchfab Viewer: https://sketchfab.com/models/5a7af325a7db4e1a91c01a8f93c18598/embed
HybridModelViewer.tsx:256 HybridModelViewer Debug: {hasValidSTLModels: undefined, hasValidSketchfabUrl: true, modelsCount: 0, modelType: 'sketchfab', modelUrl: 'https://sketchfab.com/models/5a7af325a7db4e1a91c01a8f93c18598/embed', …}
HybridModelViewer.tsx:281 Renderizando Native Viewer: /models/5a7af325a7db4e1a91c01a8f93c18598.glb
HybridModelViewer.tsx:256 HybridModelViewer Debug: {hasValidSTLModels: undefined, hasValidSketchfabUrl: true, modelsCount: 0, modelType: 'sketchfab', modelUrl: 'https://sketchfab.com/models/5a7af325a7db4e1a91c01a8f93c18598/embed', …}
HybridModelViewer.tsx:281 Renderizando Native Viewer: /models/5a7af325a7db4e1a91c01a8f93c18598.glb
use-analytics.ts:193 📊 Analytics Event: {type: 'error', action: 'error_occurred', data: {…}}
index-e6b5343a.esm.js:1667  Uncaught Error: Could not load /models/5a7af325a7db4e1a91c01a8f93c18598.glb: Unexpected token '<', "
<!DOCTYPE "... is not valid JSON
    at index-e6b5343a.esm.js:1667:36
    at _onError (GLTFLoader.js:179:9)
    at Object.onLoad (GLTFLoader.js:210:11)
    at three.module.js:42369:38
(anônimo) @ index-e6b5343a.esm.js:1667
_onError @ GLTFLoader.js:179
(anônimo) @ GLTFLoader.js:210
(anônimo) @ three.module.js:42369
Promise.then
load @ three.module.js:42357
load @ GLTFLoader.js:195
(anônimo) @ index-e6b5343a.esm.js:1664
(anônimo) @ index-e6b5343a.esm.js:1664
(anônimo) @ index-e6b5343a.esm.js:1664
query @ index.js:51
suspend @ index.js:68
useLoader @ index-e6b5343a.esm.js:1679
useGLTF @ useGLTF.js:25
Model @ NativeModelViewer.tsx:36
renderWithHooks @ react-reconciler.development.js:7363
mountIndeterminateComponent @ react-reconciler.development.js:12327
beginWork @ react-reconciler.development.js:13831
beginWork$1 @ react-reconciler.development.js:19513
performUnitOfWork @ react-reconciler.development.js:18686
workLoopSync @ react-reconciler.development.js:18597
renderRootSync @ react-reconciler.development.js:18565
performConcurrentWorkOnRoot @ react-reconciler.development.js:17836
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
HybridModelViewer.tsx:256 HybridModelViewer Debug: {hasValidSTLModels: undefined, hasValidSketchfabUrl: true, modelsCount: 0, modelType: 'sketchfab', modelUrl: 'https://sketchfab.com/models/5a7af325a7db4e1a91c01a8f93c18598/embed', …}
HybridModelViewer.tsx:281 Renderizando Native Viewer: /models/5a7af325a7db4e1a91c01a8f93c18598.glb
use-analytics.ts:193 📊 Analytics Event: {type: 'error', action: 'error_occurred', data: {…}}
index-e6b5343a.esm.js:1667  Uncaught Error: Could not load /models/5a7af325a7db4e1a91c01a8f93c18598.glb: Unexpected token '<', "
<!DOCTYPE "... is not valid JSON
    at index-e6b5343a.esm.js:1667:36
    at _onError (GLTFLoader.js:179:9)
    at Object.onLoad (GLTFLoader.js:210:11)
    at three.module.js:42369:38
(anônimo) @ index-e6b5343a.esm.js:1667
_onError @ GLTFLoader.js:179
(anônimo) @ GLTFLoader.js:210
(anônimo) @ three.module.js:42369
Promise.then
load @ three.module.js:42357
load @ GLTFLoader.js:195
(anônimo) @ index-e6b5343a.esm.js:1664
(anônimo) @ index-e6b5343a.esm.js:1664
(anônimo) @ index-e6b5343a.esm.js:1664
query @ index.js:51
suspend @ index.js:68
useLoader @ index-e6b5343a.esm.js:1679
useGLTF @ useGLTF.js:25
Model @ NativeModelViewer.tsx:36
renderWithHooks @ react-reconciler.development.js:7363
mountIndeterminateComponent @ react-reconciler.development.js:12327
beginWork @ react-reconciler.development.js:13831
beginWork$1 @ react-reconciler.development.js:19513
performUnitOfWork @ react-reconciler.development.js:18686
workLoopSync @ react-reconciler.development.js:18597
renderRootSync @ react-reconciler.development.js:18565
performConcurrentWorkOnRoot @ react-reconciler.development.js:17836
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
index.tsx:86  The above error occurred in the <Model> component:

    at Model (http://localhost:8080/src/components/3d/NativeModelViewer.tsx:19:18)
    at Suspense
    at Suspense
    at ErrorBoundary (http://localhost:8080/node_modules/.vite/deps/chunk-ELWJUCVG.js?v=2449da8e:16487:5)
    at FiberProvider (http://localhost:8080/node_modules/.vite/deps/chunk-ELWJUCVG.js?v=2449da8e:18071:21)
    at Provider (http://localhost:8080/node_modules/.vite/deps/chunk-ELWJUCVG.js?v=2449da8e:17758:3)

React will try to recreate this component tree from scratch using the error boundary you provided, ErrorBoundary.
console.error @ index.tsx:86
logCapturedError @ react-reconciler.development.js:9747
callback @ react-reconciler.development.js:9815
callCallback @ react-reconciler.development.js:4279
commitUpdateQueue @ react-reconciler.development.js:4300
commitLayoutEffectOnFiber @ react-reconciler.development.js:14877
commitLayoutMountEffects_complete @ react-reconciler.development.js:16290
commitLayoutEffects_begin @ react-reconciler.development.js:16276
commitLayoutEffects @ react-reconciler.development.js:16214
commitRootImpl @ react-reconciler.development.js:18945
commitRoot @ react-reconciler.development.js:18811
finishConcurrentRender @ react-reconciler.development.js:17990
performConcurrentWorkOnRoot @ react-reconciler.development.js:17907
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
HybridModelViewer.tsx:256 HybridModelViewer Debug: {hasValidSTLModels: undefined, hasValidSketchfabUrl: true, modelsCount: 0, modelType: 'sketchfab', modelUrl: 'https://sketchfab.com/models/5a7af325a7db4e1a91c01a8f93c18598/embed', …}
HybridModelViewer.tsx:281 Renderizando Native Viewer: /models/5a7af325a7db4e1a91c01a8f93c18598.glb
index.tsx:86  Warning: Cannot update a component (`Cases`) while rendering a different component (`ForwardRef(Canvas)`). To locate the bad setState() call inside `ForwardRef(Canvas)`, follow the stack trace as described in https://reactjs.org/link/setstate-in-render
    at Canvas (http://localhost:8080/node_modules/.vite/deps/chunk-ELWJUCVG.js?v=2449da8e:18215:3)
    at FiberProvider (http://localhost:8080/node_modules/.vite/deps/chunk-ELWJUCVG.js?v=2449da8e:18071:21)
    at CanvasWrapper
    at div
    at NativeModelViewer (http://localhost:8080/src/components/3d/NativeModelViewer.tsx:116:30)
    at div
    at HybridModelViewer (http://localhost:8080/src/components/3d/HybridModelViewer.tsx:16:30)
    at ModelViewer (http://localhost:8080/src/components/cases/ModelViewer.tsx:16:24)
    at div
    at Cases (http://localhost:8080/src/pages/Cases.tsx:31:195)
    at RenderedRoute (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4069:5)
    at Routes (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4508:5)
    at Router (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4451:15)
    at BrowserRouter (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:5196:5)
    at Provider (http://localhost:8080/node_modules/.vite/deps/chunk-MHLXIYJU.js?v=2449da8e:51:15)
    at TooltipProvider (http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=2449da8e:87:5)
    at QueryClientProvider (http://localhost:8080/node_modules/.vite/deps/@tanstack_react-query.js?v=2449da8e:2933:3)
    at ThemeProvider (http://localhost:8080/src/hooks/use-theme.tsx:20:33)
    at App
console.error @ index.tsx:86
printWarning @ react-dom.development.js:86
error @ react-dom.development.js:60
warnAboutRenderPhaseUpdatesInDEV @ react-dom.development.js:27531
scheduleUpdateOnFiber @ react-dom.development.js:25537
dispatchSetState @ react-dom.development.js:16708
(anônimo) @ use-analytics.ts:187
(anônimo) @ use-analytics.ts:233
handleError @ use-analytics.ts:281
invokeGuardedCallbackDev @ react-dom.development.js:4213
invokeGuardedCallback @ react-dom.development.js:4277
beginWork$1 @ react-dom.development.js:27490
performUnitOfWork @ react-dom.development.js:26596
workLoopSync @ react-dom.development.js:26505
renderRootSync @ react-dom.development.js:26473
performConcurrentWorkOnRoot @ react-dom.development.js:25777
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
use-analytics.ts:193 📊 Analytics Event: {type: 'error', action: 'error_occurred', data: {…}}
index-e6b5343a.esm.js:1667  Uncaught Error: Could not load /models/5a7af325a7db4e1a91c01a8f93c18598.glb: Unexpected token '<', "
<!DOCTYPE "... is not valid JSON
    at index-e6b5343a.esm.js:1667:36
    at _onError (GLTFLoader.js:179:9)
    at Object.onLoad (GLTFLoader.js:210:11)
    at three.module.js:42369:38
(anônimo) @ index-e6b5343a.esm.js:1667
_onError @ GLTFLoader.js:179
(anônimo) @ GLTFLoader.js:210
(anônimo) @ three.module.js:42369
Promise.then
load @ three.module.js:42357
load @ GLTFLoader.js:195
(anônimo) @ index-e6b5343a.esm.js:1664
(anônimo) @ index-e6b5343a.esm.js:1664
(anônimo) @ index-e6b5343a.esm.js:1664
query @ index.js:51
suspend @ index.js:68
useLoader @ index-e6b5343a.esm.js:1679
useGLTF @ useGLTF.js:25
Model @ NativeModelViewer.tsx:36
renderWithHooks @ react-reconciler.development.js:7363
mountIndeterminateComponent @ react-reconciler.development.js:12327
beginWork @ react-reconciler.development.js:13831
beginWork$1 @ react-reconciler.development.js:19513
performUnitOfWork @ react-reconciler.development.js:18686
workLoopSync @ react-reconciler.development.js:18597
renderRootSync @ react-reconciler.development.js:18565
performConcurrentWorkOnRoot @ react-reconciler.development.js:17836
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
HybridModelViewer.tsx:256 HybridModelViewer Debug: {hasValidSTLModels: undefined, hasValidSketchfabUrl: true, modelsCount: 0, modelType: 'sketchfab', modelUrl: 'https://sketchfab.com/models/5a7af325a7db4e1a91c01a8f93c18598/embed', …}
HybridModelViewer.tsx:281 Renderizando Native Viewer: /models/5a7af325a7db4e1a91c01a8f93c18598.glb
use-analytics.ts:193 📊 Analytics Event: {type: 'error', action: 'error_occurred', data: {…}}
index-e6b5343a.esm.js:1667  Uncaught Error: Could not load /models/5a7af325a7db4e1a91c01a8f93c18598.glb: Unexpected token '<', "
<!DOCTYPE "... is not valid JSON
    at index-e6b5343a.esm.js:1667:36
    at _onError (GLTFLoader.js:179:9)
    at Object.onLoad (GLTFLoader.js:210:11)
    at three.module.js:42369:38
(anônimo) @ index-e6b5343a.esm.js:1667
_onError @ GLTFLoader.js:179
(anônimo) @ GLTFLoader.js:210
(anônimo) @ three.module.js:42369
Promise.then
load @ three.module.js:42357
load @ GLTFLoader.js:195
(anônimo) @ index-e6b5343a.esm.js:1664
(anônimo) @ index-e6b5343a.esm.js:1664
(anônimo) @ index-e6b5343a.esm.js:1664
query @ index.js:51
suspend @ index.js:68
useLoader @ index-e6b5343a.esm.js:1679
useGLTF @ useGLTF.js:25
Model @ NativeModelViewer.tsx:36
renderWithHooks @ react-reconciler.development.js:7363
mountIndeterminateComponent @ react-reconciler.development.js:12327
beginWork @ react-reconciler.development.js:13831
beginWork$1 @ react-reconciler.development.js:19513
performUnitOfWork @ react-reconciler.development.js:18686
workLoopSync @ react-reconciler.development.js:18597
renderRootSync @ react-reconciler.development.js:18565
performConcurrentWorkOnRoot @ react-reconciler.development.js:17836
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
index.tsx:86  The above error occurred in the <ForwardRef(Canvas)> component:

    at Canvas (http://localhost:8080/node_modules/.vite/deps/chunk-ELWJUCVG.js?v=2449da8e:18215:3)
    at FiberProvider (http://localhost:8080/node_modules/.vite/deps/chunk-ELWJUCVG.js?v=2449da8e:18071:21)
    at CanvasWrapper
    at div
    at NativeModelViewer (http://localhost:8080/src/components/3d/NativeModelViewer.tsx:116:30)
    at div
    at HybridModelViewer (http://localhost:8080/src/components/3d/HybridModelViewer.tsx:16:30)
    at ModelViewer (http://localhost:8080/src/components/cases/ModelViewer.tsx:16:24)
    at div
    at Cases (http://localhost:8080/src/pages/Cases.tsx:31:195)
    at RenderedRoute (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4069:5)
    at Routes (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4508:5)
    at Router (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4451:15)
    at BrowserRouter (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:5196:5)
    at Provider (http://localhost:8080/node_modules/.vite/deps/chunk-MHLXIYJU.js?v=2449da8e:51:15)
    at TooltipProvider (http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=2449da8e:87:5)
    at QueryClientProvider (http://localhost:8080/node_modules/.vite/deps/@tanstack_react-query.js?v=2449da8e:2933:3)
    at ThemeProvider (http://localhost:8080/src/hooks/use-theme.tsx:20:33)
    at App

Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://reactjs.org/link/error-boundaries to learn more about error boundaries.
console.error @ index.tsx:86
logCapturedError @ react-dom.development.js:18704
update.callback @ react-dom.development.js:18737
callCallback @ react-dom.development.js:15036
commitUpdateQueue @ react-dom.development.js:15057
commitLayoutEffectOnFiber @ react-dom.development.js:23430
commitLayoutMountEffects_complete @ react-dom.development.js:24727
commitLayoutEffects_begin @ react-dom.development.js:24713
commitLayoutEffects @ react-dom.development.js:24651
commitRootImpl @ react-dom.development.js:26862
commitRoot @ react-dom.development.js:26721
finishConcurrentRender @ react-dom.development.js:25931
performConcurrentWorkOnRoot @ react-dom.development.js:25848
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
use-analytics.ts:193 📊 Analytics Event: {type: 'error', action: 'error_occurred', data: {…}}
react-dom.development.js:26962  Uncaught Error: Could not load /models/5a7af325a7db4e1a91c01a8f93c18598.glb: Unexpected token '<', "
<!DOCTYPE "... is not valid JSON
    at index-e6b5343a.esm.js:1667:36
    at _onError (GLTFLoader.js:179:9)
    at Object.onLoad (GLTFLoader.js:210:11)
    at three.module.js:42369:38
(anônimo) @ index-e6b5343a.esm.js:1667
_onError @ GLTFLoader.js:179
(anônimo) @ GLTFLoader.js:210
(anônimo) @ three.module.js:42369
Promise.then
load @ three.module.js:42357
load @ GLTFLoader.js:195
(anônimo) @ index-e6b5343a.esm.js:1664
(anônimo) @ index-e6b5343a.esm.js:1664
(anônimo) @ index-e6b5343a.esm.js:1664
query @ index.js:51
suspend @ index.js:68
useLoader @ index-e6b5343a.esm.js:1679
useGLTF @ useGLTF.js:25
Model @ NativeModelViewer.tsx:36
renderWithHooks @ react-reconciler.development.js:7363
mountIndeterminateComponent @ react-reconciler.development.js:12327
beginWork @ react-reconciler.development.js:13831
beginWork$1 @ react-reconciler.development.js:19513
performUnitOfWork @ react-reconciler.development.js:18686
workLoopSync @ react-reconciler.development.js:18597
renderRootSync @ react-reconciler.development.js:18565
performConcurrentWorkOnRoot @ react-reconciler.development.js:17836
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
three.module.js:28857 THREE.WebGLRenderer: Context Lost.
