client:495 [vite] connecting...
client:614 [vite] connected.
react-dom.development.js:29895 Download the React DevTools for a better development experience: https://reactjs.org/link/react-devtools
ModelViewer.tsx:135 State update: Object
use-analytics.ts:193 📊 Analytics Event: Object
use-analytics.ts:193 📊 Analytics Event: Object
index.tsx:86  Warning: Updating a style property during rerender (animationDelay) when a conflicting property is set (animation) can lead to styling bugs. To avoid this, don't mix shorthand and non-shorthand properties for the same value; instead, replace the shorthand with separate values.
    at div
    at div
    at div
    at div
    at div
    at IntroSection (http://localhost:8080/src/components/cases/IntroSection.tsx:14:25)
    at div
    at Cases (http://localhost:8080/src/pages/Cases.tsx:31:195)
    at RenderedRoute (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4069:5)
    at Routes (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4508:5)
    at Router (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4451:15)
    at BrowserRouter (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:5196:5)
    at Provider (http://localhost:8080/node_modules/.vite/deps/chunk-MHLXIYJU.js?v=4d7ffbec:51:15)
    at TooltipProvider (http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=4d7ffbec:87:5)
    at QueryClientProvider (http://localhost:8080/node_modules/.vite/deps/@tanstack_react-query.js?v=4d7ffbec:2933:3)
    at ThemeProvider (http://localhost:8080/src/hooks/use-theme.tsx:20:33)
    at App
console.error @ index.tsx:86
printWarning @ react-dom.development.js:86
error @ react-dom.development.js:60
validateShorthandPropertyCollisionInDev @ react-dom.development.js:2899
diffProperties @ react-dom.development.js:10123
prepareUpdate @ react-dom.development.js:10977
updateHostComponent$1 @ react-dom.development.js:21825
completeWork @ react-dom.development.js:22194
completeUnitOfWork @ react-dom.development.js:26635
performUnitOfWork @ react-dom.development.js:26607
workLoopSync @ react-dom.development.js:26505
renderRootSync @ react-dom.development.js:26473
performConcurrentWorkOnRoot @ react-dom.development.js:25777
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
index.tsx:86  Warning: Updating a style property during rerender (animationDelay) when a conflicting property is set (animation) can lead to styling bugs. To avoid this, don't mix shorthand and non-shorthand properties for the same value; instead, replace the shorthand with separate values.
    at div
    at div
    at div
    at div
    at div
    at IntroSection (http://localhost:8080/src/components/cases/IntroSection.tsx:14:25)
    at div
    at Cases (http://localhost:8080/src/pages/Cases.tsx:31:195)
    at RenderedRoute (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4069:5)
    at Routes (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4508:5)
    at Router (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4451:15)
    at BrowserRouter (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:5196:5)
    at Provider (http://localhost:8080/node_modules/.vite/deps/chunk-MHLXIYJU.js?v=4d7ffbec:51:15)
    at TooltipProvider (http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=4d7ffbec:87:5)
    at QueryClientProvider (http://localhost:8080/node_modules/.vite/deps/@tanstack_react-query.js?v=4d7ffbec:2933:3)
    at ThemeProvider (http://localhost:8080/src/hooks/use-theme.tsx:20:33)
    at App
console.error @ index.tsx:86
printWarning @ react-dom.development.js:86
error @ react-dom.development.js:60
validateShorthandPropertyCollisionInDev @ react-dom.development.js:2899
diffProperties @ react-dom.development.js:10123
prepareUpdate @ react-dom.development.js:10977
updateHostComponent$1 @ react-dom.development.js:21825
completeWork @ react-dom.development.js:22194
completeUnitOfWork @ react-dom.development.js:26635
performUnitOfWork @ react-dom.development.js:26607
workLoopSync @ react-dom.development.js:26505
renderRootSync @ react-dom.development.js:26473
performConcurrentWorkOnRoot @ react-dom.development.js:25777
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
index.tsx:86  Warning: Updating a style property during rerender (animationDelay) when a conflicting property is set (animation) can lead to styling bugs. To avoid this, don't mix shorthand and non-shorthand properties for the same value; instead, replace the shorthand with separate values.
    at div
    at div
    at div
    at div
    at div
    at IntroSection (http://localhost:8080/src/components/cases/IntroSection.tsx:14:25)
    at div
    at Cases (http://localhost:8080/src/pages/Cases.tsx:31:195)
    at RenderedRoute (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4069:5)
    at Routes (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4508:5)
    at Router (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4451:15)
    at BrowserRouter (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:5196:5)
    at Provider (http://localhost:8080/node_modules/.vite/deps/chunk-MHLXIYJU.js?v=4d7ffbec:51:15)
    at TooltipProvider (http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=4d7ffbec:87:5)
    at QueryClientProvider (http://localhost:8080/node_modules/.vite/deps/@tanstack_react-query.js?v=4d7ffbec:2933:3)
    at ThemeProvider (http://localhost:8080/src/hooks/use-theme.tsx:20:33)
    at App
console.error @ index.tsx:86
printWarning @ react-dom.development.js:86
error @ react-dom.development.js:60
validateShorthandPropertyCollisionInDev @ react-dom.development.js:2899
diffProperties @ react-dom.development.js:10123
prepareUpdate @ react-dom.development.js:10977
updateHostComponent$1 @ react-dom.development.js:21825
completeWork @ react-dom.development.js:22194
completeUnitOfWork @ react-dom.development.js:26635
performUnitOfWork @ react-dom.development.js:26607
workLoopSync @ react-dom.development.js:26505
renderRootSync @ react-dom.development.js:26473
performConcurrentWorkOnRoot @ react-dom.development.js:25777
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
index.tsx:86  Warning: Updating a style property during rerender (animationDelay) when a conflicting property is set (animation) can lead to styling bugs. To avoid this, don't mix shorthand and non-shorthand properties for the same value; instead, replace the shorthand with separate values.
    at div
    at div
    at div
    at div
    at div
    at IntroSection (http://localhost:8080/src/components/cases/IntroSection.tsx:14:25)
    at div
    at Cases (http://localhost:8080/src/pages/Cases.tsx:31:195)
    at RenderedRoute (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4069:5)
    at Routes (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4508:5)
    at Router (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4451:15)
    at BrowserRouter (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:5196:5)
    at Provider (http://localhost:8080/node_modules/.vite/deps/chunk-MHLXIYJU.js?v=4d7ffbec:51:15)
    at TooltipProvider (http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=4d7ffbec:87:5)
    at QueryClientProvider (http://localhost:8080/node_modules/.vite/deps/@tanstack_react-query.js?v=4d7ffbec:2933:3)
    at ThemeProvider (http://localhost:8080/src/hooks/use-theme.tsx:20:33)
    at App
console.error @ index.tsx:86
printWarning @ react-dom.development.js:86
error @ react-dom.development.js:60
validateShorthandPropertyCollisionInDev @ react-dom.development.js:2899
diffProperties @ react-dom.development.js:10123
prepareUpdate @ react-dom.development.js:10977
updateHostComponent$1 @ react-dom.development.js:21825
completeWork @ react-dom.development.js:22194
completeUnitOfWork @ react-dom.development.js:26635
performUnitOfWork @ react-dom.development.js:26607
workLoopSync @ react-dom.development.js:26505
renderRootSync @ react-dom.development.js:26473
performConcurrentWorkOnRoot @ react-dom.development.js:25777
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
index.tsx:86  Warning: Updating a style property during rerender (animationDelay) when a conflicting property is set (animation) can lead to styling bugs. To avoid this, don't mix shorthand and non-shorthand properties for the same value; instead, replace the shorthand with separate values.
    at div
    at div
    at div
    at div
    at div
    at IntroSection (http://localhost:8080/src/components/cases/IntroSection.tsx:14:25)
    at div
    at Cases (http://localhost:8080/src/pages/Cases.tsx:31:195)
    at RenderedRoute (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4069:5)
    at Routes (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4508:5)
    at Router (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4451:15)
    at BrowserRouter (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:5196:5)
    at Provider (http://localhost:8080/node_modules/.vite/deps/chunk-MHLXIYJU.js?v=4d7ffbec:51:15)
    at TooltipProvider (http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=4d7ffbec:87:5)
    at QueryClientProvider (http://localhost:8080/node_modules/.vite/deps/@tanstack_react-query.js?v=4d7ffbec:2933:3)
    at ThemeProvider (http://localhost:8080/src/hooks/use-theme.tsx:20:33)
    at App
console.error @ index.tsx:86
printWarning @ react-dom.development.js:86
error @ react-dom.development.js:60
validateShorthandPropertyCollisionInDev @ react-dom.development.js:2899
diffProperties @ react-dom.development.js:10123
prepareUpdate @ react-dom.development.js:10977
updateHostComponent$1 @ react-dom.development.js:21825
completeWork @ react-dom.development.js:22194
completeUnitOfWork @ react-dom.development.js:26635
performUnitOfWork @ react-dom.development.js:26607
workLoopSync @ react-dom.development.js:26505
renderRootSync @ react-dom.development.js:26473
performConcurrentWorkOnRoot @ react-dom.development.js:25777
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
index.tsx:86  Warning: Updating a style property during rerender (animationDelay) when a conflicting property is set (animation) can lead to styling bugs. To avoid this, don't mix shorthand and non-shorthand properties for the same value; instead, replace the shorthand with separate values.
    at div
    at div
    at div
    at div
    at div
    at IntroSection (http://localhost:8080/src/components/cases/IntroSection.tsx:14:25)
    at div
    at Cases (http://localhost:8080/src/pages/Cases.tsx:31:195)
    at RenderedRoute (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4069:5)
    at Routes (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4508:5)
    at Router (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4451:15)
    at BrowserRouter (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:5196:5)
    at Provider (http://localhost:8080/node_modules/.vite/deps/chunk-MHLXIYJU.js?v=4d7ffbec:51:15)
    at TooltipProvider (http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=4d7ffbec:87:5)
    at QueryClientProvider (http://localhost:8080/node_modules/.vite/deps/@tanstack_react-query.js?v=4d7ffbec:2933:3)
    at ThemeProvider (http://localhost:8080/src/hooks/use-theme.tsx:20:33)
    at App
console.error @ index.tsx:86
printWarning @ react-dom.development.js:86
error @ react-dom.development.js:60
validateShorthandPropertyCollisionInDev @ react-dom.development.js:2899
diffProperties @ react-dom.development.js:10123
prepareUpdate @ react-dom.development.js:10977
updateHostComponent$1 @ react-dom.development.js:21825
completeWork @ react-dom.development.js:22194
completeUnitOfWork @ react-dom.development.js:26635
performUnitOfWork @ react-dom.development.js:26607
workLoopSync @ react-dom.development.js:26505
renderRootSync @ react-dom.development.js:26473
performConcurrentWorkOnRoot @ react-dom.development.js:25777
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
index.tsx:86  Warning: Updating a style property during rerender (animationDelay) when a conflicting property is set (animation) can lead to styling bugs. To avoid this, don't mix shorthand and non-shorthand properties for the same value; instead, replace the shorthand with separate values.
    at div
    at div
    at div
    at div
    at div
    at IntroSection (http://localhost:8080/src/components/cases/IntroSection.tsx:14:25)
    at div
    at Cases (http://localhost:8080/src/pages/Cases.tsx:31:195)
    at RenderedRoute (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4069:5)
    at Routes (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4508:5)
    at Router (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4451:15)
    at BrowserRouter (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:5196:5)
    at Provider (http://localhost:8080/node_modules/.vite/deps/chunk-MHLXIYJU.js?v=4d7ffbec:51:15)
    at TooltipProvider (http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=4d7ffbec:87:5)
    at QueryClientProvider (http://localhost:8080/node_modules/.vite/deps/@tanstack_react-query.js?v=4d7ffbec:2933:3)
    at ThemeProvider (http://localhost:8080/src/hooks/use-theme.tsx:20:33)
    at App
console.error @ index.tsx:86
printWarning @ react-dom.development.js:86
error @ react-dom.development.js:60
validateShorthandPropertyCollisionInDev @ react-dom.development.js:2899
diffProperties @ react-dom.development.js:10123
prepareUpdate @ react-dom.development.js:10977
updateHostComponent$1 @ react-dom.development.js:21825
completeWork @ react-dom.development.js:22194
completeUnitOfWork @ react-dom.development.js:26635
performUnitOfWork @ react-dom.development.js:26607
workLoopSync @ react-dom.development.js:26505
renderRootSync @ react-dom.development.js:26473
performConcurrentWorkOnRoot @ react-dom.development.js:25777
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ModelViewer.tsx:92 Iframe loaded with model: https://sketchfab.com/models/5a7af325a7db4e1a91c01a8f93c18598/embed
ModelViewer.tsx:99 Usando tempo de transição de 2650ms (mobile)
ModelViewer.tsx:105 Transition ended after 1750481316954ms (model loaded)
use-cases.tsx:88 Model loaded successfully: 3 Pontes de 3 elementos (superior e inferior) 
ModelViewer.tsx:35 First model loaded successfully
ModelViewer.tsx:135 State update: Object
ModelViewer.tsx:135 State update: Object
[Violation]Permissions policy violation: accelerometer is not allowed in this document.
[Violation]Permissions policy violation: accelerometer is not allowed in this document.
[Violation]Permissions policy violation: accelerometer is not allowed in this document.
[Violation]Permissions policy violation: accelerometer is not allowed in this document.
[Violation]Permissions policy violation: accelerometer is not allowed in this document.
[Violation]Permissions policy violation: accelerometer is not allowed in this document.
[Violation]Permissions policy violation: accelerometer is not allowed in this document.
[Violation]Permissions policy violation: accelerometer is not allowed in this document.
The deviceorientation events are blocked by permissions policy. See <URL>
The deviceorientation events are blocked by permissions policy. See <URL>
The deviceorientation events are blocked by permissions policy. See <URL>
The deviceorientation events are blocked by permissions policy. See <URL>
The deviceorientation events are blocked by permissions policy. See <URL>
The deviceorientation events are blocked by permissions policy. See <URL>
The deviceorientation events are blocked by permissions policy. See <URL>
The deviceorientation events are blocked by permissions policy. See <URL>
  [Violation]Permissions policy violation: xr-spatial-tracking is not allowed in this document.
pollHeadset @ f45fb3a…-v2.js:2
initialize @ dcd90f2…-v2.js:1
<computed> @ fad4b81…-v2.js:1
t.View @ a71993b…-v2.js:2
constructor @ fad4b81…-v2.js:1
constructor @ fad4b81…-v2.js:1
V.r @ a71993b…-v2.js:2
i @ dcd90f2…-v2.js:1
(anônimo) @ dcd90f2…-v2.js:1
init @ dcd90f2…-v2.js:1
addFeature @ dcd90f2…-v2.js:1
(anônimo) @ dcd90f2…-v2.js:1
run @ dcd90f2…-v2.js:1
onModelLoad @ daa012a…-v2.js:1
l @ b840b92…-v2.js:2
c @ b840b92…-v2.js:2
a @ a71993b…-v2.js:2
sketchfab.com/i/archives/ar?model=5a7af325a7db4e1a91c01a8f93c18598&platform=android:1 
            
            
            Failed to load resource: the server responded with a status of 400 ()
ModelViewer.tsx:43 Transitioning from intro to model view - no reload needed
ModelViewer.tsx:135 State update: Object
ModelViewer.tsx:135 State update: Object
index.tsx:86  Warning: Updating a style property during rerender (animationDelay) when a conflicting property is set (animation) can lead to styling bugs. To avoid this, don't mix shorthand and non-shorthand properties for the same value; instead, replace the shorthand with separate values.
    at div
    at div
    at div
    at div
    at div
    at IntroSection (http://localhost:8080/src/components/cases/IntroSection.tsx:14:25)
    at div
    at Cases (http://localhost:8080/src/pages/Cases.tsx:31:195)
    at RenderedRoute (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4069:5)
    at Routes (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4508:5)
    at Router (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4451:15)
    at BrowserRouter (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:5196:5)
    at Provider (http://localhost:8080/node_modules/.vite/deps/chunk-MHLXIYJU.js?v=4d7ffbec:51:15)
    at TooltipProvider (http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=4d7ffbec:87:5)
    at QueryClientProvider (http://localhost:8080/node_modules/.vite/deps/@tanstack_react-query.js?v=4d7ffbec:2933:3)
    at ThemeProvider (http://localhost:8080/src/hooks/use-theme.tsx:20:33)
    at App
console.error @ index.tsx:86
printWarning @ react-dom.development.js:86
error @ react-dom.development.js:60
validateShorthandPropertyCollisionInDev @ react-dom.development.js:2899
diffProperties @ react-dom.development.js:10123
prepareUpdate @ react-dom.development.js:10977
updateHostComponent$1 @ react-dom.development.js:21825
completeWork @ react-dom.development.js:22194
completeUnitOfWork @ react-dom.development.js:26635
performUnitOfWork @ react-dom.development.js:26607
workLoopSync @ react-dom.development.js:26505
renderRootSync @ react-dom.development.js:26473
performSyncWorkOnRoot @ react-dom.development.js:26124
flushSyncCallbacks @ react-dom.development.js:12042
(anônimo) @ react-dom.development.js:25690
index.tsx:86  Warning: Updating a style property during rerender (animationDelay) when a conflicting property is set (animation) can lead to styling bugs. To avoid this, don't mix shorthand and non-shorthand properties for the same value; instead, replace the shorthand with separate values.
    at div
    at div
    at div
    at div
    at div
    at IntroSection (http://localhost:8080/src/components/cases/IntroSection.tsx:14:25)
    at div
    at Cases (http://localhost:8080/src/pages/Cases.tsx:31:195)
    at RenderedRoute (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4069:5)
    at Routes (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4508:5)
    at Router (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4451:15)
    at BrowserRouter (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:5196:5)
    at Provider (http://localhost:8080/node_modules/.vite/deps/chunk-MHLXIYJU.js?v=4d7ffbec:51:15)
    at TooltipProvider (http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=4d7ffbec:87:5)
    at QueryClientProvider (http://localhost:8080/node_modules/.vite/deps/@tanstack_react-query.js?v=4d7ffbec:2933:3)
    at ThemeProvider (http://localhost:8080/src/hooks/use-theme.tsx:20:33)
    at App
console.error @ index.tsx:86
printWarning @ react-dom.development.js:86
error @ react-dom.development.js:60
validateShorthandPropertyCollisionInDev @ react-dom.development.js:2899
diffProperties @ react-dom.development.js:10123
prepareUpdate @ react-dom.development.js:10977
updateHostComponent$1 @ react-dom.development.js:21825
completeWork @ react-dom.development.js:22194
completeUnitOfWork @ react-dom.development.js:26635
performUnitOfWork @ react-dom.development.js:26607
workLoopSync @ react-dom.development.js:26505
renderRootSync @ react-dom.development.js:26473
performSyncWorkOnRoot @ react-dom.development.js:26124
flushSyncCallbacks @ react-dom.development.js:12042
(anônimo) @ react-dom.development.js:25690
index.tsx:86  Warning: Updating a style property during rerender (animationDelay) when a conflicting property is set (animation) can lead to styling bugs. To avoid this, don't mix shorthand and non-shorthand properties for the same value; instead, replace the shorthand with separate values.
    at div
    at div
    at div
    at div
    at div
    at IntroSection (http://localhost:8080/src/components/cases/IntroSection.tsx:14:25)
    at div
    at Cases (http://localhost:8080/src/pages/Cases.tsx:31:195)
    at RenderedRoute (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4069:5)
    at Routes (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4508:5)
    at Router (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4451:15)
    at BrowserRouter (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:5196:5)
    at Provider (http://localhost:8080/node_modules/.vite/deps/chunk-MHLXIYJU.js?v=4d7ffbec:51:15)
    at TooltipProvider (http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=4d7ffbec:87:5)
    at QueryClientProvider (http://localhost:8080/node_modules/.vite/deps/@tanstack_react-query.js?v=4d7ffbec:2933:3)
    at ThemeProvider (http://localhost:8080/src/hooks/use-theme.tsx:20:33)
    at App
console.error @ index.tsx:86
printWarning @ react-dom.development.js:86
error @ react-dom.development.js:60
validateShorthandPropertyCollisionInDev @ react-dom.development.js:2899
diffProperties @ react-dom.development.js:10123
prepareUpdate @ react-dom.development.js:10977
updateHostComponent$1 @ react-dom.development.js:21825
completeWork @ react-dom.development.js:22194
completeUnitOfWork @ react-dom.development.js:26635
performUnitOfWork @ react-dom.development.js:26607
workLoopSync @ react-dom.development.js:26505
renderRootSync @ react-dom.development.js:26473
performSyncWorkOnRoot @ react-dom.development.js:26124
flushSyncCallbacks @ react-dom.development.js:12042
(anônimo) @ react-dom.development.js:25690
index.tsx:86  Warning: Updating a style property during rerender (animationDelay) when a conflicting property is set (animation) can lead to styling bugs. To avoid this, don't mix shorthand and non-shorthand properties for the same value; instead, replace the shorthand with separate values.
    at div
    at div
    at div
    at div
    at div
    at IntroSection (http://localhost:8080/src/components/cases/IntroSection.tsx:14:25)
    at div
    at Cases (http://localhost:8080/src/pages/Cases.tsx:31:195)
    at RenderedRoute (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4069:5)
    at Routes (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4508:5)
    at Router (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4451:15)
    at BrowserRouter (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:5196:5)
    at Provider (http://localhost:8080/node_modules/.vite/deps/chunk-MHLXIYJU.js?v=4d7ffbec:51:15)
    at TooltipProvider (http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=4d7ffbec:87:5)
    at QueryClientProvider (http://localhost:8080/node_modules/.vite/deps/@tanstack_react-query.js?v=4d7ffbec:2933:3)
    at ThemeProvider (http://localhost:8080/src/hooks/use-theme.tsx:20:33)
    at App
console.error @ index.tsx:86
printWarning @ react-dom.development.js:86
error @ react-dom.development.js:60
validateShorthandPropertyCollisionInDev @ react-dom.development.js:2899
diffProperties @ react-dom.development.js:10123
prepareUpdate @ react-dom.development.js:10977
updateHostComponent$1 @ react-dom.development.js:21825
completeWork @ react-dom.development.js:22194
completeUnitOfWork @ react-dom.development.js:26635
performUnitOfWork @ react-dom.development.js:26607
workLoopSync @ react-dom.development.js:26505
renderRootSync @ react-dom.development.js:26473
performSyncWorkOnRoot @ react-dom.development.js:26124
flushSyncCallbacks @ react-dom.development.js:12042
(anônimo) @ react-dom.development.js:25690
index.tsx:86  Warning: Updating a style property during rerender (animationDelay) when a conflicting property is set (animation) can lead to styling bugs. To avoid this, don't mix shorthand and non-shorthand properties for the same value; instead, replace the shorthand with separate values.
    at div
    at div
    at div
    at div
    at div
    at IntroSection (http://localhost:8080/src/components/cases/IntroSection.tsx:14:25)
    at div
    at Cases (http://localhost:8080/src/pages/Cases.tsx:31:195)
    at RenderedRoute (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4069:5)
    at Routes (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4508:5)
    at Router (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4451:15)
    at BrowserRouter (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:5196:5)
    at Provider (http://localhost:8080/node_modules/.vite/deps/chunk-MHLXIYJU.js?v=4d7ffbec:51:15)
    at TooltipProvider (http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=4d7ffbec:87:5)
    at QueryClientProvider (http://localhost:8080/node_modules/.vite/deps/@tanstack_react-query.js?v=4d7ffbec:2933:3)
    at ThemeProvider (http://localhost:8080/src/hooks/use-theme.tsx:20:33)
    at App
console.error @ index.tsx:86
printWarning @ react-dom.development.js:86
error @ react-dom.development.js:60
validateShorthandPropertyCollisionInDev @ react-dom.development.js:2899
diffProperties @ react-dom.development.js:10123
prepareUpdate @ react-dom.development.js:10977
updateHostComponent$1 @ react-dom.development.js:21825
completeWork @ react-dom.development.js:22194
completeUnitOfWork @ react-dom.development.js:26635
performUnitOfWork @ react-dom.development.js:26607
workLoopSync @ react-dom.development.js:26505
renderRootSync @ react-dom.development.js:26473
performSyncWorkOnRoot @ react-dom.development.js:26124
flushSyncCallbacks @ react-dom.development.js:12042
(anônimo) @ react-dom.development.js:25690
index.tsx:86  Warning: Updating a style property during rerender (animationDelay) when a conflicting property is set (animation) can lead to styling bugs. To avoid this, don't mix shorthand and non-shorthand properties for the same value; instead, replace the shorthand with separate values.
    at div
    at div
    at div
    at div
    at div
    at IntroSection (http://localhost:8080/src/components/cases/IntroSection.tsx:14:25)
    at div
    at Cases (http://localhost:8080/src/pages/Cases.tsx:31:195)
    at RenderedRoute (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4069:5)
    at Routes (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4508:5)
    at Router (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4451:15)
    at BrowserRouter (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:5196:5)
    at Provider (http://localhost:8080/node_modules/.vite/deps/chunk-MHLXIYJU.js?v=4d7ffbec:51:15)
    at TooltipProvider (http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=4d7ffbec:87:5)
    at QueryClientProvider (http://localhost:8080/node_modules/.vite/deps/@tanstack_react-query.js?v=4d7ffbec:2933:3)
    at ThemeProvider (http://localhost:8080/src/hooks/use-theme.tsx:20:33)
    at App
console.error @ index.tsx:86
printWarning @ react-dom.development.js:86
error @ react-dom.development.js:60
validateShorthandPropertyCollisionInDev @ react-dom.development.js:2899
diffProperties @ react-dom.development.js:10123
prepareUpdate @ react-dom.development.js:10977
updateHostComponent$1 @ react-dom.development.js:21825
completeWork @ react-dom.development.js:22194
completeUnitOfWork @ react-dom.development.js:26635
performUnitOfWork @ react-dom.development.js:26607
workLoopSync @ react-dom.development.js:26505
renderRootSync @ react-dom.development.js:26473
performSyncWorkOnRoot @ react-dom.development.js:26124
flushSyncCallbacks @ react-dom.development.js:12042
(anônimo) @ react-dom.development.js:25690
index.tsx:86  Warning: Updating a style property during rerender (animationDelay) when a conflicting property is set (animation) can lead to styling bugs. To avoid this, don't mix shorthand and non-shorthand properties for the same value; instead, replace the shorthand with separate values.
    at div
    at div
    at div
    at div
    at div
    at IntroSection (http://localhost:8080/src/components/cases/IntroSection.tsx:14:25)
    at div
    at Cases (http://localhost:8080/src/pages/Cases.tsx:31:195)
    at RenderedRoute (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4069:5)
    at Routes (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4508:5)
    at Router (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4451:15)
    at BrowserRouter (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:5196:5)
    at Provider (http://localhost:8080/node_modules/.vite/deps/chunk-MHLXIYJU.js?v=4d7ffbec:51:15)
    at TooltipProvider (http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=4d7ffbec:87:5)
    at QueryClientProvider (http://localhost:8080/node_modules/.vite/deps/@tanstack_react-query.js?v=4d7ffbec:2933:3)
    at ThemeProvider (http://localhost:8080/src/hooks/use-theme.tsx:20:33)
    at App
console.error @ index.tsx:86
printWarning @ react-dom.development.js:86
error @ react-dom.development.js:60
validateShorthandPropertyCollisionInDev @ react-dom.development.js:2899
diffProperties @ react-dom.development.js:10123
prepareUpdate @ react-dom.development.js:10977
updateHostComponent$1 @ react-dom.development.js:21825
completeWork @ react-dom.development.js:22194
completeUnitOfWork @ react-dom.development.js:26635
performUnitOfWork @ react-dom.development.js:26607
workLoopSync @ react-dom.development.js:26505
renderRootSync @ react-dom.development.js:26473
performSyncWorkOnRoot @ react-dom.development.js:26124
flushSyncCallbacks @ react-dom.development.js:12042
(anônimo) @ react-dom.development.js:25690
ModelViewer.tsx:92 Iframe loaded with model: https://sketchfab.com/models/5a7af325a7db4e1a91c01a8f93c18598/embed
ModelViewer.tsx:99 Usando tempo de transição de 2650ms (mobile)
ModelViewer.tsx:105 Transition ended after 1750481344192ms (model loaded)
use-cases.tsx:88 Model loaded successfully: 3 Pontes de 3 elementos (superior e inferior) 
  [Violation]Permissions policy violation: xr-spatial-tracking is not allowed in this document.
pollHeadset @ f45fb3a…-v2.js:2
initialize @ dcd90f2…-v2.js:1
<computed> @ fad4b81…-v2.js:1
t.View @ a71993b…-v2.js:2
constructor @ fad4b81…-v2.js:1
constructor @ fad4b81…-v2.js:1
V.r @ a71993b…-v2.js:2
i @ dcd90f2…-v2.js:1
(anônimo) @ dcd90f2…-v2.js:1
init @ dcd90f2…-v2.js:1
addFeature @ dcd90f2…-v2.js:1
(anônimo) @ dcd90f2…-v2.js:1
run @ dcd90f2…-v2.js:1
onModelLoad @ daa012a…-v2.js:1
l @ b840b92…-v2.js:2
c @ b840b92…-v2.js:2
a @ a71993b…-v2.js:2
Dialog.tsx:540  Warning: Missing `Description` or `aria-describedby={undefined}` for {DialogContent}.
(anônimo) @ Dialog.tsx:540
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
commitRootImpl @ react-dom.development.js:26974
commitRoot @ react-dom.development.js:26721
performSyncWorkOnRoot @ react-dom.development.js:26156
flushSyncCallbacks @ react-dom.development.js:12042
(anônimo) @ react-dom.development.js:25690
:8080/html/file-1749277750476-985363894.html:1  An iframe which has both allow-scripts and allow-same-origin for its sandbox attribute can escape its sandboxing.
  asmCrypto seems to be load from an insecure origin; this may cause to MitM-attack vulnerability. Consider using secure transport protocol.
(anônimo) @ :8080/html/file-1749…6-985363894.html:35
(anônimo) @ :8080/html/file-1749…6-985363894.html:35
 THREE.WebGLRenderer 107
index.tsx:86  Warning: Updating a style property during rerender (animationDelay) when a conflicting property is set (animation) can lead to styling bugs. To avoid this, don't mix shorthand and non-shorthand properties for the same value; instead, replace the shorthand with separate values.
    at div
    at div
    at div
    at div
    at div
    at IntroSection (http://localhost:8080/src/components/cases/IntroSection.tsx:14:25)
    at div
    at Cases (http://localhost:8080/src/pages/Cases.tsx:31:195)
    at RenderedRoute (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4069:5)
    at Routes (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4508:5)
    at Router (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4451:15)
    at BrowserRouter (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:5196:5)
    at Provider (http://localhost:8080/node_modules/.vite/deps/chunk-MHLXIYJU.js?v=4d7ffbec:51:15)
    at TooltipProvider (http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=4d7ffbec:87:5)
    at QueryClientProvider (http://localhost:8080/node_modules/.vite/deps/@tanstack_react-query.js?v=4d7ffbec:2933:3)
    at ThemeProvider (http://localhost:8080/src/hooks/use-theme.tsx:20:33)
    at App
console.error @ index.tsx:86
index.tsx:86  Warning: Updating a style property during rerender (animationDelay) when a conflicting property is set (animation) can lead to styling bugs. To avoid this, don't mix shorthand and non-shorthand properties for the same value; instead, replace the shorthand with separate values.
    at div
    at div
    at div
    at div
    at div
    at IntroSection (http://localhost:8080/src/components/cases/IntroSection.tsx:14:25)
    at div
    at Cases (http://localhost:8080/src/pages/Cases.tsx:31:195)
    at RenderedRoute (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4069:5)
    at Routes (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4508:5)
    at Router (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4451:15)
    at BrowserRouter (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:5196:5)
    at Provider (http://localhost:8080/node_modules/.vite/deps/chunk-MHLXIYJU.js?v=4d7ffbec:51:15)
    at TooltipProvider (http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=4d7ffbec:87:5)
    at QueryClientProvider (http://localhost:8080/node_modules/.vite/deps/@tanstack_react-query.js?v=4d7ffbec:2933:3)
    at ThemeProvider (http://localhost:8080/src/hooks/use-theme.tsx:20:33)
    at App
console.error @ index.tsx:86
index.tsx:86  Warning: Updating a style property during rerender (animationDelay) when a conflicting property is set (animation) can lead to styling bugs. To avoid this, don't mix shorthand and non-shorthand properties for the same value; instead, replace the shorthand with separate values.
    at div
    at div
    at div
    at div
    at div
    at IntroSection (http://localhost:8080/src/components/cases/IntroSection.tsx:14:25)
    at div
    at Cases (http://localhost:8080/src/pages/Cases.tsx:31:195)
    at RenderedRoute (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4069:5)
    at Routes (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4508:5)
    at Router (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4451:15)
    at BrowserRouter (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:5196:5)
    at Provider (http://localhost:8080/node_modules/.vite/deps/chunk-MHLXIYJU.js?v=4d7ffbec:51:15)
    at TooltipProvider (http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=4d7ffbec:87:5)
    at QueryClientProvider (http://localhost:8080/node_modules/.vite/deps/@tanstack_react-query.js?v=4d7ffbec:2933:3)
    at ThemeProvider (http://localhost:8080/src/hooks/use-theme.tsx:20:33)
    at App
console.error @ index.tsx:86
index.tsx:86  Warning: Updating a style property during rerender (animationDelay) when a conflicting property is set (animation) can lead to styling bugs. To avoid this, don't mix shorthand and non-shorthand properties for the same value; instead, replace the shorthand with separate values.
    at div
    at div
    at div
    at div
    at div
    at IntroSection (http://localhost:8080/src/components/cases/IntroSection.tsx:14:25)
    at div
    at Cases (http://localhost:8080/src/pages/Cases.tsx:31:195)
    at RenderedRoute (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4069:5)
    at Routes (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4508:5)
    at Router (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4451:15)
    at BrowserRouter (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:5196:5)
    at Provider (http://localhost:8080/node_modules/.vite/deps/chunk-MHLXIYJU.js?v=4d7ffbec:51:15)
    at TooltipProvider (http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=4d7ffbec:87:5)
    at QueryClientProvider (http://localhost:8080/node_modules/.vite/deps/@tanstack_react-query.js?v=4d7ffbec:2933:3)
    at ThemeProvider (http://localhost:8080/src/hooks/use-theme.tsx:20:33)
    at App
console.error @ index.tsx:86
index.tsx:86  Warning: Updating a style property during rerender (animationDelay) when a conflicting property is set (animation) can lead to styling bugs. To avoid this, don't mix shorthand and non-shorthand properties for the same value; instead, replace the shorthand with separate values.
    at div
    at div
    at div
    at div
    at div
    at IntroSection (http://localhost:8080/src/components/cases/IntroSection.tsx:14:25)
    at div
    at Cases (http://localhost:8080/src/pages/Cases.tsx:31:195)
    at RenderedRoute (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4069:5)
    at Routes (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4508:5)
    at Router (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4451:15)
    at BrowserRouter (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:5196:5)
    at Provider (http://localhost:8080/node_modules/.vite/deps/chunk-MHLXIYJU.js?v=4d7ffbec:51:15)
    at TooltipProvider (http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=4d7ffbec:87:5)
    at QueryClientProvider (http://localhost:8080/node_modules/.vite/deps/@tanstack_react-query.js?v=4d7ffbec:2933:3)
    at ThemeProvider (http://localhost:8080/src/hooks/use-theme.tsx:20:33)
    at App
console.error @ index.tsx:86
index.tsx:86  Warning: Updating a style property during rerender (animationDelay) when a conflicting property is set (animation) can lead to styling bugs. To avoid this, don't mix shorthand and non-shorthand properties for the same value; instead, replace the shorthand with separate values.
    at div
    at div
    at div
    at div
    at div
    at IntroSection (http://localhost:8080/src/components/cases/IntroSection.tsx:14:25)
    at div
    at Cases (http://localhost:8080/src/pages/Cases.tsx:31:195)
    at RenderedRoute (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4069:5)
    at Routes (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4508:5)
    at Router (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4451:15)
    at BrowserRouter (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:5196:5)
    at Provider (http://localhost:8080/node_modules/.vite/deps/chunk-MHLXIYJU.js?v=4d7ffbec:51:15)
    at TooltipProvider (http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=4d7ffbec:87:5)
    at QueryClientProvider (http://localhost:8080/node_modules/.vite/deps/@tanstack_react-query.js?v=4d7ffbec:2933:3)
    at ThemeProvider (http://localhost:8080/src/hooks/use-theme.tsx:20:33)
    at App
console.error @ index.tsx:86
index.tsx:86  Warning: Updating a style property during rerender (animationDelay) when a conflicting property is set (animation) can lead to styling bugs. To avoid this, don't mix shorthand and non-shorthand properties for the same value; instead, replace the shorthand with separate values.
    at div
    at div
    at div
    at div
    at div
    at IntroSection (http://localhost:8080/src/components/cases/IntroSection.tsx:14:25)
    at div
    at Cases (http://localhost:8080/src/pages/Cases.tsx:31:195)
    at RenderedRoute (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4069:5)
    at Routes (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4508:5)
    at Router (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4451:15)
    at BrowserRouter (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:5196:5)
    at Provider (http://localhost:8080/node_modules/.vite/deps/chunk-MHLXIYJU.js?v=4d7ffbec:51:15)
    at TooltipProvider (http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=4d7ffbec:87:5)
    at QueryClientProvider (http://localhost:8080/node_modules/.vite/deps/@tanstack_react-query.js?v=4d7ffbec:2933:3)
    at ThemeProvider (http://localhost:8080/src/hooks/use-theme.tsx:20:33)
    at App
console.error @ index.tsx:86
ModelViewer.tsx:92 Iframe loaded with model: https://sketchfab.com/models/5a7af325a7db4e1a91c01a8f93c18598/embed
ModelViewer.tsx:99 Usando tempo de transição de 2300ms (desktop)
ModelViewer.tsx:105 Transition ended after 1750481667960ms (model loaded)
use-cases.tsx:88 Model loaded successfully: 3 Pontes de 3 elementos (superior e inferior) 
f45fb3a3d002823dba31659e9462bdc3-v2.js:2  [Violation]Permissions policy violation: xr-spatial-tracking is not allowed in this document.
pollHeadset @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
index.tsx:86  Warning: Updating a style property during rerender (animationDelay) when a conflicting property is set (animation) can lead to styling bugs. To avoid this, don't mix shorthand and non-shorthand properties for the same value; instead, replace the shorthand with separate values.
    at div
    at div
    at div
    at div
    at div
    at IntroSection (http://localhost:8080/src/components/cases/IntroSection.tsx:14:25)
    at div
    at Cases (http://localhost:8080/src/pages/Cases.tsx:31:195)
    at RenderedRoute (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4069:5)
    at Routes (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4508:5)
    at Router (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4451:15)
    at BrowserRouter (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:5196:5)
    at Provider (http://localhost:8080/node_modules/.vite/deps/chunk-MHLXIYJU.js?v=4d7ffbec:51:15)
    at TooltipProvider (http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=4d7ffbec:87:5)
    at QueryClientProvider (http://localhost:8080/node_modules/.vite/deps/@tanstack_react-query.js?v=4d7ffbec:2933:3)
    at ThemeProvider (http://localhost:8080/src/hooks/use-theme.tsx:20:33)
    at App
console.error @ index.tsx:86
index.tsx:86  Warning: Updating a style property during rerender (animationDelay) when a conflicting property is set (animation) can lead to styling bugs. To avoid this, don't mix shorthand and non-shorthand properties for the same value; instead, replace the shorthand with separate values.
    at div
    at div
    at div
    at div
    at div
    at IntroSection (http://localhost:8080/src/components/cases/IntroSection.tsx:14:25)
    at div
    at Cases (http://localhost:8080/src/pages/Cases.tsx:31:195)
    at RenderedRoute (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4069:5)
    at Routes (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4508:5)
    at Router (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4451:15)
    at BrowserRouter (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:5196:5)
    at Provider (http://localhost:8080/node_modules/.vite/deps/chunk-MHLXIYJU.js?v=4d7ffbec:51:15)
    at TooltipProvider (http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=4d7ffbec:87:5)
    at QueryClientProvider (http://localhost:8080/node_modules/.vite/deps/@tanstack_react-query.js?v=4d7ffbec:2933:3)
    at ThemeProvider (http://localhost:8080/src/hooks/use-theme.tsx:20:33)
    at App
console.error @ index.tsx:86
index.tsx:86  Warning: Updating a style property during rerender (animationDelay) when a conflicting property is set (animation) can lead to styling bugs. To avoid this, don't mix shorthand and non-shorthand properties for the same value; instead, replace the shorthand with separate values.
    at div
    at div
    at div
    at div
    at div
    at IntroSection (http://localhost:8080/src/components/cases/IntroSection.tsx:14:25)
    at div
    at Cases (http://localhost:8080/src/pages/Cases.tsx:31:195)
    at RenderedRoute (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4069:5)
    at Routes (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4508:5)
    at Router (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4451:15)
    at BrowserRouter (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:5196:5)
    at Provider (http://localhost:8080/node_modules/.vite/deps/chunk-MHLXIYJU.js?v=4d7ffbec:51:15)
    at TooltipProvider (http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=4d7ffbec:87:5)
    at QueryClientProvider (http://localhost:8080/node_modules/.vite/deps/@tanstack_react-query.js?v=4d7ffbec:2933:3)
    at ThemeProvider (http://localhost:8080/src/hooks/use-theme.tsx:20:33)
    at App
console.error @ index.tsx:86
index.tsx:86  Warning: Updating a style property during rerender (animationDelay) when a conflicting property is set (animation) can lead to styling bugs. To avoid this, don't mix shorthand and non-shorthand properties for the same value; instead, replace the shorthand with separate values.
    at div
    at div
    at div
    at div
    at div
    at IntroSection (http://localhost:8080/src/components/cases/IntroSection.tsx:14:25)
    at div
    at Cases (http://localhost:8080/src/pages/Cases.tsx:31:195)
    at RenderedRoute (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4069:5)
    at Routes (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4508:5)
    at Router (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4451:15)
    at BrowserRouter (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:5196:5)
    at Provider (http://localhost:8080/node_modules/.vite/deps/chunk-MHLXIYJU.js?v=4d7ffbec:51:15)
    at TooltipProvider (http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=4d7ffbec:87:5)
    at QueryClientProvider (http://localhost:8080/node_modules/.vite/deps/@tanstack_react-query.js?v=4d7ffbec:2933:3)
    at ThemeProvider (http://localhost:8080/src/hooks/use-theme.tsx:20:33)
    at App
console.error @ index.tsx:86
index.tsx:86  Warning: Updating a style property during rerender (animationDelay) when a conflicting property is set (animation) can lead to styling bugs. To avoid this, don't mix shorthand and non-shorthand properties for the same value; instead, replace the shorthand with separate values.
    at div
    at div
    at div
    at div
    at div
    at IntroSection (http://localhost:8080/src/components/cases/IntroSection.tsx:14:25)
    at div
    at Cases (http://localhost:8080/src/pages/Cases.tsx:31:195)
    at RenderedRoute (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4069:5)
    at Routes (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4508:5)
    at Router (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4451:15)
    at BrowserRouter (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:5196:5)
    at Provider (http://localhost:8080/node_modules/.vite/deps/chunk-MHLXIYJU.js?v=4d7ffbec:51:15)
    at TooltipProvider (http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=4d7ffbec:87:5)
    at QueryClientProvider (http://localhost:8080/node_modules/.vite/deps/@tanstack_react-query.js?v=4d7ffbec:2933:3)
    at ThemeProvider (http://localhost:8080/src/hooks/use-theme.tsx:20:33)
    at App
console.error @ index.tsx:86
index.tsx:86  Warning: Updating a style property during rerender (animationDelay) when a conflicting property is set (animation) can lead to styling bugs. To avoid this, don't mix shorthand and non-shorthand properties for the same value; instead, replace the shorthand with separate values.
    at div
    at div
    at div
    at div
    at div
    at IntroSection (http://localhost:8080/src/components/cases/IntroSection.tsx:14:25)
    at div
    at Cases (http://localhost:8080/src/pages/Cases.tsx:31:195)
    at RenderedRoute (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4069:5)
    at Routes (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4508:5)
    at Router (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4451:15)
    at BrowserRouter (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:5196:5)
    at Provider (http://localhost:8080/node_modules/.vite/deps/chunk-MHLXIYJU.js?v=4d7ffbec:51:15)
    at TooltipProvider (http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=4d7ffbec:87:5)
    at QueryClientProvider (http://localhost:8080/node_modules/.vite/deps/@tanstack_react-query.js?v=4d7ffbec:2933:3)
    at ThemeProvider (http://localhost:8080/src/hooks/use-theme.tsx:20:33)
    at App
console.error @ index.tsx:86
index.tsx:86  Warning: Updating a style property during rerender (animationDelay) when a conflicting property is set (animation) can lead to styling bugs. To avoid this, don't mix shorthand and non-shorthand properties for the same value; instead, replace the shorthand with separate values.
    at div
    at div
    at div
    at div
    at div
    at IntroSection (http://localhost:8080/src/components/cases/IntroSection.tsx:14:25)
    at div
    at Cases (http://localhost:8080/src/pages/Cases.tsx:31:195)
    at RenderedRoute (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4069:5)
    at Routes (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4508:5)
    at Router (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:4451:15)
    at BrowserRouter (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=4d7ffbec:5196:5)
    at Provider (http://localhost:8080/node_modules/.vite/deps/chunk-MHLXIYJU.js?v=4d7ffbec:51:15)
    at TooltipProvider (http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=4d7ffbec:87:5)
    at QueryClientProvider (http://localhost:8080/node_modules/.vite/deps/@tanstack_react-query.js?v=4d7ffbec:2933:3)
    at ThemeProvider (http://localhost:8080/src/hooks/use-theme.tsx:20:33)
    at App
console.error @ index.tsx:86
ModelViewer.tsx:92 Iframe loaded with model: https://sketchfab.com/models/5a7af325a7db4e1a91c01a8f93c18598/embed
ModelViewer.tsx:99 Usando tempo de transição de 2300ms (desktop)
ModelViewer.tsx:105 Transition ended after 1750481671708ms (model loaded)
use-cases.tsx:88 Model loaded successfully: 3 Pontes de 3 elementos (superior e inferior) 
f45fb3a3d002823dba31659e9462bdc3-v2.js:2  [Violation]Permissions policy violation: xr-spatial-tracking is not allowed in this document.
pollHeadset @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
