client:495 [vite] connecting...
client:614 [vite] connected.
react-dom.development.js:29895 Download the React DevTools for a better development experience: https://reactjs.org/link/react-devtools
ModelViewer.tsx:135 State update: {initialLoad: true, firstModelLoaded: false, isTransitioning: false, customTransition: false, modelLoading: true, …}
use-analytics.ts:193 📊 Analytics Event: {type: 'page_view', action: 'view', data: {…}}
index.tsx:86  Warning: Updating a style property during rerender (animationDelay) when a conflicting property is set (animation) can lead to styling bugs. To avoid this, don't mix shorthand and non-shorthand properties for the same value; instead, replace the shorthand with separate values.
    at div
    at div
    at div
    at div
    at div
    at IntroSection (http://localhost:8080/src/components/cases/IntroSection.tsx:14:25)
    at div
    at Cases (http://localhost:8080/src/pages/Cases.tsx:31:195)
    at RenderedRoute (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4069:5)
    at Routes (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4508:5)
    at Router (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4451:15)
    at BrowserRouter (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:5196:5)
    at Provider (http://localhost:8080/node_modules/.vite/deps/chunk-MHLXIYJU.js?v=2449da8e:51:15)
    at TooltipProvider (http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=2449da8e:87:5)
    at QueryClientProvider (http://localhost:8080/node_modules/.vite/deps/@tanstack_react-query.js?v=2449da8e:2933:3)
    at ThemeProvider (http://localhost:8080/src/hooks/use-theme.tsx:20:33)
    at App
console.error @ index.tsx:86
printWarning @ react-dom.development.js:86
error @ react-dom.development.js:60
validateShorthandPropertyCollisionInDev @ react-dom.development.js:2899
diffProperties @ react-dom.development.js:10123
prepareUpdate @ react-dom.development.js:10977
updateHostComponent$1 @ react-dom.development.js:21825
completeWork @ react-dom.development.js:22194
completeUnitOfWork @ react-dom.development.js:26635
performUnitOfWork @ react-dom.development.js:26607
workLoopSync @ react-dom.development.js:26505
renderRootSync @ react-dom.development.js:26473
performConcurrentWorkOnRoot @ react-dom.development.js:25777
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
index.tsx:86  Warning: Updating a style property during rerender (animationDelay) when a conflicting property is set (animation) can lead to styling bugs. To avoid this, don't mix shorthand and non-shorthand properties for the same value; instead, replace the shorthand with separate values.
    at div
    at div
    at div
    at div
    at div
    at IntroSection (http://localhost:8080/src/components/cases/IntroSection.tsx:14:25)
    at div
    at Cases (http://localhost:8080/src/pages/Cases.tsx:31:195)
    at RenderedRoute (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4069:5)
    at Routes (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4508:5)
    at Router (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4451:15)
    at BrowserRouter (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:5196:5)
    at Provider (http://localhost:8080/node_modules/.vite/deps/chunk-MHLXIYJU.js?v=2449da8e:51:15)
    at TooltipProvider (http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=2449da8e:87:5)
    at QueryClientProvider (http://localhost:8080/node_modules/.vite/deps/@tanstack_react-query.js?v=2449da8e:2933:3)
    at ThemeProvider (http://localhost:8080/src/hooks/use-theme.tsx:20:33)
    at App
console.error @ index.tsx:86
printWarning @ react-dom.development.js:86
error @ react-dom.development.js:60
validateShorthandPropertyCollisionInDev @ react-dom.development.js:2899
diffProperties @ react-dom.development.js:10123
prepareUpdate @ react-dom.development.js:10977
updateHostComponent$1 @ react-dom.development.js:21825
completeWork @ react-dom.development.js:22194
completeUnitOfWork @ react-dom.development.js:26635
performUnitOfWork @ react-dom.development.js:26607
workLoopSync @ react-dom.development.js:26505
renderRootSync @ react-dom.development.js:26473
performConcurrentWorkOnRoot @ react-dom.development.js:25777
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
index.tsx:86  Warning: Updating a style property during rerender (animationDelay) when a conflicting property is set (animation) can lead to styling bugs. To avoid this, don't mix shorthand and non-shorthand properties for the same value; instead, replace the shorthand with separate values.
    at div
    at div
    at div
    at div
    at div
    at IntroSection (http://localhost:8080/src/components/cases/IntroSection.tsx:14:25)
    at div
    at Cases (http://localhost:8080/src/pages/Cases.tsx:31:195)
    at RenderedRoute (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4069:5)
    at Routes (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4508:5)
    at Router (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4451:15)
    at BrowserRouter (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:5196:5)
    at Provider (http://localhost:8080/node_modules/.vite/deps/chunk-MHLXIYJU.js?v=2449da8e:51:15)
    at TooltipProvider (http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=2449da8e:87:5)
    at QueryClientProvider (http://localhost:8080/node_modules/.vite/deps/@tanstack_react-query.js?v=2449da8e:2933:3)
    at ThemeProvider (http://localhost:8080/src/hooks/use-theme.tsx:20:33)
    at App
console.error @ index.tsx:86
printWarning @ react-dom.development.js:86
error @ react-dom.development.js:60
validateShorthandPropertyCollisionInDev @ react-dom.development.js:2899
diffProperties @ react-dom.development.js:10123
prepareUpdate @ react-dom.development.js:10977
updateHostComponent$1 @ react-dom.development.js:21825
completeWork @ react-dom.development.js:22194
completeUnitOfWork @ react-dom.development.js:26635
performUnitOfWork @ react-dom.development.js:26607
workLoopSync @ react-dom.development.js:26505
renderRootSync @ react-dom.development.js:26473
performConcurrentWorkOnRoot @ react-dom.development.js:25777
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
index.tsx:86  Warning: Updating a style property during rerender (animationDelay) when a conflicting property is set (animation) can lead to styling bugs. To avoid this, don't mix shorthand and non-shorthand properties for the same value; instead, replace the shorthand with separate values.
    at div
    at div
    at div
    at div
    at div
    at IntroSection (http://localhost:8080/src/components/cases/IntroSection.tsx:14:25)
    at div
    at Cases (http://localhost:8080/src/pages/Cases.tsx:31:195)
    at RenderedRoute (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4069:5)
    at Routes (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4508:5)
    at Router (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4451:15)
    at BrowserRouter (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:5196:5)
    at Provider (http://localhost:8080/node_modules/.vite/deps/chunk-MHLXIYJU.js?v=2449da8e:51:15)
    at TooltipProvider (http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=2449da8e:87:5)
    at QueryClientProvider (http://localhost:8080/node_modules/.vite/deps/@tanstack_react-query.js?v=2449da8e:2933:3)
    at ThemeProvider (http://localhost:8080/src/hooks/use-theme.tsx:20:33)
    at App
console.error @ index.tsx:86
printWarning @ react-dom.development.js:86
error @ react-dom.development.js:60
validateShorthandPropertyCollisionInDev @ react-dom.development.js:2899
diffProperties @ react-dom.development.js:10123
prepareUpdate @ react-dom.development.js:10977
updateHostComponent$1 @ react-dom.development.js:21825
completeWork @ react-dom.development.js:22194
completeUnitOfWork @ react-dom.development.js:26635
performUnitOfWork @ react-dom.development.js:26607
workLoopSync @ react-dom.development.js:26505
renderRootSync @ react-dom.development.js:26473
performConcurrentWorkOnRoot @ react-dom.development.js:25777
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
index.tsx:86  Warning: Updating a style property during rerender (animationDelay) when a conflicting property is set (animation) can lead to styling bugs. To avoid this, don't mix shorthand and non-shorthand properties for the same value; instead, replace the shorthand with separate values.
    at div
    at div
    at div
    at div
    at div
    at IntroSection (http://localhost:8080/src/components/cases/IntroSection.tsx:14:25)
    at div
    at Cases (http://localhost:8080/src/pages/Cases.tsx:31:195)
    at RenderedRoute (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4069:5)
    at Routes (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4508:5)
    at Router (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4451:15)
    at BrowserRouter (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:5196:5)
    at Provider (http://localhost:8080/node_modules/.vite/deps/chunk-MHLXIYJU.js?v=2449da8e:51:15)
    at TooltipProvider (http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=2449da8e:87:5)
    at QueryClientProvider (http://localhost:8080/node_modules/.vite/deps/@tanstack_react-query.js?v=2449da8e:2933:3)
    at ThemeProvider (http://localhost:8080/src/hooks/use-theme.tsx:20:33)
    at App
console.error @ index.tsx:86
printWarning @ react-dom.development.js:86
error @ react-dom.development.js:60
validateShorthandPropertyCollisionInDev @ react-dom.development.js:2899
diffProperties @ react-dom.development.js:10123
prepareUpdate @ react-dom.development.js:10977
updateHostComponent$1 @ react-dom.development.js:21825
completeWork @ react-dom.development.js:22194
completeUnitOfWork @ react-dom.development.js:26635
performUnitOfWork @ react-dom.development.js:26607
workLoopSync @ react-dom.development.js:26505
renderRootSync @ react-dom.development.js:26473
performConcurrentWorkOnRoot @ react-dom.development.js:25777
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
index.tsx:86  Warning: Updating a style property during rerender (animationDelay) when a conflicting property is set (animation) can lead to styling bugs. To avoid this, don't mix shorthand and non-shorthand properties for the same value; instead, replace the shorthand with separate values.
    at div
    at div
    at div
    at div
    at div
    at IntroSection (http://localhost:8080/src/components/cases/IntroSection.tsx:14:25)
    at div
    at Cases (http://localhost:8080/src/pages/Cases.tsx:31:195)
    at RenderedRoute (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4069:5)
    at Routes (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4508:5)
    at Router (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4451:15)
    at BrowserRouter (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:5196:5)
    at Provider (http://localhost:8080/node_modules/.vite/deps/chunk-MHLXIYJU.js?v=2449da8e:51:15)
    at TooltipProvider (http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=2449da8e:87:5)
    at QueryClientProvider (http://localhost:8080/node_modules/.vite/deps/@tanstack_react-query.js?v=2449da8e:2933:3)
    at ThemeProvider (http://localhost:8080/src/hooks/use-theme.tsx:20:33)
    at App
console.error @ index.tsx:86
printWarning @ react-dom.development.js:86
error @ react-dom.development.js:60
validateShorthandPropertyCollisionInDev @ react-dom.development.js:2899
diffProperties @ react-dom.development.js:10123
prepareUpdate @ react-dom.development.js:10977
updateHostComponent$1 @ react-dom.development.js:21825
completeWork @ react-dom.development.js:22194
completeUnitOfWork @ react-dom.development.js:26635
performUnitOfWork @ react-dom.development.js:26607
workLoopSync @ react-dom.development.js:26505
renderRootSync @ react-dom.development.js:26473
performConcurrentWorkOnRoot @ react-dom.development.js:25777
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
index.tsx:86  Warning: Updating a style property during rerender (animationDelay) when a conflicting property is set (animation) can lead to styling bugs. To avoid this, don't mix shorthand and non-shorthand properties for the same value; instead, replace the shorthand with separate values.
    at div
    at div
    at div
    at div
    at div
    at IntroSection (http://localhost:8080/src/components/cases/IntroSection.tsx:14:25)
    at div
    at Cases (http://localhost:8080/src/pages/Cases.tsx:31:195)
    at RenderedRoute (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4069:5)
    at Routes (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4508:5)
    at Router (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4451:15)
    at BrowserRouter (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:5196:5)
    at Provider (http://localhost:8080/node_modules/.vite/deps/chunk-MHLXIYJU.js?v=2449da8e:51:15)
    at TooltipProvider (http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=2449da8e:87:5)
    at QueryClientProvider (http://localhost:8080/node_modules/.vite/deps/@tanstack_react-query.js?v=2449da8e:2933:3)
    at ThemeProvider (http://localhost:8080/src/hooks/use-theme.tsx:20:33)
    at App
console.error @ index.tsx:86
printWarning @ react-dom.development.js:86
error @ react-dom.development.js:60
validateShorthandPropertyCollisionInDev @ react-dom.development.js:2899
diffProperties @ react-dom.development.js:10123
prepareUpdate @ react-dom.development.js:10977
updateHostComponent$1 @ react-dom.development.js:21825
completeWork @ react-dom.development.js:22194
completeUnitOfWork @ react-dom.development.js:26635
performUnitOfWork @ react-dom.development.js:26607
workLoopSync @ react-dom.development.js:26505
renderRootSync @ react-dom.development.js:26473
performConcurrentWorkOnRoot @ react-dom.development.js:25777
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ModelViewer.tsx:92 Iframe loaded with model: https://sketchfab.com/models/5a7af325a7db4e1a91c01a8f93c18598/embed
ModelViewer.tsx:99 Usando tempo de transição de 2650ms (mobile)
ModelViewer.tsx:105 Transition ended after 1750487633313ms (model loaded)
use-cases.tsx:88 Model loaded successfully: 3 Pontes de 3 elementos (superior e inferior) 
ModelViewer.tsx:35 First model loaded successfully
ModelViewer.tsx:135 State update: {initialLoad: true, firstModelLoaded: false, isTransitioning: false, customTransition: false, modelLoading: false, …}
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
ModelViewer.tsx:135 State update: {initialLoad: true, firstModelLoaded: true, isTransitioning: false, customTransition: false, modelLoading: false, …}
a71993b83e4fc29775e91744c89b50ad-v2.js:2 [Violation]'setTimeout' handler took 60ms
a71993b83e4fc29775e91744c89b50ad-v2.js:2  [Violation]Permissions policy violation: accelerometer is not allowed in this document.
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
e @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setEnable @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
_addEvent @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
addMappings @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
addMappings @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
init @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
wi @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
(anônimo) @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
init @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
Ui @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
setupManipulator @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
initialize @ dcd90f2609921bc1c172e143bc388605-v2.js:1
<computed> @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
t.View @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
V.r @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
createScene @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
onModelLoad @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
add @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
Deferred @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
then @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
sync @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
fetch @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
get @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
load @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
start @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
l @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromiseFromHandler @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromise @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromiseCtx @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
c @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
a @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s._drainQueues @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
drainQueues @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
attributes
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
attributes
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s._queueTick @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s.invoke @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._then @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O.then @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
initializeViewer @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 6f0757f2090c0b8b379aaa8d498f3538-v2.js:1
initialize @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
<computed> @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
t.View @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
V.r @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
add @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
Deferred @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
then @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
sync @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
fetch @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
get @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
c @ 451d38d26ff6171c541e4f63c1418836-v2.js:1
Pu2i @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
r @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
r.O @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
a @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
a71993b83e4fc29775e91744c89b50ad-v2.js:2  The deviceorientation events are blocked by permissions policy. See https://go.microsoft.com/fwlink/?linkid=2047894
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
e @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setEnable @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
_addEvent @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
addMappings @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
addMappings @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
init @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
wi @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
(anônimo) @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
init @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
Ui @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
setupManipulator @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
initialize @ dcd90f2609921bc1c172e143bc388605-v2.js:1
<computed> @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
t.View @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
V.r @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
createScene @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
onModelLoad @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
add @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
Deferred @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
then @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
sync @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
fetch @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
get @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
load @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
start @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
l @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromiseFromHandler @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromise @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromiseCtx @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
c @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
a @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s._drainQueues @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
drainQueues @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
attributes
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
attributes
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s._queueTick @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s.invoke @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._then @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O.then @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
initializeViewer @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 6f0757f2090c0b8b379aaa8d498f3538-v2.js:1
initialize @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
<computed> @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
t.View @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
V.r @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
add @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
Deferred @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
then @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
sync @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
fetch @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
get @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
c @ 451d38d26ff6171c541e4f63c1418836-v2.js:1
Pu2i @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
r @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
r.O @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
a @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
a71993b83e4fc29775e91744c89b50ad-v2.js:2  [Violation]Permissions policy violation: accelerometer is not allowed in this document.
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
e @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setEnable @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
_addEvent @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
addMappings @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
addMappings @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
init @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
bi @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
(anônimo) @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
init @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
Mi @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
se @ dcd90f2609921bc1c172e143bc388605-v2.js:1
init @ dcd90f2609921bc1c172e143bc388605-v2.js:1
we @ dcd90f2609921bc1c172e143bc388605-v2.js:1
initialize @ dcd90f2609921bc1c172e143bc388605-v2.js:1
<computed> @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
t.View @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
V.r @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
i @ dcd90f2609921bc1c172e143bc388605-v2.js:1
(anônimo) @ dcd90f2609921bc1c172e143bc388605-v2.js:1
init @ dcd90f2609921bc1c172e143bc388605-v2.js:1
addFeature @ dcd90f2609921bc1c172e143bc388605-v2.js:1
(anônimo) @ dcd90f2609921bc1c172e143bc388605-v2.js:1
run @ dcd90f2609921bc1c172e143bc388605-v2.js:1
onModelLoad @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
add @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
Deferred @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
then @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
sync @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
fetch @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
get @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
load @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
start @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
l @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromiseFromHandler @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromise @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromiseCtx @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
c @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
a @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s._drainQueues @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
drainQueues @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
attributes
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
attributes
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s._queueTick @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s.invoke @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._then @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O.then @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
initializeViewer @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 6f0757f2090c0b8b379aaa8d498f3538-v2.js:1
initialize @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
<computed> @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
t.View @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
V.r @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
add @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
Deferred @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
then @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
sync @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
fetch @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
get @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
c @ 451d38d26ff6171c541e4f63c1418836-v2.js:1
Pu2i @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
r @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
r.O @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
a @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
a71993b83e4fc29775e91744c89b50ad-v2.js:2  The deviceorientation events are blocked by permissions policy. See https://go.microsoft.com/fwlink/?linkid=2047894
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
e @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setEnable @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
_addEvent @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
addMappings @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
addMappings @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
init @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
bi @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
(anônimo) @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
init @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
Mi @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
se @ dcd90f2609921bc1c172e143bc388605-v2.js:1
init @ dcd90f2609921bc1c172e143bc388605-v2.js:1
we @ dcd90f2609921bc1c172e143bc388605-v2.js:1
initialize @ dcd90f2609921bc1c172e143bc388605-v2.js:1
<computed> @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
t.View @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
V.r @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
i @ dcd90f2609921bc1c172e143bc388605-v2.js:1
(anônimo) @ dcd90f2609921bc1c172e143bc388605-v2.js:1
init @ dcd90f2609921bc1c172e143bc388605-v2.js:1
addFeature @ dcd90f2609921bc1c172e143bc388605-v2.js:1
(anônimo) @ dcd90f2609921bc1c172e143bc388605-v2.js:1
run @ dcd90f2609921bc1c172e143bc388605-v2.js:1
onModelLoad @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
add @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
Deferred @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
then @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
sync @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
fetch @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
get @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
load @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
start @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
l @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromiseFromHandler @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromise @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromiseCtx @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
c @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
a @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s._drainQueues @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
drainQueues @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
attributes
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
attributes
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s._queueTick @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s.invoke @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._then @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O.then @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
initializeViewer @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 6f0757f2090c0b8b379aaa8d498f3538-v2.js:1
initialize @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
<computed> @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
t.View @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
V.r @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
add @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
Deferred @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
then @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
sync @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
fetch @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
get @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
c @ 451d38d26ff6171c541e4f63c1418836-v2.js:1
Pu2i @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
r @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
r.O @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
a @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
f45fb3a3d002823dba31659e9462bdc3-v2.js:2  [Violation]Permissions policy violation: xr-spatial-tracking is not allowed in this document.
pollHeadset @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
initialize @ dcd90f2609921bc1c172e143bc388605-v2.js:1
<computed> @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
t.View @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
V.r @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
i @ dcd90f2609921bc1c172e143bc388605-v2.js:1
(anônimo) @ dcd90f2609921bc1c172e143bc388605-v2.js:1
init @ dcd90f2609921bc1c172e143bc388605-v2.js:1
addFeature @ dcd90f2609921bc1c172e143bc388605-v2.js:1
(anônimo) @ dcd90f2609921bc1c172e143bc388605-v2.js:1
run @ dcd90f2609921bc1c172e143bc388605-v2.js:1
onModelLoad @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
add @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
Deferred @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
then @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
sync @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
fetch @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
get @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
load @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
start @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
l @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromiseFromHandler @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromise @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromiseCtx @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
c @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
a @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s._drainQueues @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
drainQueues @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
attributes
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
attributes
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s._queueTick @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s.invoke @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._then @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O.then @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
initializeViewer @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 6f0757f2090c0b8b379aaa8d498f3538-v2.js:1
initialize @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
<computed> @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
t.View @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
V.r @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
add @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
Deferred @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
then @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
sync @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
fetch @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
get @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
c @ 451d38d26ff6171c541e4f63c1418836-v2.js:1
Pu2i @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
r @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
r.O @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
a @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
a71993b83e4fc29775e91744c89b50ad-v2.js:2 [Violation]'setTimeout' handler took 297ms
a71993b83e4fc29775e91744c89b50ad-v2.js:2 
            
            
            GET https://sketchfab.com/i/archives/ar?model=5a7af325a7db4e1a91c01a8f93c18598&platform=android 400 (Bad Request)
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ ea5acfbf4885d3379e59431c49e6de7d-v2.js:2
(anônimo) @ acb50531ce52f476bf5a705fd10ae005-v2.js:2
N @ acb50531ce52f476bf5a705fd10ae005-v2.js:2
t.exports @ ea5acfbf4885d3379e59431c49e6de7d-v2.js:2
t.exports @ ea5acfbf4885d3379e59431c49e6de7d-v2.js:2
(anônimo) @ acb50531ce52f476bf5a705fd10ae005-v2.js:2
V @ acb50531ce52f476bf5a705fd10ae005-v2.js:2
(anônimo) @ acb50531ce52f476bf5a705fd10ae005-v2.js:2
v @ acb50531ce52f476bf5a705fd10ae005-v2.js:2
characterData
m @ acb50531ce52f476bf5a705fd10ae005-v2.js:2
s @ acb50531ce52f476bf5a705fd10ae005-v2.js:2
w @ acb50531ce52f476bf5a705fd10ae005-v2.js:2
o @ 1b68f7de13e5ed49025fef6b352a03d9-v2.js:2
c @ 1b68f7de13e5ed49025fef6b352a03d9-v2.js:2
(anônimo) @ 1b68f7de13e5ed49025fef6b352a03d9-v2.js:2
(anônimo) @ acb50531ce52f476bf5a705fd10ae005-v2.js:2
N @ acb50531ce52f476bf5a705fd10ae005-v2.js:2
(anônimo) @ 1b68f7de13e5ed49025fef6b352a03d9-v2.js:2
(anônimo) @ 3fadd3c767178b389af41bb68d372921-v2.js:1
viewModel @ 3fadd3c767178b389af41bb68d372921-v2.js:1
hit @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
onModelLoad @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
add @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
Deferred @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
then @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
sync @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
fetch @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
get @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
load @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
start @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
l @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromiseFromHandler @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromise @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromiseCtx @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
c @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
a @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s._drainQueues @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
drainQueues @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
attributes
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
attributes
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s._queueTick @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s.invoke @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._then @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O.then @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
initializeViewer @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 6f0757f2090c0b8b379aaa8d498f3538-v2.js:1
initialize @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
<computed> @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
t.View @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
V.r @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
add @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
Deferred @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
then @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
sync @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
fetch @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
get @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
c @ 451d38d26ff6171c541e4f63c1418836-v2.js:1
Pu2i @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
r @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
r.O @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
a @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2 [Violation]'message' handler took 323ms
ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2 [Violation]'load' handler took 950ms
ModelViewer.tsx:43 Transitioning from intro to model view - no reload needed
ModelViewer.tsx:135 State update: {initialLoad: true, firstModelLoaded: true, isTransitioning: false, customTransition: false, modelLoading: false, …}
ModelViewer.tsx:135 State update: {initialLoad: false, firstModelLoaded: true, isTransitioning: false, customTransition: false, modelLoading: false, …}
react-dom.development.js:6878  Unable to preventDefault inside passive event listener invocation.
preventDefault @ react-dom.development.js:6878
handleTouchStart @ ThumbnailCarousel.tsx:91
callCallback2 @ react-dom.development.js:4164
invokeGuardedCallbackDev @ react-dom.development.js:4213
invokeGuardedCallback @ react-dom.development.js:4277
invokeGuardedCallbackAndCatchFirstError @ react-dom.development.js:4291
executeDispatch @ react-dom.development.js:9041
processDispatchQueueItemsInOrder @ react-dom.development.js:9073
processDispatchQueue @ react-dom.development.js:9086
dispatchEventsForPlugins @ react-dom.development.js:9097
(anônimo) @ react-dom.development.js:9288
batchedUpdates$1 @ react-dom.development.js:26179
batchedUpdates @ react-dom.development.js:3991
dispatchEventForPluginEventSystem @ react-dom.development.js:9287
dispatchEventWithEnableCapturePhaseSelectiveHydrationWithoutDiscreteEventReplay @ react-dom.development.js:6465
dispatchEvent @ react-dom.development.js:6457
dispatchDiscreteEvent @ react-dom.development.js:6430
ModelViewer.tsx:63 Transitioning to new model: 
ModelViewer.tsx:135 State update: {initialLoad: false, firstModelLoaded: true, isTransitioning: true, customTransition: true, modelLoading: true, …}
client:495 [vite] connecting...
client:614 [vite] connected.
ModelViewer.tsx:92 Iframe loaded with model: 
ModelViewer.tsx:99 Usando tempo de transição de 2650ms (mobile)
ModelViewer.tsx:110 Model loaded, waiting 1625ms more for minimum transition time
ModelViewer.tsx:135 State update: {initialLoad: true, firstModelLoaded: false, isTransitioning: false, customTransition: false, modelLoading: true, …}
use-analytics.ts:193 📊 Analytics Event: {type: 'page_view', action: 'view', data: {…}}
scheduler.development.js:517 [Violation]'message' handler took 179ms
index.tsx:86  Warning: Updating a style property during rerender (animationDelay) when a conflicting property is set (animation) can lead to styling bugs. To avoid this, don't mix shorthand and non-shorthand properties for the same value; instead, replace the shorthand with separate values.
    at div
    at div
    at div
    at div
    at div
    at IntroSection (http://localhost:8080/src/components/cases/IntroSection.tsx:14:25)
    at div
    at Cases (http://localhost:8080/src/pages/Cases.tsx:31:195)
    at RenderedRoute (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4069:5)
    at Routes (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4508:5)
    at Router (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4451:15)
    at BrowserRouter (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:5196:5)
    at Provider (http://localhost:8080/node_modules/.vite/deps/chunk-MHLXIYJU.js?v=2449da8e:51:15)
    at TooltipProvider (http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=2449da8e:87:5)
    at QueryClientProvider (http://localhost:8080/node_modules/.vite/deps/@tanstack_react-query.js?v=2449da8e:2933:3)
    at ThemeProvider (http://localhost:8080/src/hooks/use-theme.tsx:20:33)
    at App
console.error @ index.tsx:86
printWarning @ react-dom.development.js:86
error @ react-dom.development.js:60
validateShorthandPropertyCollisionInDev @ react-dom.development.js:2899
diffProperties @ react-dom.development.js:10123
prepareUpdate @ react-dom.development.js:10977
updateHostComponent$1 @ react-dom.development.js:21825
completeWork @ react-dom.development.js:22194
completeUnitOfWork @ react-dom.development.js:26635
performUnitOfWork @ react-dom.development.js:26607
workLoopSync @ react-dom.development.js:26505
renderRootSync @ react-dom.development.js:26473
performConcurrentWorkOnRoot @ react-dom.development.js:25777
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
index.tsx:86  Warning: Updating a style property during rerender (animationDelay) when a conflicting property is set (animation) can lead to styling bugs. To avoid this, don't mix shorthand and non-shorthand properties for the same value; instead, replace the shorthand with separate values.
    at div
    at div
    at div
    at div
    at div
    at IntroSection (http://localhost:8080/src/components/cases/IntroSection.tsx:14:25)
    at div
    at Cases (http://localhost:8080/src/pages/Cases.tsx:31:195)
    at RenderedRoute (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4069:5)
    at Routes (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4508:5)
    at Router (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4451:15)
    at BrowserRouter (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:5196:5)
    at Provider (http://localhost:8080/node_modules/.vite/deps/chunk-MHLXIYJU.js?v=2449da8e:51:15)
    at TooltipProvider (http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=2449da8e:87:5)
    at QueryClientProvider (http://localhost:8080/node_modules/.vite/deps/@tanstack_react-query.js?v=2449da8e:2933:3)
    at ThemeProvider (http://localhost:8080/src/hooks/use-theme.tsx:20:33)
    at App
console.error @ index.tsx:86
printWarning @ react-dom.development.js:86
error @ react-dom.development.js:60
validateShorthandPropertyCollisionInDev @ react-dom.development.js:2899
diffProperties @ react-dom.development.js:10123
prepareUpdate @ react-dom.development.js:10977
updateHostComponent$1 @ react-dom.development.js:21825
completeWork @ react-dom.development.js:22194
completeUnitOfWork @ react-dom.development.js:26635
performUnitOfWork @ react-dom.development.js:26607
workLoopSync @ react-dom.development.js:26505
renderRootSync @ react-dom.development.js:26473
performConcurrentWorkOnRoot @ react-dom.development.js:25777
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
index.tsx:86  Warning: Updating a style property during rerender (animationDelay) when a conflicting property is set (animation) can lead to styling bugs. To avoid this, don't mix shorthand and non-shorthand properties for the same value; instead, replace the shorthand with separate values.
    at div
    at div
    at div
    at div
    at div
    at IntroSection (http://localhost:8080/src/components/cases/IntroSection.tsx:14:25)
    at div
    at Cases (http://localhost:8080/src/pages/Cases.tsx:31:195)
    at RenderedRoute (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4069:5)
    at Routes (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4508:5)
    at Router (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4451:15)
    at BrowserRouter (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:5196:5)
    at Provider (http://localhost:8080/node_modules/.vite/deps/chunk-MHLXIYJU.js?v=2449da8e:51:15)
    at TooltipProvider (http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=2449da8e:87:5)
    at QueryClientProvider (http://localhost:8080/node_modules/.vite/deps/@tanstack_react-query.js?v=2449da8e:2933:3)
    at ThemeProvider (http://localhost:8080/src/hooks/use-theme.tsx:20:33)
    at App
console.error @ index.tsx:86
printWarning @ react-dom.development.js:86
error @ react-dom.development.js:60
validateShorthandPropertyCollisionInDev @ react-dom.development.js:2899
diffProperties @ react-dom.development.js:10123
prepareUpdate @ react-dom.development.js:10977
updateHostComponent$1 @ react-dom.development.js:21825
completeWork @ react-dom.development.js:22194
completeUnitOfWork @ react-dom.development.js:26635
performUnitOfWork @ react-dom.development.js:26607
workLoopSync @ react-dom.development.js:26505
renderRootSync @ react-dom.development.js:26473
performConcurrentWorkOnRoot @ react-dom.development.js:25777
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
index.tsx:86  Warning: Updating a style property during rerender (animationDelay) when a conflicting property is set (animation) can lead to styling bugs. To avoid this, don't mix shorthand and non-shorthand properties for the same value; instead, replace the shorthand with separate values.
    at div
    at div
    at div
    at div
    at div
    at IntroSection (http://localhost:8080/src/components/cases/IntroSection.tsx:14:25)
    at div
    at Cases (http://localhost:8080/src/pages/Cases.tsx:31:195)
    at RenderedRoute (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4069:5)
    at Routes (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4508:5)
    at Router (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4451:15)
    at BrowserRouter (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:5196:5)
    at Provider (http://localhost:8080/node_modules/.vite/deps/chunk-MHLXIYJU.js?v=2449da8e:51:15)
    at TooltipProvider (http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=2449da8e:87:5)
    at QueryClientProvider (http://localhost:8080/node_modules/.vite/deps/@tanstack_react-query.js?v=2449da8e:2933:3)
    at ThemeProvider (http://localhost:8080/src/hooks/use-theme.tsx:20:33)
    at App
console.error @ index.tsx:86
printWarning @ react-dom.development.js:86
error @ react-dom.development.js:60
validateShorthandPropertyCollisionInDev @ react-dom.development.js:2899
diffProperties @ react-dom.development.js:10123
prepareUpdate @ react-dom.development.js:10977
updateHostComponent$1 @ react-dom.development.js:21825
completeWork @ react-dom.development.js:22194
completeUnitOfWork @ react-dom.development.js:26635
performUnitOfWork @ react-dom.development.js:26607
workLoopSync @ react-dom.development.js:26505
renderRootSync @ react-dom.development.js:26473
performConcurrentWorkOnRoot @ react-dom.development.js:25777
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
index.tsx:86  Warning: Updating a style property during rerender (animationDelay) when a conflicting property is set (animation) can lead to styling bugs. To avoid this, don't mix shorthand and non-shorthand properties for the same value; instead, replace the shorthand with separate values.
    at div
    at div
    at div
    at div
    at div
    at IntroSection (http://localhost:8080/src/components/cases/IntroSection.tsx:14:25)
    at div
    at Cases (http://localhost:8080/src/pages/Cases.tsx:31:195)
    at RenderedRoute (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4069:5)
    at Routes (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4508:5)
    at Router (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4451:15)
    at BrowserRouter (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:5196:5)
    at Provider (http://localhost:8080/node_modules/.vite/deps/chunk-MHLXIYJU.js?v=2449da8e:51:15)
    at TooltipProvider (http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=2449da8e:87:5)
    at QueryClientProvider (http://localhost:8080/node_modules/.vite/deps/@tanstack_react-query.js?v=2449da8e:2933:3)
    at ThemeProvider (http://localhost:8080/src/hooks/use-theme.tsx:20:33)
    at App
console.error @ index.tsx:86
printWarning @ react-dom.development.js:86
error @ react-dom.development.js:60
validateShorthandPropertyCollisionInDev @ react-dom.development.js:2899
diffProperties @ react-dom.development.js:10123
prepareUpdate @ react-dom.development.js:10977
updateHostComponent$1 @ react-dom.development.js:21825
completeWork @ react-dom.development.js:22194
completeUnitOfWork @ react-dom.development.js:26635
performUnitOfWork @ react-dom.development.js:26607
workLoopSync @ react-dom.development.js:26505
renderRootSync @ react-dom.development.js:26473
performConcurrentWorkOnRoot @ react-dom.development.js:25777
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
index.tsx:86  Warning: Updating a style property during rerender (animationDelay) when a conflicting property is set (animation) can lead to styling bugs. To avoid this, don't mix shorthand and non-shorthand properties for the same value; instead, replace the shorthand with separate values.
    at div
    at div
    at div
    at div
    at div
    at IntroSection (http://localhost:8080/src/components/cases/IntroSection.tsx:14:25)
    at div
    at Cases (http://localhost:8080/src/pages/Cases.tsx:31:195)
    at RenderedRoute (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4069:5)
    at Routes (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4508:5)
    at Router (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4451:15)
    at BrowserRouter (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:5196:5)
    at Provider (http://localhost:8080/node_modules/.vite/deps/chunk-MHLXIYJU.js?v=2449da8e:51:15)
    at TooltipProvider (http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=2449da8e:87:5)
    at QueryClientProvider (http://localhost:8080/node_modules/.vite/deps/@tanstack_react-query.js?v=2449da8e:2933:3)
    at ThemeProvider (http://localhost:8080/src/hooks/use-theme.tsx:20:33)
    at App
console.error @ index.tsx:86
printWarning @ react-dom.development.js:86
error @ react-dom.development.js:60
validateShorthandPropertyCollisionInDev @ react-dom.development.js:2899
diffProperties @ react-dom.development.js:10123
prepareUpdate @ react-dom.development.js:10977
updateHostComponent$1 @ react-dom.development.js:21825
completeWork @ react-dom.development.js:22194
completeUnitOfWork @ react-dom.development.js:26635
performUnitOfWork @ react-dom.development.js:26607
workLoopSync @ react-dom.development.js:26505
renderRootSync @ react-dom.development.js:26473
performConcurrentWorkOnRoot @ react-dom.development.js:25777
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
index.tsx:86  Warning: Updating a style property during rerender (animationDelay) when a conflicting property is set (animation) can lead to styling bugs. To avoid this, don't mix shorthand and non-shorthand properties for the same value; instead, replace the shorthand with separate values.
    at div
    at div
    at div
    at div
    at div
    at IntroSection (http://localhost:8080/src/components/cases/IntroSection.tsx:14:25)
    at div
    at Cases (http://localhost:8080/src/pages/Cases.tsx:31:195)
    at RenderedRoute (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4069:5)
    at Routes (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4508:5)
    at Router (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4451:15)
    at BrowserRouter (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:5196:5)
    at Provider (http://localhost:8080/node_modules/.vite/deps/chunk-MHLXIYJU.js?v=2449da8e:51:15)
    at TooltipProvider (http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=2449da8e:87:5)
    at QueryClientProvider (http://localhost:8080/node_modules/.vite/deps/@tanstack_react-query.js?v=2449da8e:2933:3)
    at ThemeProvider (http://localhost:8080/src/hooks/use-theme.tsx:20:33)
    at App
console.error @ index.tsx:86
printWarning @ react-dom.development.js:86
error @ react-dom.development.js:60
validateShorthandPropertyCollisionInDev @ react-dom.development.js:2899
diffProperties @ react-dom.development.js:10123
prepareUpdate @ react-dom.development.js:10977
updateHostComponent$1 @ react-dom.development.js:21825
completeWork @ react-dom.development.js:22194
completeUnitOfWork @ react-dom.development.js:26635
performUnitOfWork @ react-dom.development.js:26607
workLoopSync @ react-dom.development.js:26505
renderRootSync @ react-dom.development.js:26473
performConcurrentWorkOnRoot @ react-dom.development.js:25777
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
a71993b83e4fc29775e91744c89b50ad-v2.js:2 [Violation]'setTimeout' handler took 64ms
ModelViewer.tsx:92 Iframe loaded with model: https://sketchfab.com/models/5a7af325a7db4e1a91c01a8f93c18598/embed
ModelViewer.tsx:99 Usando tempo de transição de 2650ms (mobile)
ModelViewer.tsx:105 Transition ended after 1750487659538ms (model loaded)
use-cases.tsx:88 Model loaded successfully: 3 Pontes de 3 elementos (superior e inferior) 
ModelViewer.tsx:35 First model loaded successfully
ModelViewer.tsx:135 State update: {initialLoad: true, firstModelLoaded: false, isTransitioning: false, customTransition: false, modelLoading: false, …}
ModelViewer.tsx:135 State update: {initialLoad: true, firstModelLoaded: true, isTransitioning: false, customTransition: false, modelLoading: false, …}
a71993b83e4fc29775e91744c89b50ad-v2.js:2  [Violation]Permissions policy violation: accelerometer is not allowed in this document.
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
e @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setEnable @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
_addEvent @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
addMappings @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
addMappings @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
init @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
wi @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
(anônimo) @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
init @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
Ui @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
setupManipulator @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
initialize @ dcd90f2609921bc1c172e143bc388605-v2.js:1
<computed> @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
t.View @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
V.r @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
createScene @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
onModelLoad @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
add @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
Deferred @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
then @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
sync @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
fetch @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
get @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
load @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
start @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
l @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromiseFromHandler @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromise @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromiseCtx @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
c @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
a @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s._drainQueues @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
drainQueues @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
attributes
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
attributes
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s._queueTick @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s.invoke @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._then @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O.then @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
initializeViewer @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 6f0757f2090c0b8b379aaa8d498f3538-v2.js:1
initialize @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
<computed> @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
t.View @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
V.r @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
add @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
Deferred @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
then @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
sync @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
fetch @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
get @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
c @ 451d38d26ff6171c541e4f63c1418836-v2.js:1
Pu2i @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
r @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
r.O @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
a @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
a71993b83e4fc29775e91744c89b50ad-v2.js:2  The deviceorientation events are blocked by permissions policy. See https://go.microsoft.com/fwlink/?linkid=2047894
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
e @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setEnable @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
_addEvent @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
addMappings @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
addMappings @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
init @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
wi @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
(anônimo) @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
init @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
Ui @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
setupManipulator @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
initialize @ dcd90f2609921bc1c172e143bc388605-v2.js:1
<computed> @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
t.View @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
V.r @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
createScene @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
onModelLoad @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
add @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
Deferred @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
then @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
sync @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
fetch @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
get @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
load @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
start @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
l @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromiseFromHandler @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromise @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromiseCtx @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
c @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
a @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s._drainQueues @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
drainQueues @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
attributes
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
attributes
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s._queueTick @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s.invoke @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._then @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O.then @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
initializeViewer @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 6f0757f2090c0b8b379aaa8d498f3538-v2.js:1
initialize @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
<computed> @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
t.View @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
V.r @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
add @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
Deferred @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
then @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
sync @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
fetch @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
get @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
c @ 451d38d26ff6171c541e4f63c1418836-v2.js:1
Pu2i @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
r @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
r.O @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
a @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
a71993b83e4fc29775e91744c89b50ad-v2.js:2  [Violation]Permissions policy violation: accelerometer is not allowed in this document.
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
e @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setEnable @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
_addEvent @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
addMappings @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
addMappings @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
init @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
bi @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
(anônimo) @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
init @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
Mi @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
se @ dcd90f2609921bc1c172e143bc388605-v2.js:1
init @ dcd90f2609921bc1c172e143bc388605-v2.js:1
we @ dcd90f2609921bc1c172e143bc388605-v2.js:1
initialize @ dcd90f2609921bc1c172e143bc388605-v2.js:1
<computed> @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
t.View @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
V.r @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
i @ dcd90f2609921bc1c172e143bc388605-v2.js:1
(anônimo) @ dcd90f2609921bc1c172e143bc388605-v2.js:1
init @ dcd90f2609921bc1c172e143bc388605-v2.js:1
addFeature @ dcd90f2609921bc1c172e143bc388605-v2.js:1
(anônimo) @ dcd90f2609921bc1c172e143bc388605-v2.js:1
run @ dcd90f2609921bc1c172e143bc388605-v2.js:1
onModelLoad @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
add @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
Deferred @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
then @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
sync @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
fetch @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
get @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
load @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
start @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
l @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromiseFromHandler @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromise @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromiseCtx @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
c @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
a @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s._drainQueues @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
drainQueues @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
attributes
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
attributes
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s._queueTick @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s.invoke @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._then @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O.then @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
initializeViewer @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 6f0757f2090c0b8b379aaa8d498f3538-v2.js:1
initialize @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
<computed> @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
t.View @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
V.r @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
add @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
Deferred @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
then @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
sync @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
fetch @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
get @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
c @ 451d38d26ff6171c541e4f63c1418836-v2.js:1
Pu2i @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
r @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
r.O @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
a @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
a71993b83e4fc29775e91744c89b50ad-v2.js:2  The deviceorientation events are blocked by permissions policy. See https://go.microsoft.com/fwlink/?linkid=2047894
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
e @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setEnable @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
_addEvent @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
addMappings @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
addMappings @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
init @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
bi @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
(anônimo) @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
init @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
Mi @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
se @ dcd90f2609921bc1c172e143bc388605-v2.js:1
init @ dcd90f2609921bc1c172e143bc388605-v2.js:1
we @ dcd90f2609921bc1c172e143bc388605-v2.js:1
initialize @ dcd90f2609921bc1c172e143bc388605-v2.js:1
<computed> @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
t.View @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
V.r @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
i @ dcd90f2609921bc1c172e143bc388605-v2.js:1
(anônimo) @ dcd90f2609921bc1c172e143bc388605-v2.js:1
init @ dcd90f2609921bc1c172e143bc388605-v2.js:1
addFeature @ dcd90f2609921bc1c172e143bc388605-v2.js:1
(anônimo) @ dcd90f2609921bc1c172e143bc388605-v2.js:1
run @ dcd90f2609921bc1c172e143bc388605-v2.js:1
onModelLoad @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
add @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
Deferred @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
then @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
sync @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
fetch @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
get @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
load @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
start @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
l @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromiseFromHandler @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromise @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromiseCtx @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
c @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
a @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s._drainQueues @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
drainQueues @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
attributes
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
attributes
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s._queueTick @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s.invoke @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._then @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O.then @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
initializeViewer @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 6f0757f2090c0b8b379aaa8d498f3538-v2.js:1
initialize @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
<computed> @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
t.View @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
V.r @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
add @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
Deferred @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
then @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
sync @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
fetch @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
get @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
c @ 451d38d26ff6171c541e4f63c1418836-v2.js:1
Pu2i @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
r @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
r.O @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
a @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
f45fb3a3d002823dba31659e9462bdc3-v2.js:2  [Violation]Permissions policy violation: xr-spatial-tracking is not allowed in this document.
pollHeadset @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
initialize @ dcd90f2609921bc1c172e143bc388605-v2.js:1
<computed> @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
t.View @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
V.r @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
i @ dcd90f2609921bc1c172e143bc388605-v2.js:1
(anônimo) @ dcd90f2609921bc1c172e143bc388605-v2.js:1
init @ dcd90f2609921bc1c172e143bc388605-v2.js:1
addFeature @ dcd90f2609921bc1c172e143bc388605-v2.js:1
(anônimo) @ dcd90f2609921bc1c172e143bc388605-v2.js:1
run @ dcd90f2609921bc1c172e143bc388605-v2.js:1
onModelLoad @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
add @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
Deferred @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
then @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
sync @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
fetch @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
get @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
load @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
start @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
l @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromiseFromHandler @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromise @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromiseCtx @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
c @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
a @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s._drainQueues @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
drainQueues @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
attributes
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
attributes
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s._queueTick @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s.invoke @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._then @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O.then @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
initializeViewer @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 6f0757f2090c0b8b379aaa8d498f3538-v2.js:1
initialize @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
<computed> @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
t.View @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
V.r @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
add @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
Deferred @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
then @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
sync @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
fetch @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
get @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
c @ 451d38d26ff6171c541e4f63c1418836-v2.js:1
Pu2i @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
r @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
r.O @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
a @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
a71993b83e4fc29775e91744c89b50ad-v2.js:2 [Violation]'setTimeout' handler took 232ms
ModelViewer.tsx:115 Transition ended after 2650ms (minimum time reached)
use-cases.tsx:88 Model loaded successfully: RESTAURADO - DENTE 36 
ModelViewer.tsx:135 State update: {initialLoad: false, firstModelLoaded: true, isTransitioning: false, customTransition: false, modelLoading: false, …}
a71993b83e4fc29775e91744c89b50ad-v2.js:2 
            
            
            GET https://sketchfab.com/i/archives/ar?model=5a7af325a7db4e1a91c01a8f93c18598&platform=android 400 (Bad Request)
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ ea5acfbf4885d3379e59431c49e6de7d-v2.js:2
(anônimo) @ acb50531ce52f476bf5a705fd10ae005-v2.js:2
N @ acb50531ce52f476bf5a705fd10ae005-v2.js:2
t.exports @ ea5acfbf4885d3379e59431c49e6de7d-v2.js:2
t.exports @ ea5acfbf4885d3379e59431c49e6de7d-v2.js:2
(anônimo) @ acb50531ce52f476bf5a705fd10ae005-v2.js:2
V @ acb50531ce52f476bf5a705fd10ae005-v2.js:2
(anônimo) @ acb50531ce52f476bf5a705fd10ae005-v2.js:2
v @ acb50531ce52f476bf5a705fd10ae005-v2.js:2
characterData
m @ acb50531ce52f476bf5a705fd10ae005-v2.js:2
s @ acb50531ce52f476bf5a705fd10ae005-v2.js:2
w @ acb50531ce52f476bf5a705fd10ae005-v2.js:2
o @ 1b68f7de13e5ed49025fef6b352a03d9-v2.js:2
c @ 1b68f7de13e5ed49025fef6b352a03d9-v2.js:2
(anônimo) @ 1b68f7de13e5ed49025fef6b352a03d9-v2.js:2
(anônimo) @ acb50531ce52f476bf5a705fd10ae005-v2.js:2
N @ acb50531ce52f476bf5a705fd10ae005-v2.js:2
(anônimo) @ 1b68f7de13e5ed49025fef6b352a03d9-v2.js:2
(anônimo) @ 3fadd3c767178b389af41bb68d372921-v2.js:1
viewModel @ 3fadd3c767178b389af41bb68d372921-v2.js:1
hit @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
onModelLoad @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
add @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
Deferred @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
then @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
sync @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
fetch @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
get @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
load @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
start @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
l @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromiseFromHandler @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromise @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromiseCtx @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
c @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
a @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s._drainQueues @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
drainQueues @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
attributes
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
attributes
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s._queueTick @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s.invoke @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._then @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O.then @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
initializeViewer @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 6f0757f2090c0b8b379aaa8d498f3538-v2.js:1
initialize @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
<computed> @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
t.View @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
V.r @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
add @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
Deferred @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
then @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
sync @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
fetch @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
get @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
c @ 451d38d26ff6171c541e4f63c1418836-v2.js:1
Pu2i @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
r @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
r.O @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
a @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2 [Violation]'message' handler took 356ms
ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2 [Violation]'load' handler took 1167ms
ModelViewer.tsx:43 Transitioning from intro to model view - no reload needed
ModelViewer.tsx:135 State update: {initialLoad: true, firstModelLoaded: true, isTransitioning: false, customTransition: false, modelLoading: false, …}
ModelViewer.tsx:135 State update: {initialLoad: false, firstModelLoaded: true, isTransitioning: false, customTransition: false, modelLoading: false, …}
