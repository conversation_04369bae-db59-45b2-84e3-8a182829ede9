client:495 [vite] connecting...
client:614 [vite] connected.
react-dom.development.js:29895 Download the React DevTools for a better development experience: https://reactjs.org/link/react-devtools
ModelViewer.tsx:135 State update: Object
use-analytics.ts:193 📊 Analytics Event: Object
ModelViewer.tsx:92 Iframe loaded with model: https://sketchfab.com/models/5a7af325a7db4e1a91c01a8f93c18598/embed
ModelViewer.tsx:99 Usando tempo de transição de 2300ms (desktop)
ModelViewer.tsx:105 Transition ended after 1750483163839ms (model loaded)
use-cases.tsx:88 Model loaded successfully: 3 Pontes de 3 elementos (superior e inferior) 
ModelViewer.tsx:35 First model loaded successfully
ModelViewer.tsx:135 State update: Object
ModelViewer.tsx:135 State update: Object
a71993b83e4fc29775e91744c89b50ad-v2.js:2  [Violation]Permissions policy violation: accelerometer is not allowed in this document.
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
a71993b83e4fc29775e91744c89b50ad-v2.js:2  The deviceorientation events are blocked by permissions policy. See https://go.microsoft.com/fwlink/?linkid=2047894
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
a71993b83e4fc29775e91744c89b50ad-v2.js:2  [Violation]Permissions policy violation: accelerometer is not allowed in this document.
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
a71993b83e4fc29775e91744c89b50ad-v2.js:2  The deviceorientation events are blocked by permissions policy. See https://go.microsoft.com/fwlink/?linkid=2047894
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
f45fb3a3d002823dba31659e9462bdc3-v2.js:2  [Violation]Permissions policy violation: xr-spatial-tracking is not allowed in this document.
pollHeadset @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
ModelViewer.tsx:43 Transitioning from intro to model view - no reload needed
ModelViewer.tsx:135 State update: Object
ModelViewer.tsx:135 State update: Object
