client:495 [vite] connecting...
client:614 [vite] connected.
react-dom.development.js:29895 Download the React DevTools for a better development experience: https://reactjs.org/link/react-devtools
HybridModelViewer.tsx:256 HybridModelViewer Debug: Object
HybridModelViewer.tsx:293 Renderizando Sketchfab Viewer: https://sketchfab.com/models/5a7af325a7db4e1a91c01a8f93c18598/embed
ModelViewer.tsx:135 State update: Object
use-analytics.ts:193 📊 Analytics Event: Object
HybridModelViewer.tsx:256 HybridModelViewer Debug: Object
HybridModelViewer.tsx:281 Renderizando Native Viewer: /models/5a7af325a7db4e1a91c01a8f93c18598.glb
HybridModelViewer.tsx:256 HybridModelViewer Debug: Object
HybridModelViewer.tsx:281 Renderizando Native Viewer: /models/5a7af325a7db4e1a91c01a8f93c18598.glb
use-analytics.ts:193 📊 Analytics Event: Object
index.js:26  Uncaught Error: Could not load /models/5a7af325a7db4e1a91c01a8f93c18598.glb: Unexpected token '<', "
<!DOCTYPE "... is not valid JSON
    at index-e6b5343a.esm.js:1667:36
    at _onError (GLTFLoader.js:179:9)
    at Object.onLoad (GLTFLoader.js:210:11)
    at three.module.js:42369:38
use-analytics.ts:193 📊 Analytics Event: Object
index.js:26  Uncaught Error: Could not load /models/5a7af325a7db4e1a91c01a8f93c18598.glb: Unexpected token '<', "
<!DOCTYPE "... is not valid JSON
    at index-e6b5343a.esm.js:1667:36
    at _onError (GLTFLoader.js:179:9)
    at Object.onLoad (GLTFLoader.js:210:11)
    at three.module.js:42369:38
index.tsx:86  The above error occurred in the <Model> component:

    at Model (http://localhost:8080/src/components/3d/NativeModelViewer.tsx:19:18)
    at Suspense
    at Suspense
    at ErrorBoundary (http://localhost:8080/node_modules/.vite/deps/chunk-ELWJUCVG.js?v=2449da8e:16487:5)
    at FiberProvider (http://localhost:8080/node_modules/.vite/deps/chunk-ELWJUCVG.js?v=2449da8e:18071:21)
    at Provider (http://localhost:8080/node_modules/.vite/deps/chunk-ELWJUCVG.js?v=2449da8e:17758:3)

React will try to recreate this component tree from scratch using the error boundary you provided, ErrorBoundary.
console.error @ index.tsx:86
HybridModelViewer.tsx:256 HybridModelViewer Debug: Object
HybridModelViewer.tsx:281 Renderizando Native Viewer: /models/5a7af325a7db4e1a91c01a8f93c18598.glb
index.tsx:86  Warning: Cannot update a component (`Cases`) while rendering a different component (`ForwardRef(Canvas)`). To locate the bad setState() call inside `ForwardRef(Canvas)`, follow the stack trace as described in https://reactjs.org/link/setstate-in-render
    at Canvas (http://localhost:8080/node_modules/.vite/deps/chunk-ELWJUCVG.js?v=2449da8e:18215:3)
    at FiberProvider (http://localhost:8080/node_modules/.vite/deps/chunk-ELWJUCVG.js?v=2449da8e:18071:21)
    at CanvasWrapper
    at div
    at NativeModelViewer (http://localhost:8080/src/components/3d/NativeModelViewer.tsx:116:30)
    at div
    at HybridModelViewer (http://localhost:8080/src/components/3d/HybridModelViewer.tsx:16:30)
    at ModelViewer (http://localhost:8080/src/components/cases/ModelViewer.tsx:16:24)
    at div
    at Cases (http://localhost:8080/src/pages/Cases.tsx:31:195)
    at RenderedRoute (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4069:5)
    at Routes (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4508:5)
    at Router (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4451:15)
    at BrowserRouter (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:5196:5)
    at Provider (http://localhost:8080/node_modules/.vite/deps/chunk-MHLXIYJU.js?v=2449da8e:51:15)
    at TooltipProvider (http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=2449da8e:87:5)
    at QueryClientProvider (http://localhost:8080/node_modules/.vite/deps/@tanstack_react-query.js?v=2449da8e:2933:3)
    at ThemeProvider (http://localhost:8080/src/hooks/use-theme.tsx:20:33)
    at App
console.error @ index.tsx:86
use-analytics.ts:193 📊 Analytics Event: Object
react-three-fiber.esm.js:145  Uncaught Error: Could not load /models/5a7af325a7db4e1a91c01a8f93c18598.glb: Unexpected token '<', "
<!DOCTYPE "... is not valid JSON
    at index-e6b5343a.esm.js:1667:36
    at _onError (GLTFLoader.js:179:9)
    at Object.onLoad (GLTFLoader.js:210:11)
    at three.module.js:42369:38
HybridModelViewer.tsx:256 HybridModelViewer Debug: Object
HybridModelViewer.tsx:281 Renderizando Native Viewer: /models/5a7af325a7db4e1a91c01a8f93c18598.glb
use-analytics.ts:193 📊 Analytics Event: Object
react-three-fiber.esm.js:145  Uncaught Error: Could not load /models/5a7af325a7db4e1a91c01a8f93c18598.glb: Unexpected token '<', "
<!DOCTYPE "... is not valid JSON
    at index-e6b5343a.esm.js:1667:36
    at _onError (GLTFLoader.js:179:9)
    at Object.onLoad (GLTFLoader.js:210:11)
    at three.module.js:42369:38
index.tsx:86  The above error occurred in the <ForwardRef(Canvas)> component:

    at Canvas (http://localhost:8080/node_modules/.vite/deps/chunk-ELWJUCVG.js?v=2449da8e:18215:3)
    at FiberProvider (http://localhost:8080/node_modules/.vite/deps/chunk-ELWJUCVG.js?v=2449da8e:18071:21)
    at CanvasWrapper
    at div
    at NativeModelViewer (http://localhost:8080/src/components/3d/NativeModelViewer.tsx:116:30)
    at div
    at HybridModelViewer (http://localhost:8080/src/components/3d/HybridModelViewer.tsx:16:30)
    at ModelViewer (http://localhost:8080/src/components/cases/ModelViewer.tsx:16:24)
    at div
    at Cases (http://localhost:8080/src/pages/Cases.tsx:31:195)
    at RenderedRoute (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4069:5)
    at Routes (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4508:5)
    at Router (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4451:15)
    at BrowserRouter (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:5196:5)
    at Provider (http://localhost:8080/node_modules/.vite/deps/chunk-MHLXIYJU.js?v=2449da8e:51:15)
    at TooltipProvider (http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=2449da8e:87:5)
    at QueryClientProvider (http://localhost:8080/node_modules/.vite/deps/@tanstack_react-query.js?v=2449da8e:2933:3)
    at ThemeProvider (http://localhost:8080/src/hooks/use-theme.tsx:20:33)
    at App

Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://reactjs.org/link/error-boundaries to learn more about error boundaries.
console.error @ index.tsx:86
use-analytics.ts:193 📊 Analytics Event: Object
react-dom.development.js:26962  Uncaught Error: Could not load /models/5a7af325a7db4e1a91c01a8f93c18598.glb: Unexpected token '<', "
<!DOCTYPE "... is not valid JSON
    at index-e6b5343a.esm.js:1667:36
    at _onError (GLTFLoader.js:179:9)
    at Object.onLoad (GLTFLoader.js:210:11)
    at three.module.js:42369:38
three.module.js:28857 THREE.WebGLRenderer: Context Lost.
