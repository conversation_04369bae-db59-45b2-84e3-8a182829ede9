
import React, { useRef, useEffect, useState } from 'react';
import { cn } from '@/lib/utils';
import { AnimatePresence, motion } from 'framer-motion';
import { useIsMobile } from '@/hooks/use-mobile';

interface ModelViewerProps {
  modelUrl: string;
  title: string;
  modelLoading: boolean;
  transitioning: boolean;
  onLoad: () => void;
  introVisible?: boolean; // Add prop to know if intro is visible
}

const ModelViewer = ({ modelUrl, title, modelLoading, transitioning, onLoad, introVisible = false }: ModelViewerProps) => {
  const isMobile = useIsMobile();
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [currentModel, setCurrentModel] = useState(modelUrl);
  const [nextModel, setNextModel] = useState('');
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [fadeDirection, setFadeDirection] = useState('out');
  const [initialLoad, setInitialLoad] = useState(true); // Track initial load state
  const [customTransition, setCustomTransition] = useState(false); // Track custom transition state
  const [firstModelLoaded, setFirstModelLoaded] = useState(false); // Track if first model is loaded
  const [transitionStartTime, setTransitionStartTime] = useState(0); // Track when transition started

  // Handle the initial model load
  useEffect(() => {
    // Mark the first model as loaded when it completes loading
    if (modelLoading === false && !firstModelLoaded) {
      setFirstModelLoaded(true);
      console.log("First model loaded successfully");
    }
  }, [modelLoading, firstModelLoaded]);

  // Special effect for intro to model transition
  useEffect(() => {
    // When intro disappears and first model is loaded, just reveal it without reloading
    if (!introVisible && initialLoad && firstModelLoaded) {
      console.log("Transitioning from intro to model view - no reload needed");
      setInitialLoad(false);
      // Just fade in the already loaded model without reloading it
      setFadeDirection('in');

      // After a short delay, clear any transitioning states
      const timer = setTimeout(() => {
        setIsTransitioning(false);
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [introVisible, initialLoad, firstModelLoaded]);

  // Simple case-to-case transition effect
  useEffect(() => {
    // Skip this effect during initial load or when intro is visible
    if (initialLoad || introVisible) return;

    if (modelUrl !== currentModel && !isTransitioning) {
      console.log(`Transitioning to new model: ${modelUrl}`);

      // Start transition effect
      setIsTransitioning(true);
      setCustomTransition(true);
      setTransitionStartTime(Date.now()); // Registrar o tempo de início da transição
      setCurrentModel(modelUrl); // Update model URL immediately

      // Não definimos um timer aqui - a transição continuará até que o modelo carregue
    }
  }, [modelUrl, currentModel, isTransitioning, initialLoad, introVisible]);

  // Force reset transitioning state if it gets stuck
  useEffect(() => {
    // If transitioning state is stuck for more than 10 seconds, reset it
    if (isTransitioning) {
      const resetTimer = setTimeout(() => {
        console.log("Force resetting transition state after timeout");
        setIsTransitioning(false);
        setCustomTransition(false);
      }, 10000); // Aumentado para 10 segundos para permitir carregamentos mais lentos

      return () => clearTimeout(resetTimer);
    }
  }, [isTransitioning]);

  // Handle iframe load event
  useEffect(() => {
    const handleIframeLoad = () => {
      console.log("Iframe loaded with model:", currentModel);

      // Calcular quanto tempo se passou desde o início da transição
      const currentTime = Date.now();
      const elapsedTime = currentTime - transitionStartTime;
      // Tempo mínimo de 2.3 segundos para desktop, 2.65 segundos para mobile
      const minTransitionTime = isMobile ? 2650 : 2300;
      console.log(`Usando tempo de transição de ${minTransitionTime}ms (${isMobile ? 'mobile' : 'desktop'})`);

      if (elapsedTime >= minTransitionTime) {
        // Se já passou o tempo mínimo, encerrar a transição imediatamente
        setIsTransitioning(false);
        setCustomTransition(false);
        console.log(`Transition ended after ${elapsedTime}ms (model loaded)`);
        onLoad();
      } else {
        // Se não passou o tempo mínimo, esperar o tempo restante
        const remainingTime = minTransitionTime - elapsedTime;
        console.log(`Model loaded, waiting ${remainingTime}ms more for minimum transition time`);

        setTimeout(() => {
          setIsTransitioning(false);
          setCustomTransition(false);
          console.log(`Transition ended after ${minTransitionTime}ms (minimum time reached)`);
          onLoad();
        }, remainingTime);
      }
    };

    const iframe = iframeRef.current;
    if (iframe) {
      iframe.addEventListener('load', handleIframeLoad);
    }

    return () => {
      if (iframe) {
        iframe.removeEventListener('load', handleIframeLoad);
      }
    };
  }, [onLoad, currentModel]);

  // Log state changes for debugging
  useEffect(() => {
    console.log("State update:", {
      initialLoad,
      firstModelLoaded,
      isTransitioning,
      customTransition,
      modelLoading,
      introVisible
    });
  }, [initialLoad, firstModelLoaded, isTransitioning, customTransition, modelLoading, introVisible]);

  return (
    <>
      {/* The iframe is always present but opacity changes based on state */}
      <div className="fixed inset-0 w-full h-full overflow-hidden pointer-events-auto">
        <iframe
          ref={iframeRef}
          title={`3D MODEL - ${title.toUpperCase()}`}
          src={`${currentModel}${currentModel.includes('?') ? '&' : '?'}autospin=1&autostart=1&ui_controls=0&ui_infos=0&ui_watermark=0&ui_stop=0&ui_inspector=0&ui_ar=0&ui_help=0&ui_settings=0&ui_vr=0&ui_fullscreen=0&ui_animations=0&transparent=1${isMobile ? '&zoom=0.5&camera_zoom=0.75' : ''}`}
          className={cn(
            "w-full h-full scale-125", // Increased scale for mobile to hide UI elements
            introVisible || customTransition ? "opacity-0" : "opacity-100 transition-opacity duration-500"
          )}
          frameBorder="0"
          allow="autoplay; fullscreen"
        />
      </div>

      {/* Enhanced transition overlay with logo - only covers the model area */}
      <AnimatePresence>
        {(customTransition) && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="fixed inset-0 z-10 pointer-events-none overflow-hidden"
            // Cobrindo toda a tela, mas com z-index baixo para ficar atrás dos outros elementos
          >
            {/* Semi-transparent background to allow other elements to be visible */}
            <div className="absolute inset-0 bg-black/80"></div>

            {/* Logo and loading animation container */}
            <div
              className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 flex flex-col items-center"
            >
              {/* Logo */}
              <div className="mb-8 text-center">
                <div className="text-xl font-medium tracking-tighter flex flex-col items-center">
                  <span className="font-semibold text-2xl mb-1 bg-gradient-to-r from-purple-400 to-blue-500 text-transparent bg-clip-text uppercase">FORM & FUNCTION</span>
                  <span className="text-sm font-light text-white/60 uppercase">/ JONHNATAS LIMA</span>
                </div>
              </div>

              {/* Loading animation */}
              <motion.div
                className="w-16 h-16 rounded-full"
                style={{
                  background: "linear-gradient(to right, rgba(168, 85, 247, 0.2), rgba(59, 130, 246, 0.2))",
                  borderWidth: "2px",
                  borderStyle: "solid",
                  borderImage: "linear-gradient(to right, rgba(168, 85, 247, 0.5), rgba(59, 130, 246, 0.5)) 1"
                }}
                initial={{ opacity: 0.5, scale: 0.8 }}
                animate={{
                  opacity: [0.5, 0.8, 0.5],
                  scale: [0.8, 1.2, 0.8],
                  rotate: [0, 180, 360]
                }}
                transition={{
                  duration: isMobile ? 2.65 : 2.3, // 2.3s para desktop, 2.65s para mobile
                  ease: "easeInOut",
                  times: [0, 0.5, 1],
                  repeat: Infinity // Animation continues until model is loaded
                }}
              />
            </div>
          </motion.div>
        )}
      </AnimatePresence>


    </>
  );
};

export default ModelViewer;
