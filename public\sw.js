// Service Worker para Cache Inteligente - <PERSON><PERSON><PERSON>lio <PERSON>tológico
// Versão: 1.0.0

const CACHE_NAME = 'dental-portfolio-v1';
const STATIC_CACHE = 'static-v1';
const DYNAMIC_CACHE = 'dynamic-v1';
const MODEL_CACHE = 'models-v1';
const IMAGE_CACHE = 'images-v1';

// Assets estáticos para cache
const STATIC_ASSETS = [
  '/',
  '/index.html',
  '/src/main.tsx',
  '/src/index.css',
  '/src/styles/exocad-viewer.css'
];

// Estratégias de cache
const CACHE_STRATEGIES = {
  CACHE_FIRST: 'cache-first',
  NETWORK_FIRST: 'network-first',
  STALE_WHILE_REVALIDATE: 'stale-while-revalidate',
  NETWORK_ONLY: 'network-only',
  CACHE_ONLY: 'cache-only'
};

// Configurações por tipo de recurso
const RESOURCE_CONFIG = {
  models: {
    strategy: CACHE_STRATEGIES.CACHE_FIRST,
    cacheName: MODEL_CACHE,
    maxAge: 7 * 24 * 60 * 60 * 1000, // 7 dias
    maxEntries: 50
  },
  images: {
    strategy: CACHE_STRATEGIES.STALE_WHILE_REVALIDATE,
    cacheName: IMAGE_CACHE,
    maxAge: 3 * 24 * 60 * 60 * 1000, // 3 dias
    maxEntries: 100
  },
  static: {
    strategy: CACHE_STRATEGIES.CACHE_FIRST,
    cacheName: STATIC_CACHE,
    maxAge: 30 * 24 * 60 * 60 * 1000, // 30 dias
    maxEntries: 200
  },
  api: {
    strategy: CACHE_STRATEGIES.NETWORK_FIRST,
    cacheName: DYNAMIC_CACHE,
    maxAge: 5 * 60 * 1000, // 5 minutos
    maxEntries: 50
  }
};

// Instalação do Service Worker
self.addEventListener('install', event => {
  console.log('[SW] Installing...');
  
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then(cache => {
        console.log('[SW] Caching static assets');
        return cache.addAll(STATIC_ASSETS);
      })
      .then(() => {
        console.log('[SW] Installation complete');
        return self.skipWaiting();
      })
      .catch(error => {
        console.error('[SW] Installation failed:', error);
      })
  );
});

// Ativação do Service Worker
self.addEventListener('activate', event => {
  console.log('[SW] Activating...');
  
  event.waitUntil(
    Promise.all([
      // Limpar caches antigos
      caches.keys().then(cacheNames => {
        return Promise.all(
          cacheNames.map(cacheName => {
            if (!Object.values(RESOURCE_CONFIG).some(config => config.cacheName === cacheName) &&
                cacheName !== CACHE_NAME) {
              console.log('[SW] Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      }),
      // Tomar controle de todas as abas
      self.clients.claim()
    ])
  );
});

// Interceptação de requisições
self.addEventListener('fetch', event => {
  const { request } = event;
  const url = new URL(request.url);
  
  // Ignorar requisições não-HTTP
  if (!request.url.startsWith('http')) return;
  
  // Determinar tipo de recurso e estratégia
  const resourceType = getResourceType(url);
  const config = RESOURCE_CONFIG[resourceType];
  
  if (!config) return;
  
  event.respondWith(
    handleRequest(request, config)
  );
});

// Determinar tipo de recurso
function getResourceType(url) {
  const pathname = url.pathname.toLowerCase();
  
  // Modelos 3D
  if (pathname.includes('/models/') || 
      pathname.endsWith('.glb') || 
      pathname.endsWith('.gltf') ||
      pathname.includes('sketchfab.com')) {
    return 'models';
  }
  
  // Imagens
  if (pathname.includes('/images/') ||
      pathname.match(/\.(jpg|jpeg|png|gif|webp|avif|svg)$/)) {
    return 'images';
  }
  
  // APIs
  if (pathname.includes('/api/')) {
    return 'api';
  }
  
  // Assets estáticos
  if (pathname.match(/\.(js|css|html|woff|woff2|ttf)$/) ||
      pathname === '/' ||
      pathname.includes('/src/')) {
    return 'static';
  }
  
  return 'static'; // Default
}

// Manipular requisições baseado na estratégia
async function handleRequest(request, config) {
  const { strategy, cacheName, maxAge } = config;
  
  switch (strategy) {
    case CACHE_STRATEGIES.CACHE_FIRST:
      return cacheFirst(request, cacheName, maxAge);
    
    case CACHE_STRATEGIES.NETWORK_FIRST:
      return networkFirst(request, cacheName, maxAge);
    
    case CACHE_STRATEGIES.STALE_WHILE_REVALIDATE:
      return staleWhileRevalidate(request, cacheName, maxAge);
    
    default:
      return fetch(request);
  }
}

// Estratégia: Cache First
async function cacheFirst(request, cacheName, maxAge) {
  const cache = await caches.open(cacheName);
  const cachedResponse = await cache.match(request);
  
  if (cachedResponse && !isExpired(cachedResponse, maxAge)) {
    return cachedResponse;
  }
  
  try {
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  } catch (error) {
    if (cachedResponse) {
      return cachedResponse; // Fallback para cache expirado
    }
    throw error;
  }
}

// Estratégia: Network First
async function networkFirst(request, cacheName, maxAge) {
  const cache = await caches.open(cacheName);
  
  try {
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  } catch (error) {
    const cachedResponse = await cache.match(request);
    if (cachedResponse && !isExpired(cachedResponse, maxAge)) {
      return cachedResponse;
    }
    throw error;
  }
}

// Estratégia: Stale While Revalidate
async function staleWhileRevalidate(request, cacheName, maxAge) {
  const cache = await caches.open(cacheName);
  const cachedResponse = await cache.match(request);
  
  // Buscar nova versão em background
  const fetchPromise = fetch(request).then(networkResponse => {
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  }).catch(() => {
    // Ignorar erros de rede em background
  });
  
  // Retornar cache se disponível, senão aguardar rede
  if (cachedResponse && !isExpired(cachedResponse, maxAge)) {
    return cachedResponse;
  }
  
  return fetchPromise;
}

// Verificar se resposta está expirada
function isExpired(response, maxAge) {
  if (!maxAge) return false;
  
  const dateHeader = response.headers.get('date');
  if (!dateHeader) return false;
  
  const responseDate = new Date(dateHeader);
  const now = new Date();
  
  return (now.getTime() - responseDate.getTime()) > maxAge;
}

// Limpeza periódica de cache
async function cleanupCaches() {
  for (const [type, config] of Object.entries(RESOURCE_CONFIG)) {
    const cache = await caches.open(config.cacheName);
    const requests = await cache.keys();
    
    if (requests.length > config.maxEntries) {
      // Remover entradas mais antigas
      const sortedRequests = requests.sort((a, b) => {
        // Ordenar por data (mais antigos primeiro)
        return new Date(a.headers.get('date') || 0) - new Date(b.headers.get('date') || 0);
      });
      
      const toDelete = sortedRequests.slice(0, requests.length - config.maxEntries);
      await Promise.all(toDelete.map(request => cache.delete(request)));
      
      console.log(`[SW] Cleaned up ${toDelete.length} entries from ${config.cacheName}`);
    }
  }
}

// Executar limpeza a cada 6 horas
setInterval(cleanupCaches, 6 * 60 * 60 * 1000);

// Mensagens do cliente
self.addEventListener('message', event => {
  const { type, payload } = event.data;
  
  switch (type) {
    case 'SKIP_WAITING':
      self.skipWaiting();
      break;
    
    case 'GET_CACHE_STATUS':
      getCacheStatus().then(status => {
        event.ports[0].postMessage({ type: 'CACHE_STATUS', payload: status });
      });
      break;
    
    case 'CLEAR_CACHE':
      clearSpecificCache(payload.cacheName).then(() => {
        event.ports[0].postMessage({ type: 'CACHE_CLEARED' });
      });
      break;
    
    case 'PRELOAD_RESOURCES':
      preloadResources(payload.urls).then(() => {
        event.ports[0].postMessage({ type: 'PRELOAD_COMPLETE' });
      });
      break;
  }
});

// Obter status do cache
async function getCacheStatus() {
  const status = {};
  
  for (const [type, config] of Object.entries(RESOURCE_CONFIG)) {
    const cache = await caches.open(config.cacheName);
    const requests = await cache.keys();
    status[type] = {
      entries: requests.length,
      maxEntries: config.maxEntries,
      cacheName: config.cacheName
    };
  }
  
  return status;
}

// Limpar cache específico
async function clearSpecificCache(cacheName) {
  return caches.delete(cacheName);
}

// Preload de recursos
async function preloadResources(urls) {
  const cache = await caches.open(DYNAMIC_CACHE);
  
  const preloadPromises = urls.map(async url => {
    try {
      const response = await fetch(url);
      if (response.ok) {
        await cache.put(url, response);
      }
    } catch (error) {
      console.warn(`[SW] Failed to preload: ${url}`, error);
    }
  });
  
  return Promise.allSettled(preloadPromises);
}
