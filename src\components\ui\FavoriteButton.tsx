import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Heart, Star, Bookmark } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useFavorites } from '@/hooks/use-favorites';

interface FavoriteButtonProps {
  item: {
    id: string;
    title: string;
    thumbnail: string;
    type: string;
  };
  variant?: 'heart' | 'star' | 'bookmark';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  showLabel?: boolean;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
}

const FavoriteButton: React.FC<FavoriteButtonProps> = ({
  item,
  variant = 'heart',
  size = 'md',
  className,
  showLabel = false,
  position = 'top-right'
}) => {
  const { isFavorite, toggleFavorite } = useFavorites();
  const [isAnimating, setIsAnimating] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);

  const isItemFavorite = isFavorite(item.id);

  // Configurações de ícones
  const icons = {
    heart: Heart,
    star: Star,
    bookmark: Bookmark
  };

  // Configurações de tamanho
  const sizes = {
    sm: {
      button: "w-8 h-8",
      icon: "w-4 h-4",
      text: "text-xs"
    },
    md: {
      button: "w-10 h-10",
      icon: "w-5 h-5",
      text: "text-sm"
    },
    lg: {
      button: "w-12 h-12",
      icon: "w-6 h-6",
      text: "text-base"
    }
  };

  // Configurações de posição
  const positions = {
    'top-right': "top-2 right-2",
    'top-left': "top-2 left-2",
    'bottom-right': "bottom-2 right-2",
    'bottom-left': "bottom-2 left-2"
  };

  const IconComponent = icons[variant];
  const sizeConfig = sizes[size];

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    setIsAnimating(true);
    toggleFavorite(item);
    
    // Reset animation state
    setTimeout(() => setIsAnimating(false), 600);
  };

  return (
    <div className={cn("relative", className)}>
      {/* Botão principal */}
      <motion.button
        className={cn(
          "relative flex items-center justify-center rounded-full transition-all duration-200",
          "backdrop-blur-sm border border-white/20",
          sizeConfig.button,
          isItemFavorite 
            ? "bg-red-500/90 text-white shadow-lg" 
            : "bg-black/30 text-white/80 hover:bg-black/50",
          positions[position],
          "hover:scale-110 focus:outline-none focus:ring-2 focus:ring-red-500/50"
        )}
        onClick={handleClick}
        onMouseEnter={() => setShowTooltip(true)}
        onMouseLeave={() => setShowTooltip(false)}
        whileTap={{ scale: 0.9 }}
        animate={{
          scale: isAnimating ? [1, 1.3, 1] : 1,
        }}
        transition={{
          duration: 0.3,
          ease: "easeInOut"
        }}
      >
        {/* Ícone principal */}
        <motion.div
          animate={{
            rotate: isAnimating ? [0, 15, -15, 0] : 0,
          }}
          transition={{ duration: 0.4 }}
        >
          <IconComponent 
            className={cn(
              sizeConfig.icon,
              isItemFavorite ? "fill-current" : "fill-none"
            )}
          />
        </motion.div>

        {/* Efeito de pulso quando favorito */}
        <AnimatePresence>
          {isItemFavorite && (
            <motion.div
              className="absolute inset-0 rounded-full bg-red-500/30"
              initial={{ scale: 1, opacity: 0.5 }}
              animate={{ 
                scale: [1, 1.5, 2],
                opacity: [0.5, 0.2, 0]
              }}
              exit={{ opacity: 0 }}
              transition={{ 
                duration: 1,
                repeat: Infinity,
                repeatDelay: 2
              }}
            />
          )}
        </AnimatePresence>

        {/* Partículas de coração (apenas para variant heart) */}
        <AnimatePresence>
          {isAnimating && variant === 'heart' && (
            <>
              {Array.from({ length: 6 }).map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute w-1 h-1 bg-red-400 rounded-full"
                  initial={{ 
                    scale: 0,
                    x: 0,
                    y: 0,
                    opacity: 1
                  }}
                  animate={{
                    scale: [0, 1, 0],
                    x: Math.cos((i * Math.PI * 2) / 6) * 20,
                    y: Math.sin((i * Math.PI * 2) / 6) * 20,
                    opacity: [1, 1, 0]
                  }}
                  transition={{
                    duration: 0.6,
                    delay: i * 0.1,
                    ease: "easeOut"
                  }}
                />
              ))}
            </>
          )}
        </AnimatePresence>
      </motion.button>

      {/* Tooltip */}
      <AnimatePresence>
        {showTooltip && (
          <motion.div
            className="absolute z-50 px-2 py-1 text-xs text-white bg-black/80 rounded whitespace-nowrap"
            style={{
              bottom: position.includes('bottom') ? 'auto' : '100%',
              top: position.includes('top') ? 'auto' : '100%',
              left: position.includes('left') ? '100%' : 'auto',
              right: position.includes('right') ? '100%' : 'auto',
              marginBottom: position.includes('bottom') ? 0 : 8,
              marginTop: position.includes('top') ? 0 : 8,
              marginLeft: position.includes('left') ? 8 : 0,
              marginRight: position.includes('right') ? 8 : 0,
            }}
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{ duration: 0.2 }}
          >
            {isItemFavorite ? 'Remover dos favoritos' : 'Adicionar aos favoritos'}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Label (se habilitado) */}
      {showLabel && (
        <motion.div
          className={cn(
            "mt-1 text-center",
            sizeConfig.text,
            isItemFavorite ? "text-red-500" : "text-gray-600"
          )}
          animate={{
            color: isItemFavorite ? "#ef4444" : "#6b7280"
          }}
        >
          {isItemFavorite ? 'Favorito' : 'Favoritar'}
        </motion.div>
      )}
    </div>
  );
};

// Componente de contador de favoritos
export const FavoritesCounter: React.FC<{
  count: number;
  className?: string;
}> = ({ count, className }) => (
  <motion.div
    className={cn(
      "flex items-center gap-1 px-2 py-1 bg-red-500/10 text-red-600 rounded-full text-sm",
      className
    )}
    animate={{
      scale: count > 0 ? [1, 1.1, 1] : 1
    }}
    transition={{ duration: 0.3 }}
  >
    <Heart className="w-4 h-4 fill-current" />
    <span className="font-medium">{count}</span>
  </motion.div>
);

// Componente de lista de favoritos compacta
export const FavoritesList: React.FC<{
  favorites: Array<{
    id: string;
    title: string;
    thumbnail: string;
    type: string;
  }>;
  onItemClick?: (item: any) => void;
  className?: string;
}> = ({ favorites, onItemClick, className }) => (
  <div className={cn("space-y-2", className)}>
    <AnimatePresence>
      {favorites.map((item, index) => (
        <motion.div
          key={item.id}
          className="flex items-center gap-3 p-2 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer"
          onClick={() => onItemClick?.(item)}
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: 20 }}
          transition={{ delay: index * 0.1 }}
          whileHover={{ scale: 1.02 }}
        >
          <img
            src={item.thumbnail}
            alt={item.title}
            className="w-12 h-12 object-cover rounded"
          />
          <div className="flex-1 min-w-0">
            <h4 className="font-medium text-sm truncate">{item.title}</h4>
            <p className="text-xs text-gray-500">{item.type}</p>
          </div>
          <FavoriteButton
            item={item}
            size="sm"
            position="top-right"
          />
        </motion.div>
      ))}
    </AnimatePresence>
  </div>
);

export default FavoriteButton;
