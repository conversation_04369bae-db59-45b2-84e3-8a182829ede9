import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';

interface InteractiveButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'accent' | 'ghost' | 'outline';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  disabled?: boolean;
  loading?: boolean;
  className?: string;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  ripple?: boolean;
  glow?: boolean;
}

// Componente de efeito ripple
const RippleEffect: React.FC<{ x: number; y: number; onComplete: () => void }> = ({ 
  x, 
  y, 
  onComplete 
}) => (
  <motion.div
    className="absolute rounded-full bg-white/30 pointer-events-none"
    style={{
      left: x - 10,
      top: y - 10,
      width: 20,
      height: 20,
    }}
    initial={{ scale: 0, opacity: 1 }}
    animate={{ scale: 4, opacity: 0 }}
    transition={{ duration: 0.6, ease: "easeOut" }}
    onAnimationComplete={onComplete}
  />
);

const InteractiveButton: React.FC<InteractiveButtonProps> = ({
  children,
  onClick,
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  className,
  icon,
  iconPosition = 'left',
  ripple = true,
  glow = false
}) => {
  const [ripples, setRipples] = useState<Array<{ id: number; x: number; y: number }>>([]);
  const [isPressed, setIsPressed] = useState(false);

  // Variantes de estilo
  const variants = {
    primary: "bg-gradient-primary text-white shadow-md hover:shadow-lg",
    secondary: "bg-gradient-to-r from-purple-600 to-purple-700 text-white shadow-md hover:shadow-lg",
    accent: "bg-gradient-accent text-white shadow-md hover:shadow-lg",
    ghost: "bg-transparent text-gray-700 hover:bg-gray-100",
    outline: "border-2 border-primary-500 text-primary-600 hover:bg-primary-50"
  };

  const sizes = {
    sm: "px-3 py-1.5 text-sm",
    md: "px-4 py-2 text-base",
    lg: "px-6 py-3 text-lg",
    xl: "px-8 py-4 text-xl"
  };

  // Manipular clique com efeito ripple
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    if (disabled || loading) return;

    if (ripple) {
      const rect = event.currentTarget.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;
      
      const newRipple = {
        id: Date.now(),
        x,
        y
      };
      
      setRipples(prev => [...prev, newRipple]);
    }

    onClick?.();
  };

  // Remover ripple após animação
  const removeRipple = (id: number) => {
    setRipples(prev => prev.filter(ripple => ripple.id !== id));
  };

  return (
    <motion.button
      className={cn(
        "relative overflow-hidden rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",
        variants[variant],
        sizes[size],
        glow && "hover-glow",
        "hover-lift",
        className
      )}
      onClick={handleClick}
      disabled={disabled || loading}
      onMouseDown={() => setIsPressed(true)}
      onMouseUp={() => setIsPressed(false)}
      onMouseLeave={() => setIsPressed(false)}
      whileTap={{ scale: 0.98 }}
      animate={{
        scale: isPressed ? 0.98 : 1,
      }}
      transition={{ duration: 0.1 }}
    >
      {/* Efeitos ripple */}
      <AnimatePresence>
        {ripples.map(ripple => (
          <RippleEffect
            key={ripple.id}
            x={ripple.x}
            y={ripple.y}
            onComplete={() => removeRipple(ripple.id)}
          />
        ))}
      </AnimatePresence>

      {/* Conteúdo do botão */}
      <div className="relative flex items-center justify-center gap-2">
        {/* Ícone à esquerda */}
        {icon && iconPosition === 'left' && (
          <motion.div
            initial={{ opacity: 0, x: -10 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.2 }}
          >
            {icon}
          </motion.div>
        )}

        {/* Texto/conteúdo */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.2, delay: 0.1 }}
        >
          {loading ? (
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
              <span>Carregando...</span>
            </div>
          ) : (
            children
          )}
        </motion.div>

        {/* Ícone à direita */}
        {icon && iconPosition === 'right' && (
          <motion.div
            initial={{ opacity: 0, x: 10 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.2 }}
          >
            {icon}
          </motion.div>
        )}
      </div>

      {/* Overlay de hover */}
      <motion.div
        className="absolute inset-0 bg-white/10 opacity-0"
        whileHover={{ opacity: 1 }}
        transition={{ duration: 0.2 }}
      />
    </motion.button>
  );
};

// Componente de botão flutuante (FAB)
export const FloatingActionButton: React.FC<{
  children: React.ReactNode;
  onClick?: () => void;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}> = ({ children, onClick, className, size = 'md' }) => {
  const sizes = {
    sm: "w-12 h-12",
    md: "w-14 h-14",
    lg: "w-16 h-16"
  };

  return (
    <motion.button
      className={cn(
        "fixed bottom-6 right-6 rounded-full bg-gradient-primary text-white shadow-lg z-50",
        "flex items-center justify-center",
        "hover-lift hover-glow",
        sizes[size],
        className
      )}
      onClick={onClick}
      whileHover={{ scale: 1.1 }}
      whileTap={{ scale: 0.9 }}
      initial={{ scale: 0, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      transition={{ 
        type: "spring", 
        stiffness: 260, 
        damping: 20,
        delay: 0.5 
      }}
    >
      {children}
    </motion.button>
  );
};

// Componente de toggle switch animado
export const AnimatedToggle: React.FC<{
  checked: boolean;
  onChange: (checked: boolean) => void;
  label?: string;
  disabled?: boolean;
}> = ({ checked, onChange, label, disabled = false }) => {
  return (
    <label className="flex items-center gap-3 cursor-pointer">
      <div className="relative">
        <motion.div
          className={cn(
            "w-12 h-6 rounded-full transition-colors duration-200",
            checked ? "bg-primary-500" : "bg-gray-300",
            disabled && "opacity-50 cursor-not-allowed"
          )}
          onClick={() => !disabled && onChange(!checked)}
        >
          <motion.div
            className="absolute top-0.5 left-0.5 w-5 h-5 bg-white rounded-full shadow-md"
            animate={{
              x: checked ? 24 : 0,
            }}
            transition={{
              type: "spring",
              stiffness: 500,
              damping: 30
            }}
          />
        </motion.div>
      </div>
      {label && (
        <span className={cn(
          "text-sm font-medium",
          disabled ? "text-gray-400" : "text-gray-700"
        )}>
          {label}
        </span>
      )}
    </label>
  );
};

export default InteractiveButton;
