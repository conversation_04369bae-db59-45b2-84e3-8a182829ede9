
import React, { useState, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';

interface Project {
  id: string;
  title: string;
  thumbnail: string;
  modelUrl: string;
  htmlContent: string;
  type: string;
  description: string;
  exocadHtmlUrl?: string;
}

interface ThumbnailCarouselProps {
  projects: Project[];
  currentProjectId: string;
  onSelectProject: (index: number) => void;
  onNavigateNext: () => void;
  onNavigatePrevious: () => void;
}

const ThumbnailCarousel = ({
  projects,
  currentProjectId,
  onSelectProject,
  onNavigateNext,
  onNavigatePrevious
}: ThumbnailCarouselProps) => {
  const isMobile = useIsMobile();

  // Refs for drag functionality
  const carouselRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [startX, setStartX] = useState(0);
  const [scrollLeft, setScrollLeft] = useState(0);
  const [clickStartTime, setClickStartTime] = useState(0);
  const [clickStartPosition, setClickStartPosition] = useState({ x: 0, y: 0 });

  // Get current project index
  const currentIndex = projects.findIndex(project => project.id === currentProjectId);

  // Calculate which thumbnails to show (all projects for dragging)
  const visibleProjects = projects;

  // Scroll to current project when it changes
  useEffect(() => {
    if (carouselRef.current && currentProjectId) {
      const currentElement = carouselRef.current.querySelector(`[data-project-id="${currentProjectId}"]`);
      if (currentElement) {
        const containerWidth = carouselRef.current.offsetWidth;
        const elementOffset = (currentElement as HTMLElement).offsetLeft;
        const elementWidth = (currentElement as HTMLElement).offsetWidth;

        // Center the element
        carouselRef.current.scrollLeft = elementOffset - (containerWidth / 2) + (elementWidth / 2);
      }
    }
  }, [currentProjectId]);

  // Drag handlers
  const handleMouseDown = (e: React.MouseEvent) => {
    if (!carouselRef.current) return;

    setIsDragging(true);
    setStartX(e.pageX - carouselRef.current.offsetLeft);
    setScrollLeft(carouselRef.current.scrollLeft);
    setClickStartTime(Date.now());
    setClickStartPosition({ x: e.pageX, y: e.pageY });

    // Change cursor style
    if (carouselRef.current) {
      carouselRef.current.style.cursor = 'grabbing';
    }
  };

  const handleTouchStart = (e: React.TouchEvent) => {
    if (!carouselRef.current || e.touches.length !== 1) return;

    setIsDragging(true);
    setStartX(e.touches[0].pageX - carouselRef.current.offsetLeft);
    setScrollLeft(carouselRef.current.scrollLeft);
    setClickStartTime(Date.now());
    setClickStartPosition({ x: e.touches[0].pageX, y: e.touches[0].pageY });

    // Prevent default to avoid page scrolling while dragging
    if (isMobile) {
      e.preventDefault();
    }
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging || !carouselRef.current) return;

    e.preventDefault();
    const x = e.pageX - carouselRef.current.offsetLeft;
    const walk = (x - startX) * 2.5; // Increased scroll speed multiplier for better responsiveness
    carouselRef.current.scrollLeft = scrollLeft - walk;
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!isDragging || !carouselRef.current || e.touches.length !== 1) return;

    // Prevent default to avoid page scrolling while dragging
    if (isMobile) {
      e.preventDefault();
    }

    const x = e.touches[0].pageX - carouselRef.current.offsetLeft;
    const walk = (x - startX) * 2.5; // Increased scroll speed multiplier for better responsiveness
    carouselRef.current.scrollLeft = scrollLeft - walk;
  };

  const handleMouseUp = (e: React.MouseEvent) => {
    setIsDragging(false);

    // Reset cursor style
    if (carouselRef.current) {
      carouselRef.current.style.cursor = 'grab';
    }
  };

  const handleTouchEnd = () => {
    setIsDragging(false);
  };

  return (
    <div className={cn(
      "fixed left-0 right-0 flex justify-center pointer-events-auto overflow-hidden",
      isMobile ? "bottom-0 py-3 px-2" : "bottom-4 py-2 px-4" // Reduced horizontal padding on mobile
    )}>
      <div className={cn(
        "flex items-center gap-2 p-2 bg-black/20 backdrop-blur-sm mx-auto w-full max-w-screen-sm",
        isMobile ? "h-[60px] justify-center rounded-t-2xl rounded-b-none" : "rounded-full" // Rounded top corners only on mobile
      )}>
        {/* Previous button */}
        <button
          onClick={onNavigatePrevious}
          className={cn(
            "flex items-center justify-center rounded-full bg-white/5 hover:bg-white/15 transition-colors transform hover:scale-105 flex-shrink-0",
            isMobile ? "h-8 w-8" : "h-14 w-14" // Even smaller buttons on mobile
          )}
          aria-label="Caso anterior"
        >
          <ChevronLeft className={cn(
            "text-white",
            isMobile ? "h-4 w-4" : "h-6 w-6" // Smaller icons on mobile
          )} />
        </button>

        {/* Thumbnails */}
        <div
          ref={carouselRef}
          className={cn(
            "flex items-center gap-2 px-1",
            isMobile ? "overflow-x-scroll hide-scrollbar" : "overflow-x-auto hide-scrollbar", // Hide scrollbar on mobile
            isDragging ? "cursor-grabbing" : "cursor-grab",
            isMobile ? "flex-1 min-w-0" : "" // Use flex-1 instead of fixed width to adapt to container
          )}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={() => setIsDragging(false)}
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
          style={{
            userSelect: 'none',
            scrollbarWidth: 'none', // Hide scrollbar in Firefox
            msOverflowStyle: 'none', // Hide scrollbar in IE/Edge
            WebkitOverflowScrolling: 'touch' // Smooth scrolling on iOS
          }}
        >
          {visibleProjects.map((project, idx) => {
            // Calculate the actual index in the full projects array
            const actualIndex = projects.findIndex(p => p.id === project.id);

            return (
              <div
                key={project.id}
                data-project-id={project.id}
                className={cn(
                  "transition-all duration-300 rounded-full overflow-hidden group relative flex-shrink-0",
                  currentProjectId === project.id
                    ? "ring-2 ring-white scale-110 z-10"
                    : "opacity-70 hover:opacity-100"
                )}
              >
                <button
                  onClick={(e) => {
                    // Only select if it was a click, not a drag
                    if (Date.now() - clickStartTime < 200 &&
                        Math.abs(e.pageX - clickStartPosition.x) < 10 &&
                        Math.abs(e.pageY - clickStartPosition.y) < 10) {
                      onSelectProject(actualIndex);
                    }
                  }}
                  className="relative focus:outline-none"
                >
                  <img
                    src={project.thumbnail}
                    alt={project.title}
                    className={cn(
                      "object-cover rounded-full",
                      isMobile ? "h-9 w-9" : "h-12 w-12" // Slightly smaller thumbnails on mobile to prevent overflow
                    )}
                    draggable="false"
                  />
                  <div
                    className={cn(
                      "absolute inset-0 transition-colors flex items-center justify-center rounded-full",
                      currentProjectId === project.id
                        ? "bg-black/40"
                        : "bg-black/0 group-hover:bg-black/30"
                    )}
                  >
                    {currentProjectId === project.id && (
                      <div className="w-2 h-2 bg-white rounded-full"></div>
                    )}
                  </div>
                </button>
              </div>
            );
          })}
        </div>

        {/* Next button */}
        <button
          onClick={onNavigateNext}
          className={cn(
            "flex items-center justify-center rounded-full bg-white/5 hover:bg-white/15 transition-colors transform hover:scale-105 flex-shrink-0",
            isMobile ? "h-8 w-8" : "h-14 w-14" // Even smaller buttons on mobile
          )}
          aria-label="Próximo caso"
        >
          <ChevronRight className={cn(
            "text-white",
            isMobile ? "h-4 w-4" : "h-6 w-6" // Smaller icons on mobile
          )} />
        </button>
      </div>
    </div>
  );
};

export default ThumbnailCarousel;
