import { useState, useEffect, useCallback } from 'react';
import { toast } from '@/components/ui/sonner';

interface CacheStatus {
  [key: string]: {
    entries: number;
    maxEntries: number;
    cacheName: string;
  };
}

interface UseServiceWorkerReturn {
  isSupported: boolean;
  isRegistered: boolean;
  isOnline: boolean;
  cacheStatus: CacheStatus | null;
  updateAvailable: boolean;
  registration: ServiceWorkerRegistration | null;
  registerSW: () => Promise<void>;
  updateSW: () => void;
  getCacheStatus: () => Promise<CacheStatus>;
  clearCache: (cacheName?: string) => Promise<void>;
  preloadResources: (urls: string[]) => Promise<void>;
}

export const useServiceWorker = (): UseServiceWorkerReturn => {
  const [isSupported] = useState(() => 'serviceWorker' in navigator);
  const [isRegistered, setIsRegistered] = useState(false);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [cacheStatus, setCacheStatus] = useState<CacheStatus | null>(null);
  const [updateAvailable, setUpdateAvailable] = useState(false);
  const [registration, setRegistration] = useState<ServiceWorkerRegistration | null>(null);

  // Monitorar status online/offline
  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      toast.success('Conexão restaurada');
    };

    const handleOffline = () => {
      setIsOnline(false);
      toast.info('Modo offline ativado');
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Registrar Service Worker
  const registerSW = useCallback(async () => {
    if (!isSupported) {
      console.warn('Service Worker não suportado');
      return;
    }

    try {
      const reg = await navigator.serviceWorker.register('/sw.js', {
        scope: '/'
      });

      setRegistration(reg);
      setIsRegistered(true);

      console.log('Service Worker registrado:', reg);

      // Verificar atualizações
      reg.addEventListener('updatefound', () => {
        const newWorker = reg.installing;
        if (newWorker) {
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              setUpdateAvailable(true);
              toast.info('Nova versão disponível', {
                description: 'Clique para atualizar',
                action: {
                  label: 'Atualizar',
                  onClick: updateSW
                }
              });
            }
          });
        }
      });

      // Escutar mensagens do SW
      navigator.serviceWorker.addEventListener('message', handleSWMessage);

      // Obter status inicial do cache
      await getCacheStatus();

    } catch (error) {
      console.error('Erro ao registrar Service Worker:', error);
    }
  }, [isSupported]);

  // Atualizar Service Worker
  const updateSW = useCallback(() => {
    if (registration?.waiting) {
      registration.waiting.postMessage({ type: 'SKIP_WAITING' });
      window.location.reload();
    }
  }, [registration]);

  // Manipular mensagens do Service Worker
  const handleSWMessage = useCallback((event: MessageEvent) => {
    const { type, payload } = event.data;

    switch (type) {
      case 'CACHE_STATUS':
        setCacheStatus(payload);
        break;
      case 'CACHE_CLEARED':
        toast.success('Cache limpo com sucesso');
        getCacheStatus();
        break;
      case 'PRELOAD_COMPLETE':
        toast.success('Recursos pré-carregados');
        break;
    }
  }, []);

  // Obter status do cache
  const getCacheStatus = useCallback(async (): Promise<CacheStatus> => {
    if (!registration?.active) {
      return {};
    }

    return new Promise((resolve) => {
      const messageChannel = new MessageChannel();
      
      messageChannel.port1.onmessage = (event) => {
        const { type, payload } = event.data;
        if (type === 'CACHE_STATUS') {
          setCacheStatus(payload);
          resolve(payload);
        }
      };

      registration.active.postMessage(
        { type: 'GET_CACHE_STATUS' },
        [messageChannel.port2]
      );
    });
  }, [registration]);

  // Limpar cache
  const clearCache = useCallback(async (cacheName?: string): Promise<void> => {
    if (!registration?.active) {
      throw new Error('Service Worker não ativo');
    }

    return new Promise((resolve, reject) => {
      const messageChannel = new MessageChannel();
      
      messageChannel.port1.onmessage = (event) => {
        const { type } = event.data;
        if (type === 'CACHE_CLEARED') {
          resolve();
        }
      };

      const timeout = setTimeout(() => {
        reject(new Error('Timeout ao limpar cache'));
      }, 5000);

      registration.active.postMessage(
        { 
          type: 'CLEAR_CACHE', 
          payload: { cacheName: cacheName || 'all' }
        },
        [messageChannel.port2]
      );

      messageChannel.port1.onmessage = (event) => {
        clearTimeout(timeout);
        resolve();
      };
    });
  }, [registration]);

  // Pré-carregar recursos
  const preloadResources = useCallback(async (urls: string[]): Promise<void> => {
    if (!registration?.active) {
      throw new Error('Service Worker não ativo');
    }

    return new Promise((resolve, reject) => {
      const messageChannel = new MessageChannel();
      
      const timeout = setTimeout(() => {
        reject(new Error('Timeout ao pré-carregar recursos'));
      }, 30000);

      messageChannel.port1.onmessage = (event) => {
        const { type } = event.data;
        if (type === 'PRELOAD_COMPLETE') {
          clearTimeout(timeout);
          resolve();
        }
      };

      registration.active.postMessage(
        { 
          type: 'PRELOAD_RESOURCES', 
          payload: { urls }
        },
        [messageChannel.port2]
      );
    });
  }, [registration]);

  // Auto-registrar em produção
  useEffect(() => {
    if (process.env.NODE_ENV === 'production' && isSupported) {
      registerSW();
    }
  }, [isSupported, registerSW]);

  return {
    isSupported,
    isRegistered,
    isOnline,
    cacheStatus,
    updateAvailable,
    registration,
    registerSW,
    updateSW,
    getCacheStatus,
    clearCache,
    preloadResources
  };
};

// Hook para estatísticas de cache
export const useCacheStats = () => {
  const { cacheStatus, getCacheStatus } = useServiceWorker();

  const stats = {
    totalEntries: cacheStatus ? Object.values(cacheStatus).reduce((sum, cache) => sum + cache.entries, 0) : 0,
    totalCapacity: cacheStatus ? Object.values(cacheStatus).reduce((sum, cache) => sum + cache.maxEntries, 0) : 0,
    utilizationPercentage: cacheStatus ? 
      (Object.values(cacheStatus).reduce((sum, cache) => sum + cache.entries, 0) / 
       Object.values(cacheStatus).reduce((sum, cache) => sum + cache.maxEntries, 0)) * 100 : 0,
    cachesByType: cacheStatus || {}
  };

  return {
    ...stats,
    refreshStats: getCacheStatus
  };
};
