
import { useState, useEffect } from 'react';
import { projectsData } from '@/data/projectsData';

export type TextAnimation = {
  title: boolean;
  subtitle: boolean;
  description: boolean;
  tags: boolean;
};

export function useCases() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [transitioning, setTransitioning] = useState(false);
  const [modelLoading, setModelLoading] = useState(true);
  const [filterCategory, setFilterCategory] = useState("TODOS");

  const currentCase = projectsData[currentIndex];

  // Extract unique categories from projects and convert to uppercase
  const categories = ["TODOS", ...Array.from(new Set(projectsData.map(project => project.type?.toUpperCase() || "OUTROS")))];

  // Effect to handle filter category changes
  useEffect(() => {
    if (filterCategory === "TODOS") {
      // If filter is set to "TODOS", go back to the first case
      setTransitioning(true);
      setCurrentIndex(0);
    } else {
      // Find the first case with the selected category
      const firstMatchingIndex = projectsData.findIndex(
        project => project.type?.toUpperCase() === filterCategory
      );

      if (firstMatchingIndex !== -1) {
        setTransitioning(true);
        setCurrentIndex(firstMatchingIndex);
      }
    }
  }, [filterCategory]);

  const goToNextCase = () => {
    setTransitioning(true);

    // If filtering is active, navigate within filtered cases
    if (filterCategory !== "TODOS") {
      const filteredCases = projectsData.filter(project => project.type?.toUpperCase() === filterCategory);
      const currentFilteredIndex = filteredCases.findIndex(project => project.id === currentCase.id);
      const nextFilteredIndex = (currentFilteredIndex + 1) % filteredCases.length;
      const nextCaseId = filteredCases[nextFilteredIndex].id;
      const nextIndex = projectsData.findIndex(project => project.id === nextCaseId);
      setCurrentIndex(nextIndex);
    } else {
      // Otherwise navigate through all cases
      setCurrentIndex((prev) => (prev + 1) % projectsData.length);
    }
  };

  const goToPreviousCase = () => {
    setTransitioning(true);

    // If filtering is active, navigate within filtered cases
    if (filterCategory !== "TODOS") {
      const filteredCases = projectsData.filter(project => project.type?.toUpperCase() === filterCategory);
      const currentFilteredIndex = filteredCases.findIndex(project => project.id === currentCase.id);
      const prevFilteredIndex = (currentFilteredIndex - 1 + filteredCases.length) % filteredCases.length;
      const prevCaseId = filteredCases[prevFilteredIndex].id;
      const prevIndex = projectsData.findIndex(project => project.id === prevCaseId);
      setCurrentIndex(prevIndex);
    } else {
      // Otherwise navigate through all cases
      setCurrentIndex((prev) => (prev - 1 + projectsData.length) % projectsData.length);
    }
  };

  // Reset model loading state when changing cases
  useEffect(() => {
    // Set loading state when changing cases
    setModelLoading(true);
    setTransitioning(true);
  }, [currentIndex]);

  const handleModelLoad = () => {
    // Clear transitioning state immediately when model loads
    setTransitioning(false);
    setModelLoading(false);

    console.log("Model loaded successfully:", projectsData[currentIndex].title);
  };

  const handleSelectProject = (idx: number) => {
    setTransitioning(true);

    // If we're selecting from filtered thumbnails, we need to find the correct index in the full list
    if (filterCategory !== "TODOS") {
      const filteredCases = projectsData.filter(project => project.type?.toUpperCase() === filterCategory);
      if (idx >= 0 && idx < filteredCases.length) {
        const selectedCaseId = filteredCases[idx].id;
        const fullIndex = projectsData.findIndex(project => project.id === selectedCaseId);
        setCurrentIndex(fullIndex);
      }
    } else {
      // If not filtering, just use the index directly
      setCurrentIndex(idx);
    }
  };

  // Filter projects based on category
  const filteredThumbnails = filterCategory === "TODOS"
    ? projectsData
    : projectsData.filter(project => project.type?.toUpperCase() === filterCategory);

  return {
    currentCase,
    currentIndex,
    transitioning,
    modelLoading,
    filterCategory,
    categories,
    filteredThumbnails,
    setFilterCategory,
    goToNextCase,
    goToPreviousCase,
    handleModelLoad,
    handleSelectProject
  };
}
