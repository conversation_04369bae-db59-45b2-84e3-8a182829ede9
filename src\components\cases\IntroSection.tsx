
import React, { useEffect, useState } from 'react';
import { cn } from '@/lib/utils';
import { ChevronDown } from 'lucide-react';

interface IntroSectionProps {
  introVisible: boolean;
  textAnimation: {
    title: boolean;
    subtitle: boolean;
    description: boolean;
    tags: boolean;
  };
  onSkipIntro: () => void;
  modelLoaded: boolean;
}

const IntroSection = ({ introVisible, textAnimation, onSkipIntro, modelLoaded }: IntroSectionProps) => {
  const [particles, setParticles] = useState<Array<{
    id: number,
    x: number,
    y: number,
    size: number,
    speed: number,
    opacity?: number,
    color?: string,
    pulse?: boolean,
    rotate?: boolean,
    rotateSpeed?: number
  }>>([]);
  const [showSkipButton, setShowSkipButton] = useState(false);

  // Generate floating particles effect with improved aesthetics
  useEffect(() => {
    if (!introVisible) return;

    // Aumentamos a quantidade de partículas para um efeito mais rico
    const particleCount = 60;

    // Criamos partículas com mais propriedades para efeitos visuais
    const newParticles = Array.from({ length: particleCount }, (_, i) => ({
      id: i,
      x: Math.random() * 100,
      y: Math.random() * 100,
      size: 0.8 + Math.random() * 3.5, // Partículas menores para um efeito mais sutil
      speed: 0.3 + Math.random() * 1.2, // Velocidade mais lenta para movimento mais suave
      opacity: 0.1 + Math.random() * 0.4, // Opacidade variável
      color: Math.random() > 0.7 ? 'purple' : 'default', // Algumas partículas com cor diferente
      pulse: Math.random() > 0.8, // Algumas partículas pulsam
      rotate: Math.random() > 0.7, // Algumas partículas rotacionam
      rotateSpeed: Math.random() * 0.5
    }));

    setParticles(newParticles);

    // Animate particles com movimentos mais complexos
    const animationInterval = setInterval(() => {
      setParticles(prev => prev.map(particle => {
        // Movimento sinusoidal horizontal sutil para algumas partículas
        const xMovement = particle.rotate ?
          Math.sin(Date.now() * 0.001 * particle.rotateSpeed) * 0.1 : 0;

        return {
          ...particle,
          y: particle.y - particle.speed / 5,
          x: particle.x + xMovement,
          // Reset particle position when it goes off the screen
          ...(particle.y < -5 ? {
            y: 105,
            x: Math.random() * 100,
            size: 0.8 + Math.random() * 3.5,
            opacity: 0.1 + Math.random() * 0.4,
            color: Math.random() > 0.7 ? 'purple' : 'default',
            pulse: Math.random() > 0.8,
            rotate: Math.random() > 0.7,
            rotateSpeed: Math.random() * 0.5
          } : {})
        };
      }));
    }, 40); // Intervalo menor para animação mais suave

    return () => clearInterval(animationInterval);
  }, [introVisible]);

  // Show skip button only after categories have been displayed (after 3 seconds)
  useEffect(() => {
    if (textAnimation.tags && introVisible) {
      const timer = setTimeout(() => {
        setShowSkipButton(true);
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [textAnimation.tags, introVisible]);

  // Categories for dental work - desktop order
  const desktopServices = [
    'COROAS',
    'LENTES',
    'PONTES',
    'PLANEJAMENTOS',
    'COROAS SOBRE IMPLANTE',
    'RESTAURADOS',
    'PONTES SOBRE IMPLANTE',
    'PLACAS DE BRUXISMO',
    'PROVISÓRIOS',
    'CASCA DE OVO',
    'MODELOS',
    'MODELOS COM IMPLANTE E GENGIVA'
  ];

  // Categories for dental work - mobile order with shorter names for some categories
  const mobileServices = [
    'COROAS',
    'LENTES',
    'PLANEJAMENTOS',
    'PONTES',
    'RESTAURADOS',
    'PLACAS DE BRUXISMO',
    'PROVISÓRIOS',
    'CASCA DE OVO',
    'MODELOS',
    'COROAS S/ IMPLANTE',
    'PONTES S/ IMPLANTE',
    'MODELOS IMPLANTE/GENGIVA'
  ];

  // State to track which service list to use
  const [isMobile, setIsMobile] = useState(false);

  // Effect to detect screen size and update on resize
  useEffect(() => {
    // Initial check
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 640);
    };

    // Only run on client side
    if (typeof window !== 'undefined') {
      checkScreenSize();

      // Add resize listener
      window.addEventListener('resize', checkScreenSize);

      // Cleanup
      return () => window.removeEventListener('resize', checkScreenSize);
    }
  }, []);

  // Função para formatar o texto das categorias com quebras de linha quando necessário
  const formatCategoryText = (text: string, isMobile: boolean) => {
    // Se for uma categoria com nome muito longo em uma única palavra
    if (text.length > 12 && !text.includes(' ')) {
      // Reduzir o tamanho da fonte para categorias com palavras muito longas
      return <span className="text-[10px] sm:text-xs">{text}</span>;
    }

    // Se for uma categoria com 2 ou mais palavras
    if (text.includes(' ')) {
      const words = text.split(' ');

      // Se tiver mais de 2 palavras
      if (words.length > 2) {
        return (
          <>
            {words.slice(0, 1).join(' ')}<br />
            {words.slice(1, 2).join(' ')}<br />
            {words.slice(2).join(' ')}
          </>
        );
      }

      // Se tiver 2 palavras
      return (
        <>
          {words[0]}<br />
          {words[1]}
        </>
      );
    }

    // Caso contrário, retorna o texto normal
    return text;
  };

  // Use appropriate service list based on screen size
  const dentalServices = isMobile ? mobileServices : desktopServices;

  return (
    <div
      className={cn(
        "fixed inset-0 z-50 flex flex-col items-center justify-center bg-black px-6 py-12 transition-all duration-1000 overflow-hidden",
        introVisible ? "opacity-100" : "opacity-0 pointer-events-none"
      )}
    >
      {/* Animated background particles - enhanced version */}
      {particles.map(particle => (
        <div
          key={particle.id}
          className={cn(
            "absolute rounded-full blur-sm",
            particle.color === 'purple' ? 'bg-purple-400/20' : 'bg-white/10',
            particle.pulse && 'animate-pulse'
          )}
          style={{
            left: `${particle.x}%`,
            top: `${particle.y}%`,
            width: `${particle.size}px`,
            height: `${particle.size}px`,
            opacity: particle.opacity || 0.2,
            transition: 'transform 0.5s ease-out, opacity 0.8s ease-in-out',
            transform: particle.rotate ? `rotate(${Date.now() * 0.01 * (particle.rotateSpeed || 0.1)}deg)` : 'none'
          }}
        />
      ))}

      {/* Glow effects - enhanced with more vibrant colors and animations */}
      <div className="absolute w-[600px] h-[600px] bg-gradient-to-r from-purple-500/15 to-transparent rounded-full blur-3xl animate-pulse"
           style={{animationDuration: '8s'}}></div>
      <div className="absolute bottom-10 left-10 w-[400px] h-[400px] bg-gradient-to-r from-blue-500/10 to-transparent rounded-full blur-3xl"
           style={{animation: 'float 10s ease-in-out infinite'}}></div>
      <div className="absolute top-20 right-10 w-[350px] h-[350px] bg-gradient-to-r from-purple-500/10 to-transparent rounded-full blur-3xl"
           style={{animation: 'float 12s ease-in-out infinite', animationDelay: '2s'}}></div>

      {/* Novo efeito de brilho central */}
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-gradient-to-br from-purple-500/5 via-blue-500/5 to-transparent rounded-full blur-3xl opacity-70"></div>

      {/* Header content - título, nome, linha e informações - movido para cima */}
      <div className="max-w-5xl mx-auto text-center space-y-1 sm:space-y-4 md:space-y-8 relative z-10 pt-0 sm:pt-4 md:pt-8 mt-[-48vh] sm:mt-[-56vh] md:mt-[-56vh]">
        <div className={cn(
          "transition-all duration-1000 transform",
          textAnimation.title ? "opacity-100 translate-y-0" : "opacity-0 translate-y-8"
        )}>
          <div className="flex flex-col items-center justify-center mb-0 sm:mb-2">
            <div className="font-bold tracking-tight uppercase text-white mb-0 sm:mb-1" style={{ fontSize: 'var(--step-xl)' }}>
              <span className="bg-gradient-to-r from-purple-400 to-blue-500 text-transparent bg-clip-text">FORM & FUNCTION</span>
            </div>
            <h1 className="font-bold tracking-tight uppercase text-white px-2 leading-tight mt-3 sm:mt-4 md:mt-5" style={{ fontSize: 'var(--step-xl)' }}>
              PORTFÓLIO ODONTOLÓGICO
            </h1>
            <p className="text-white/70 mt-3 sm:mt-4 md:mt-5 font-light uppercase tracking-wider" style={{ fontSize: 'var(--step-md)' }}>JONHNATAS LIMA</p>
          </div>
        </div>

        <div className={cn(
          "space-y-4 sm:space-y-6 md:space-y-8 transition-all duration-1000 delay-300 transform",
          textAnimation.subtitle ? "opacity-100 translate-y-0" : "opacity-0 translate-y-8"
        )}>
          <div className="h-px w-full max-w-sm bg-gradient-to-r from-transparent via-purple-400/50 to-transparent mx-auto mt-4 sm:mt-5 md:mt-6 mb-2 sm:mb-3 md:mb-4 relative">
            <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-6 h-6">
              <div className="w-1.5 h-1.5 bg-purple-400 rounded-full absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 animate-pulse"></div>
            </div>
          </div>

          <p className="leading-relaxed uppercase px-4 relative mt-8 sm:mt-10 md:mt-12" style={{ fontSize: 'var(--step-sm)' }}>
            <span className="bg-gradient-to-r from-purple-300 to-blue-300 text-transparent bg-clip-text font-medium">ESPECIALISTA EM MODELAGEM 3D</span>
            <br className="sm:hidden" />
            <span className="text-white/80">PARA APLICAÇÕES ODONTOLÓGICAS,</span>
            <br className="hidden sm:block" />
            <span className="bg-gradient-to-r from-blue-300 to-purple-300 text-transparent bg-clip-text font-medium">FOCO EM PRECISÃO, ESTÉTICA E FUNCIONALIDADE.</span>
          </p>
        </div>
      </div>

      {/* Service categories - em um contêiner separado para posicionamento independente */}
      <div className="absolute left-0 right-0 bottom-[60px] sm:bottom-[80px] md:bottom-[100px] z-10 max-w-5xl mx-auto">
        <div className={cn(
          "transition-all duration-1000 delay-700 transform",
          textAnimation.tags ? "opacity-100 translate-y-0" : "opacity-0 translate-y-8"
        )}>
          <div className={cn(
            "grid py-3 sm:py-4 md:py-6 px-2 relative",
            "gap-0", // Removemos o gap para que a grade fique contínua
            "grid-cols-3", // 3 colunas por padrão em mobile
            "sm:grid-cols-3 md:grid-cols-4", // Mantém 3 colunas em tablet, 4 em desktop
            isMobile ? "auto-rows-fr" : "", // Altura igual para todas as linhas em mobile
            "" // Removemos a grade padrão do Tailwind para implementar uma personalizada
          )}>
            {dentalServices.map((service, index) => (
              <div
                key={service}
                className={cn(
                  "bg-white/2 hover:bg-white/5 transition-all duration-500 font-medium uppercase text-white relative overflow-hidden group hover:scale-[1.02] backdrop-blur-[1px] border-0 hover:bg-purple-500/10 flex items-center justify-center text-center intro-category",
                  isMobile ? "px-1 py-2" : "px-2 py-2.5 sm:px-3 sm:py-3 md:py-4", // Padding ajustado para centralização
                  "leading-tight", // Mantém apenas o leading-tight
                  // Adicionamos uma abordagem mais simples para as bordas internas
                  "border-r border-b border-purple-400/10", // Todas as células têm borda direita e inferior
                  // Removemos bordas externas
                  index % (isMobile ? 3 : 4) === (isMobile ? 2 : 3) ? "border-r-0" : "", // Sem borda direita para última coluna
                  index >= dentalServices.length - (isMobile ? 3 : 4) ? "border-b-0" : "" // Sem borda inferior para última linha
                )}
                style={{
                  animationDelay: `${index * 80}ms`,
                  animation: 'fadeInUp 0.5s ease-out forwards'
                }}
              >
                {/* Efeito de partículas no hover */}
                <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                {/* Conteúdo */}
                <div className="relative z-10 group-hover:text-purple-100 transition-colors duration-300 w-full text-center">
                  {formatCategoryText(service, isMobile)}
                </div>

                {/* Linha de destaque no hover */}
                <div className="absolute bottom-0 left-0 w-full h-0.5 bg-gradient-to-r from-transparent via-purple-400/40 to-transparent transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500"></div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Animated skip button - only shows after categories are displayed (3 seconds) */}
      {showSkipButton && (
        <div
          className={cn(
            "fixed left-1/2 transform -translate-x-1/2 transition-all duration-1200 cursor-pointer",
            "bottom-4 sm:bottom-10", // Posição mais alta em dispositivos móveis
            showSkipButton ? "opacity-100 translate-y-0" : "opacity-0 translate-y-10"
          )}
          onClick={onSkipIntro}
        >
          <div className="flex flex-col items-center space-y-1 sm:space-y-2 group">
            <span className="text-white/70 font-medium uppercase group-hover:text-white transition-colors tracking-wider" style={{ fontSize: 'var(--step-xs)' }}>
              <span className="bg-gradient-to-r from-purple-300 to-blue-300 text-transparent bg-clip-text group-hover:from-white group-hover:to-white transition-all duration-300">Pular introdução</span>
            </span>
            <div className="w-8 h-8 sm:w-10 sm:h-10 flex items-center justify-center rounded-full bg-gradient-to-br from-purple-500/20 to-blue-500/20 group-hover:from-purple-500/40 group-hover:to-blue-500/40 transition-all duration-700 animate-bounce animate-duration-2000 backdrop-blur-sm border border-white/10 group-hover:border-white/20">
              <ChevronDown className="w-4 h-4 sm:w-5 sm:h-5 text-white" />
              <div className="absolute inset-0 rounded-full bg-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default IntroSection;
