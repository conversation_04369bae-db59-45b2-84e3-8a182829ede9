import React, { useState, useEffect } from 'react';
import { projectsData } from '@/data/projectsData';
import { X, Save, Plus, Edit, Trash2, Eye, ArrowUp, ArrowDown, Upload, FileText, Image as ImageIcon, ExternalLink, Download, Copy, AlertTriangle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from '@/components/ui/sonner';
import { Project } from '@/components/ProjectGallery';
import HtmlEditor from '@/components/admin/HtmlEditor';
import FileUploader from '@/components/admin/FileUploader';
import SaveDirectlyButton from '@/components/admin/SaveDirectlyButton';
import HybridModelViewer from '@/components/3d/HybridModelViewer';
import ThemeToggle from '@/components/ui/ThemeToggle';

// Senha para acessar o painel de administração (em produção, use um método mais seguro)
const ADMIN_PASSWORD = 'admin123';

const Admin = () => {
  // Estado de autenticação
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [password, setPassword] = useState('');

  // Estado dos casos
  const [cases, setCases] = useState<Project[]>([]);
  const [selectedCase, setSelectedCase] = useState<Project | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // Estado para exportação
  const [exportedData, setExportedData] = useState('');
  const [showExportDialog, setShowExportDialog] = useState(false);

  // Inicializar os casos a partir dos dados existentes
  useEffect(() => {
    setCases([...projectsData]);
  }, []);

  // Função para autenticar
  const handleAuthenticate = () => {
    if (password === ADMIN_PASSWORD) {
      setIsAuthenticated(true);
      toast.success('Autenticado com sucesso!');
    } else {
      toast.error('Senha incorreta!');
    }
  };

  // Função para editar um caso
  const handleEditCase = (caseItem: Project) => {
    setSelectedCase({...caseItem});
    setIsEditing(true);
  };

  // Função para criar um novo caso
  const handleCreateCase = () => {
    const newId = (Math.max(...cases.map(c => parseInt(c.id))) + 1).toString();
    setSelectedCase({
      id: newId,
      title: 'Novo Caso',
      thumbnail: '',
      modelUrl: '',
      htmlContent: '',
      exocadHtmlUrl: '',
      type: 'COROAS',
      description: '',
      category: 'Odontológico',
      galleryImages: []
    });
    setIsEditing(true);
  };

  // Função para salvar um caso (novo ou editado)
  const handleSaveCase = () => {
    if (!selectedCase) return;

    const updatedCases = isEditing && cases.some(c => c.id === selectedCase.id)
      ? cases.map(c => c.id === selectedCase.id ? selectedCase : c)
      : [...cases, selectedCase];

    setCases(updatedCases);
    setIsEditing(false);
    setSelectedCase(null);
    toast.success(`Caso ${selectedCase.title} salvo com sucesso!`);
  };

  // Função para excluir um caso
  const handleDeleteCase = () => {
    if (!selectedCase) return;

    const updatedCases = cases.filter(c => c.id !== selectedCase.id);
    setCases(updatedCases);
    setShowDeleteConfirm(false);
    setSelectedCase(null);
    toast.success(`Caso ${selectedCase.title} excluído com sucesso!`);
  };

  // Função para mover um caso para cima na ordem
  const handleMoveUp = (index: number) => {
    if (index === 0) return;
    const updatedCases = [...cases];
    [updatedCases[index], updatedCases[index - 1]] = [updatedCases[index - 1], updatedCases[index]];
    setCases(updatedCases);
  };

  // Função para mover um caso para baixo na ordem
  const handleMoveDown = (index: number) => {
    if (index === cases.length - 1) return;
    const updatedCases = [...cases];
    [updatedCases[index], updatedCases[index + 1]] = [updatedCases[index + 1], updatedCases[index]];
    setCases(updatedCases);
  };

  // Função para verificar se há URLs de blob nos dados
  const checkForBlobUrls = (projects: Project[]): boolean => {
    let hasBlobUrls = false;

    projects.forEach(project => {
      // Verificar thumbnail
      if (project.thumbnail && project.thumbnail.startsWith('blob:')) {
        hasBlobUrls = true;
      }

      // Verificar galleryImages
      if (project.galleryImages) {
        project.galleryImages.forEach(img => {
          if (img.startsWith('blob:')) {
            hasBlobUrls = true;
          }
        });
      }
    });

    return hasBlobUrls;
  };

  // Função para exportar os dados
  const handleExportData = () => {
    // Verificar se há URLs de blob
    const hasBlobUrls = checkForBlobUrls(cases);

    if (hasBlobUrls) {
      toast.warning('Atenção: Foram detectadas imagens temporárias (blob URLs) que não funcionarão após recarregar a página. Veja as instruções para mais detalhes.', {
        duration: 6000,
      });
    }

    // Formatar os dados para exportação
    const dataString = `
// Arquivo gerado automaticamente pelo Gerenciador de Casos
// Última atualização: ${new Date().toLocaleString()}

import { Project } from "@/components/ProjectGallery";

export const projectsData: Project[] = ${JSON.stringify(cases, null, 2)};
`;
    setExportedData(dataString);
    setShowExportDialog(true);
  };

  // Função para atualizar um campo do caso selecionado
  const updateSelectedCase = (field: string, value: any) => {
    if (!selectedCase) return;
    setSelectedCase({
      ...selectedCase,
      [field]: value
    });
  };

  // Função para adicionar uma imagem à galeria
  const handleAddGalleryImage = (url: string) => {
    if (!selectedCase) return;
    const galleryImages = selectedCase.galleryImages || [];
    setSelectedCase({
      ...selectedCase,
      galleryImages: [...galleryImages, url]
    });
  };

  // Função para remover uma imagem da galeria
  const handleRemoveGalleryImage = (index: number) => {
    if (!selectedCase || !selectedCase.galleryImages) return;
    const updatedImages = [...selectedCase.galleryImages];
    updatedImages.splice(index, 1);
    setSelectedCase({
      ...selectedCase,
      galleryImages: updatedImages
    });
  };

  // Renderizar a tela de login se não estiver autenticado
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center p-4">
        <div className="bg-gray-900 p-6 rounded-lg shadow-lg max-w-md w-full">
          <h1 className="text-2xl font-bold text-white mb-6">Acesso ao Painel de Administração</h1>
          <div className="space-y-4">
            <div>
              <Label htmlFor="password" className="text-white">Senha</Label>
              <Input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleAuthenticate()}
                className="bg-gray-800 border-gray-700 text-white"
              />
            </div>
            <Button onClick={handleAuthenticate} className="w-full">Acessar</Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-950 text-white">
      {/* Cabeçalho */}
      <header className="bg-gray-900 p-4 shadow-md">
        <div className="container mx-auto flex justify-between items-center">
          <h1 className="text-xl font-bold">Gerenciador de Casos - Portfólio Odontológico</h1>
          <div className="flex gap-2 items-center">
            <ThemeToggle variant="button" size="md" />
            <SaveDirectlyButton
              projects={cases}
              onSuccess={() => toast.success('Dados salvos com sucesso!')}
            />
            <Button onClick={handleExportData} variant="outline">
              <Download className="mr-2 h-4 w-4" />
              Exportar Código
            </Button>
            <Button onClick={() => setIsAuthenticated(false)} variant="destructive">Sair</Button>
          </div>
        </div>
      </header>

      {/* Conteúdo principal */}
      <main className="container mx-auto p-4">
        <div className="bg-blue-900/20 border border-blue-800/50 p-4 rounded-lg mb-6">
          <h3 className="text-base font-medium flex items-center mb-3 text-blue-300">
            <AlertTriangle className="h-5 w-5 mr-2 text-blue-400" />
            Importante: Servidor de Administração
          </h3>
          <div className="text-sm text-gray-300 space-y-2">
            <p>
              Para usar todas as funcionalidades do painel de administração (upload de arquivos, salvar dados diretamente),
              você precisa iniciar o servidor com o comando:
            </p>
            <div className="bg-gray-800 p-2 rounded font-mono text-sm">
              npm run server
            </div>
            <p>
              Se o servidor não estiver rodando, algumas funcionalidades ainda funcionarão em modo de compatibilidade,
              mas os dados não serão salvos permanentemente.
            </p>
          </div>
        </div>

        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold">Lista de Casos ({cases.length})</h2>
          <Button onClick={handleCreateCase} className="bg-green-600 hover:bg-green-700">
            <Plus className="mr-2 h-4 w-4" /> Novo Caso
          </Button>
        </div>

        {/* Lista de casos */}
        <div className="bg-gray-900 rounded-lg shadow-lg overflow-hidden">
          <table className="w-full">
            <thead>
              <tr className="bg-gray-800">
                <th className="p-3 text-left">ID</th>
                <th className="p-3 text-left">Miniatura</th>
                <th className="p-3 text-left">Título</th>
                <th className="p-3 text-left">Categoria</th>
                <th className="p-3 text-left">Ordem</th>
                <th className="p-3 text-right">Ações</th>
              </tr>
            </thead>
            <tbody>
              {cases.map((caseItem, index) => (
                <tr key={caseItem.id} className="border-t border-gray-800 hover:bg-gray-800/50">
                  <td className="p-3">{caseItem.id}</td>
                  <td className="p-3">
                    {caseItem.thumbnail ? (
                      <img
                        src={caseItem.thumbnail}
                        alt={caseItem.title}
                        className="w-16 h-12 object-cover rounded"
                      />
                    ) : (
                      <div className="w-16 h-12 bg-gray-800 rounded flex items-center justify-center">
                        <ImageIcon className="text-gray-600" size={20} />
                      </div>
                    )}
                  </td>
                  <td className="p-3">{caseItem.title}</td>
                  <td className="p-3">
                    <span className="px-2 py-1 bg-purple-900/30 border border-purple-800 rounded-full text-xs">
                      {caseItem.type}
                    </span>
                  </td>
                  <td className="p-3">
                    <div className="flex gap-1">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleMoveUp(index)}
                        disabled={index === 0}
                      >
                        <ArrowUp size={16} />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleMoveDown(index)}
                        disabled={index === cases.length - 1}
                      >
                        <ArrowDown size={16} />
                      </Button>
                    </div>
                  </td>
                  <td className="p-3 text-right">
                    <div className="flex gap-1 justify-end">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleEditCase(caseItem)}
                      >
                        <Edit size={16} />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="text-red-500 hover:text-red-400"
                        onClick={() => {
                          setSelectedCase(caseItem);
                          setShowDeleteConfirm(true);
                        }}
                      >
                        <Trash2 size={16} />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </main>

      {/* Modal de edição/criação de caso */}
      {isEditing && selectedCase && (
        <Dialog open={isEditing} onOpenChange={(open) => !open && setIsEditing(false)}>
          <DialogContent className="bg-gray-900 text-white border-gray-800 max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>{selectedCase.id ? `Editar Caso: ${selectedCase.title}` : 'Novo Caso'}</DialogTitle>
              <DialogDescription>
                Preencha os campos abaixo para {selectedCase.id ? 'editar' : 'criar'} o caso.
              </DialogDescription>
            </DialogHeader>

            <Tabs defaultValue="basic" className="w-full">
              <TabsList className="bg-gray-800">
                <TabsTrigger value="basic">Informações Básicas</TabsTrigger>
                <TabsTrigger value="content">Conteúdo</TabsTrigger>
                <TabsTrigger value="gallery">Galeria de Imagens</TabsTrigger>
                <TabsTrigger value="model">Modelo 3D</TabsTrigger>
                <TabsTrigger value="3d-advanced">3D Avançado</TabsTrigger>
              </TabsList>

              {/* Aba de informações básicas */}
              <TabsContent value="basic" className="space-y-4 mt-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="title">Título</Label>
                    <Input
                      id="title"
                      value={selectedCase.title}
                      onChange={(e) => updateSelectedCase('title', e.target.value)}
                      className="bg-gray-800 border-gray-700"
                    />
                  </div>
                  <div>
                    <Label htmlFor="type">Categoria</Label>
                    <Input
                      id="type"
                      value={selectedCase.type}
                      onChange={(e) => updateSelectedCase('type', e.target.value)}
                      className="bg-gray-800 border-gray-700"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="description">Descrição (Painel de Informações)</Label>
                  <Textarea
                    id="description"
                    value={selectedCase.description}
                    onChange={(e) => updateSelectedCase('description', e.target.value)}
                    className="bg-gray-800 border-gray-700 min-h-[100px]"
                  />
                </div>

                <div>
                  <Label htmlFor="thumbnail" className="mb-2 block">Miniatura do Caso</Label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Input
                        id="thumbnail"
                        value={selectedCase.thumbnail}
                        onChange={(e) => updateSelectedCase('thumbnail', e.target.value)}
                        className="bg-gray-800 border-gray-700"
                        placeholder="URL da imagem de miniatura"
                      />

                      {selectedCase.thumbnail && (
                        <div className="mt-3">
                          <p className="text-sm text-gray-400 mb-1">Pré-visualização:</p>
                          <div className="relative group w-40 h-30">
                            <img
                              src={selectedCase.thumbnail}
                              alt="Miniatura"
                              className="w-full h-full object-cover rounded-lg border border-gray-700"
                            />
                          </div>
                        </div>
                      )}
                    </div>

                    <div>
                      <FileUploader
                        onFileUploaded={(fileUrl) => {
                          updateSelectedCase('thumbnail', fileUrl);
                        }}
                        acceptedTypes="image/*"
                        label="Upload de Miniatura"
                      />
                    </div>
                  </div>
                </div>
              </TabsContent>

              {/* Conteúdo será implementado na próxima parte */}
              <TabsContent value="content" className="space-y-4 mt-4">
                <div>
                  <Label htmlFor="htmlContent" className="mb-2 block">Conteúdo HTML (Painel de Informações)</Label>
                  <HtmlEditor
                    value={selectedCase.htmlContent}
                    onChange={(value) => updateSelectedCase('htmlContent', value)}
                    height="200px"
                  />
                </div>

                <div className="mt-6">
                  <Label htmlFor="exocadHtmlUrl" className="mb-2 block">Arquivo HTML do Exocad</Label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Input
                        id="exocadHtmlUrl"
                        value={selectedCase.exocadHtmlUrl}
                        onChange={(e) => updateSelectedCase('exocadHtmlUrl', e.target.value)}
                        className="bg-gray-800 border-gray-700"
                        placeholder="/arquivosCasos/caso2/exemplo.html"
                      />
                      <p className="text-xs text-gray-400 mt-1">
                        Caminho para o arquivo HTML do Exocad (relativo à pasta public)
                      </p>
                    </div>
                    <div>
                      <FileUploader
                        onFileUploaded={(fileUrl) => {
                          // Em um ambiente real, você salvaria o arquivo no servidor
                          // e retornaria o caminho relativo
                          updateSelectedCase('exocadHtmlUrl', fileUrl);
                        }}
                        acceptedTypes=".html"
                        label="Upload do Arquivo HTML"
                      />
                    </div>
                  </div>
                </div>
              </TabsContent>

              {/* Galeria será implementada na próxima parte */}
              <TabsContent value="gallery" className="space-y-4 mt-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <div className="flex items-end gap-2">
                      <div className="flex-1">
                        <Label htmlFor="newImage">Adicionar Nova Imagem por URL</Label>
                        <Input
                          id="newImage"
                          placeholder="URL da imagem"
                          className="bg-gray-800 border-gray-700"
                        />
                      </div>
                      <Button
                        onClick={() => {
                          const input = document.getElementById('newImage') as HTMLInputElement;
                          if (input.value) {
                            handleAddGalleryImage(input.value);
                            input.value = '';
                          }
                        }}
                      >
                        Adicionar
                      </Button>
                    </div>
                  </div>

                  <div>
                    <FileUploader
                      onFileUploaded={(fileUrl) => {
                        handleAddGalleryImage(fileUrl);
                      }}
                      acceptedTypes="image/*"
                      label="Upload de Imagem"
                    />
                  </div>
                </div>

                <div className="mt-6">
                  <h3 className="text-lg font-medium mb-2">Imagens na Galeria ({selectedCase.galleryImages?.length || 0})</h3>

                  {(!selectedCase.galleryImages || selectedCase.galleryImages.length === 0) ? (
                    <div className="bg-gray-800/50 border border-gray-700 rounded-lg p-8 text-center">
                      <ImageIcon className="h-12 w-12 mx-auto mb-3 text-gray-600" />
                      <p className="text-gray-400">Nenhuma imagem adicionada à galeria</p>
                      <p className="text-gray-500 text-sm mt-1">Adicione imagens usando os campos acima</p>
                    </div>
                  ) : (
                    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                      {selectedCase.galleryImages.map((image, index) => (
                        <div key={index} className="relative group">
                          <img
                            src={image}
                            alt={`Imagem ${index + 1}`}
                            className="w-full aspect-[4/3] object-cover rounded-lg border border-gray-700"
                          />
                          <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center">
                            <Button
                              variant="destructive"
                              size="sm"
                              className="absolute top-2 right-2"
                              onClick={() => handleRemoveGalleryImage(index)}
                            >
                              <X size={16} className="mr-1" /> Remover
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </TabsContent>

              {/* Modelo 3D será implementado na próxima parte */}
              <TabsContent value="model" className="space-y-4 mt-4">
                <div>
                  <Label htmlFor="modelUrl">URL do Modelo 3D (Sketchfab)</Label>
                  <Textarea
                    id="modelUrl"
                    value={selectedCase.modelUrl}
                    onChange={(e) => {
                      // Extrair a URL do iframe se o usuário colar o código de incorporação completo
                      const value = e.target.value;
                      if (value.includes('<iframe') && value.includes('src=')) {
                        // Extrair a URL do atributo src do iframe
                        const srcMatch = value.match(/src="([^"]+)"/);
                        if (srcMatch && srcMatch[1]) {
                          // Remover parâmetros que possam interferir com nosso player
                          let cleanUrl = srcMatch[1];
                          // Remover parâmetros existentes que serão adicionados pelo nosso player
                          const paramsToRemove = ['autospin', 'autostart', 'ui_controls', 'ui_infos', 'ui_watermark',
                                                 'ui_stop', 'ui_inspector', 'ui_ar', 'ui_help', 'ui_settings',
                                                 'ui_vr', 'ui_fullscreen', 'ui_animations', 'transparent'];

                          paramsToRemove.forEach(param => {
                            const regex = new RegExp(`[?&]${param}=[^&]*`, 'g');
                            cleanUrl = cleanUrl.replace(regex, '');
                          });

                          // Remover & no início se houver
                          cleanUrl = cleanUrl.replace(/\?&/, '?');
                          // Remover ? no final se houver
                          cleanUrl = cleanUrl.replace(/\?$/, '');

                          updateSelectedCase('modelUrl', cleanUrl);
                        } else {
                          updateSelectedCase('modelUrl', value);
                        }
                      } else {
                        updateSelectedCase('modelUrl', value);
                      }
                    }}
                    className="bg-gray-800 border-gray-700 min-h-[100px] font-mono text-sm"
                  />
                  <p className="text-xs text-gray-400 mt-1">
                    Cole o código de incorporação completo do Sketchfab aqui. O sistema extrairá automaticamente a URL do modelo.
                  </p>
                  <p className="text-xs text-gray-400 mt-1">
                    Exemplo: <code className="bg-gray-700 px-1 py-0.5 rounded">https://sketchfab.com/models/6edc70c58a0f428ba75c14e48348e983/embed?autospin=1&autostart=1&transparent=1</code>
                  </p>
                </div>

                {selectedCase.modelUrl && (
                  <div className="mt-4">
                    <h3 className="text-lg font-medium mb-2">Pré-visualização do Modelo</h3>
                    <div className="bg-gray-800 p-4 rounded">
                      <div className="aspect-video">
                        <iframe
                          title={`Pré-visualização - ${selectedCase.title}`}
                          src={`${selectedCase.modelUrl}${selectedCase.modelUrl.includes('?') ? '&' : '?'}autospin=1&autostart=1&ui_theme=dark`}
                          className="w-full h-full"
                          frameBorder="0"
                          allow="autoplay; fullscreen"
                        />
                      </div>
                    </div>
                    <p className="text-xs text-gray-400 mt-1">
                      URL do modelo: <code className="bg-gray-700 px-1 py-0.5 rounded">{selectedCase.modelUrl}</code>
                    </p>
                  </div>
                )}
              </TabsContent>

              {/* Nova aba para 3D Avançado */}
              <TabsContent value="3d-advanced" className="space-y-4 mt-4">
                <div className="bg-blue-900/20 border border-blue-800/50 p-4 rounded-lg mb-6">
                  <h3 className="text-base font-medium flex items-center mb-3 text-blue-300">
                    <AlertTriangle className="h-5 w-5 mr-2 text-blue-400" />
                    Visualizador 3D Híbrido
                  </h3>
                  <div className="text-sm text-gray-300 space-y-2">
                    <p>
                      O sistema agora suporta visualização 3D nativa usando Three.js além do Sketchfab.
                      Para usar o visualizador nativo, você pode:
                    </p>
                    <ul className="list-disc ml-5 space-y-1">
                      <li>Fazer upload de modelos GLB/GLTF para melhor performance</li>
                      <li>Ter controle total sobre iluminação e materiais</li>
                      <li>Implementar funcionalidades como toggle de malhas</li>
                      <li>Otimização automática para dispositivos móveis</li>
                    </ul>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Configurações do modelo nativo */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Modelo 3D Nativo (GLB/GLTF)</h3>

                    <div>
                      <Label htmlFor="nativeModelUrl">URL do Modelo GLB/GLTF</Label>
                      <Input
                        id="nativeModelUrl"
                        value={selectedCase.nativeModelUrl || ''}
                        onChange={(e) => updateSelectedCase('nativeModelUrl', e.target.value)}
                        className="bg-gray-800 border-gray-700"
                        placeholder="/models/caso1.glb"
                      />
                      <p className="text-xs text-gray-400 mt-1">
                        Caminho para o arquivo GLB/GLTF (melhor performance que Sketchfab)
                      </p>
                    </div>

                    <div>
                      <FileUploader
                        onFileUploaded={(fileUrl) => {
                          updateSelectedCase('nativeModelUrl', fileUrl);
                        }}
                        acceptedTypes=".glb,.gltf"
                        label="Upload de Modelo 3D"
                      />
                    </div>

                    <div className="space-y-3">
                      <Label>Configurações do Modelo</Label>

                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="enableAutoRotate"
                          checked={selectedCase.modelSettings?.autoRotate !== false}
                          onChange={(e) => updateSelectedCase('modelSettings', {
                            ...selectedCase.modelSettings,
                            autoRotate: e.target.checked
                          })}
                          className="rounded"
                        />
                        <Label htmlFor="enableAutoRotate" className="text-sm">Rotação automática</Label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="enableShadows"
                          checked={selectedCase.modelSettings?.shadows !== false}
                          onChange={(e) => updateSelectedCase('modelSettings', {
                            ...selectedCase.modelSettings,
                            shadows: e.target.checked
                          })}
                          className="rounded"
                        />
                        <Label htmlFor="enableShadows" className="text-sm">Sombras</Label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="enableEnvironment"
                          checked={selectedCase.modelSettings?.environment !== false}
                          onChange={(e) => updateSelectedCase('modelSettings', {
                            ...selectedCase.modelSettings,
                            environment: e.target.checked
                          })}
                          className="rounded"
                        />
                        <Label htmlFor="enableEnvironment" className="text-sm">Iluminação de ambiente</Label>
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="modelScale">Escala do Modelo</Label>
                      <Input
                        id="modelScale"
                        type="number"
                        step="0.1"
                        min="0.1"
                        max="5"
                        value={selectedCase.modelSettings?.scale || 1}
                        onChange={(e) => updateSelectedCase('modelSettings', {
                          ...selectedCase.modelSettings,
                          scale: parseFloat(e.target.value) || 1
                        })}
                        className="bg-gray-800 border-gray-700"
                      />
                    </div>
                  </div>

                  {/* Preview do modelo nativo */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Preview do Modelo Nativo</h3>

                    {selectedCase.nativeModelUrl ? (
                      <div className="bg-gray-800 rounded-lg overflow-hidden">
                        <div className="aspect-square relative">
                          <HybridModelViewer
                            modelUrl={selectedCase.nativeModelUrl}
                            title={selectedCase.title}
                            forceNative={true}
                            className="w-full h-full"
                          />
                        </div>
                        <div className="p-3 bg-gray-900">
                          <p className="text-xs text-gray-400">
                            Preview do modelo nativo - Use os controles para testar a interação
                          </p>
                        </div>
                      </div>
                    ) : (
                      <div className="bg-gray-800/50 border border-gray-700 rounded-lg p-8 text-center aspect-square flex flex-col items-center justify-center">
                        <div className="w-16 h-16 bg-gray-700 rounded-lg flex items-center justify-center mb-3">
                          <span className="text-2xl">🎯</span>
                        </div>
                        <p className="text-gray-400 mb-2">Nenhum modelo nativo</p>
                        <p className="text-gray-500 text-sm">Adicione um arquivo GLB/GLTF para preview</p>
                      </div>
                    )}

                    <div className="bg-green-900/20 border border-green-800/50 p-3 rounded-lg">
                      <h4 className="text-sm font-medium text-green-300 mb-2">Vantagens do Modelo Nativo:</h4>
                      <ul className="text-xs text-gray-300 space-y-1">
                        <li>• Performance superior em dispositivos móveis</li>
                        <li>• Controle total sobre iluminação e materiais</li>
                        <li>• Toggle de partes do modelo (superior/inferior)</li>
                        <li>• Carregamento mais rápido</li>
                        <li>• Funciona offline</li>
                      </ul>
                    </div>
                  </div>
                </div>

                <div className="border-t border-gray-700 pt-4">
                  <h3 className="text-lg font-medium mb-3">Configurações de Fallback</h3>
                  <div className="bg-yellow-900/20 border border-yellow-800/50 p-4 rounded-lg">
                    <p className="text-sm text-gray-300 mb-2">
                      <strong>Sistema Híbrido:</strong> Se um modelo nativo (GLB/GLTF) estiver disponível,
                      ele será usado automaticamente. Caso contrário, o sistema fará fallback para o Sketchfab.
                    </p>
                    <p className="text-xs text-gray-400">
                      Isso garante compatibilidade total enquanto oferece a melhor experiência possível.
                    </p>
                  </div>
                </div>
              </TabsContent>
            </Tabs>

            <DialogFooter>
              <Button variant="outline" onClick={() => setIsEditing(false)}>Cancelar</Button>
              <Button onClick={handleSaveCase}>Salvar Caso</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* Modal de confirmação de exclusão */}
      <Dialog open={showDeleteConfirm} onOpenChange={setShowDeleteConfirm}>
        <DialogContent className="bg-gray-900 text-white border-gray-800">
          <DialogHeader>
            <DialogTitle>Confirmar Exclusão</DialogTitle>
            <DialogDescription>
              Tem certeza que deseja excluir o caso "{selectedCase?.title}"? Esta ação não pode ser desfeita.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteConfirm(false)}>Cancelar</Button>
            <Button variant="destructive" onClick={handleDeleteCase}>Excluir</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Modal de exportação de dados */}
      <Dialog open={showExportDialog} onOpenChange={setShowExportDialog}>
        <DialogContent className="bg-gray-900 text-white border-gray-800 max-w-4xl">
          <DialogHeader>
            <DialogTitle>Exportar Dados</DialogTitle>
            <DialogDescription>
              Siga as instruções abaixo para salvar suas alterações no arquivo projectsData.ts
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6">
            <div className="bg-blue-900/20 border border-blue-800/50 p-4 rounded-lg">
              <h3 className="text-base font-medium flex items-center mb-3 text-blue-300">
                <AlertTriangle className="h-5 w-5 mr-2 text-blue-400" />
                Importante: Duas Formas de Salvar
              </h3>
              <div className="text-sm text-gray-300 space-y-2">
                <p>
                  Você pode salvar suas alterações de duas formas:
                </p>
                <ol className="list-decimal ml-5 space-y-1">
                  <li><strong>Salvar no Servidor:</strong> Use o botão "Salvar no Servidor" para salvar diretamente no arquivo projectsData.ts.</li>
                  <li><strong>Exportar Manualmente:</strong> Copie o código abaixo e substitua manualmente o conteúdo do arquivo.</li>
                </ol>
              </div>
            </div>

            <div className="bg-gray-800/50 border border-gray-700 p-4 rounded-lg">
              <h3 className="text-sm font-medium flex items-center mb-3 text-white">
                <span className="bg-blue-600 text-white rounded-full w-5 h-5 inline-flex items-center justify-center mr-2">1</span>
                Salvar Diretamente no Servidor
              </h3>

              <p className="text-sm text-gray-400 mb-3">
                Clique no botão abaixo para salvar os dados diretamente no arquivo projectsData.ts através do servidor local.
                Esta é a opção mais simples e as alterações serão aplicadas imediatamente.
              </p>

              <div className="flex justify-end">
                <SaveDirectlyButton
                  projects={cases}
                  onSuccess={() => {
                    setShowExportDialog(false);
                    toast.success('Dados salvos com sucesso no arquivo projectsData.ts!');
                  }}
                />
              </div>
            </div>

            <div className="bg-gray-800/50 border border-gray-700 p-4 rounded-lg">
              <h3 className="text-sm font-medium flex items-center mb-3 text-white">
                <span className="bg-blue-600 text-white rounded-full w-5 h-5 inline-flex items-center justify-center mr-2">2</span>
                Exportar Código Manualmente
              </h3>

              <p className="text-sm text-gray-400 mb-3">
                Alternativamente, você pode copiar o código abaixo e substituir manualmente o conteúdo do arquivo projectsData.ts.
              </p>

              <div className="bg-gray-800 p-4 rounded-md mb-3">
                <pre className="text-xs overflow-auto max-h-[300px]">{exportedData}</pre>
              </div>

              <div className="flex justify-end">
                <Button
                  onClick={() => {
                    navigator.clipboard.writeText(exportedData);
                    toast.success('Código copiado para a área de transferência!');
                  }}
                >
                  <Copy className="h-4 w-4 mr-2" />
                  Copiar para Área de Transferência
                </Button>
              </div>
            </div>



            <div className="bg-yellow-900/20 border border-yellow-800/50 p-4 rounded-lg">
              <h3 className="text-sm font-medium flex items-center mb-2 text-yellow-300">
                <AlertTriangle className="h-4 w-4 mr-2 text-yellow-400" />
                Nota sobre Imagens
              </h3>
              <p className="text-sm text-gray-300">
                Se você fez upload de novas imagens, elas estão sendo referenciadas como URLs temporárias (começando com "blob:").
                Estas URLs não funcionarão após recarregar a página. Para imagens permanentes, você deve:
              </p>
              <ol className="list-decimal ml-5 mt-2 text-sm text-gray-300 space-y-1">
                <li>Salvar as imagens na pasta <code className="bg-gray-700 px-1 py-0.5 rounded">public/</code> do projeto</li>
                <li>Atualizar as URLs para apontar para os arquivos salvos (ex: "/images/minha-imagem.jpg")</li>
              </ol>
            </div>
          </div>

          <DialogFooter className="mt-4">
            <Button onClick={() => setShowExportDialog(false)}>Entendi</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Admin;
