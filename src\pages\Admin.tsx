import React, { useState, useEffect } from 'react';
import { projectsData } from '@/data/projectsData';
import { X, Save, Plus, Edit, Trash2, Eye, ArrowUp, ArrowDown, Upload, FileText, Image as ImageIcon, ExternalLink, Download, Copy, AlertTriangle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from '@/components/ui/sonner';
import { Project } from '@/components/ProjectGallery';
import HtmlEditor from '@/components/admin/HtmlEditor';
import FileUploader from '@/components/admin/FileUploader';
import SaveDirectlyButton from '@/components/admin/SaveDirectlyButton';
import HybridModelViewer from '@/components/3d/HybridModelViewer';
import ThemeToggle from '@/components/ui/ThemeToggle';

// Senha para acessar o painel de administração (em produção, use um método mais seguro)
const ADMIN_PASSWORD = 'admin123';

const AdminFixed = () => {
  // Estado de autenticação
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [password, setPassword] = useState('');

  // Estado dos casos
  const [cases, setCases] = useState<Project[]>([]);
  const [selectedCase, setSelectedCase] = useState<Project | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // Estado para exportação
  const [exportedData, setExportedData] = useState('');
  const [showExportDialog, setShowExportDialog] = useState(false);

  // Inicializar os casos a partir dos dados existentes
  useEffect(() => {
    setCases([...projectsData]);
  }, []);

  // Função para autenticar
  const handleAuthenticate = () => {
    if (password === ADMIN_PASSWORD) {
      setIsAuthenticated(true);
      toast.success('Autenticado com sucesso!');
    } else {
      toast.error('Senha incorreta!');
    }
  };

  // Função para editar um caso
  const handleEditCase = (caseItem: Project) => {
    setSelectedCase({...caseItem});
    setIsEditing(true);
  };

  // Função para criar um novo caso
  const handleCreateCase = () => {
    const newId = (Math.max(...cases.map(c => parseInt(c.id))) + 1).toString();
    setSelectedCase({
      id: newId,
      title: 'Novo Caso',
      thumbnail: '',
      modelUrl: '',
      htmlContent: '',
      exocadHtmlUrl: '',
      type: 'COROAS',
      description: '',
      category: 'Odontológico',
      galleryImages: []
    });
    setIsEditing(true);
  };

  // Função para salvar um caso (novo ou editado)
  const handleSaveCase = () => {
    if (!selectedCase) return;

    const updatedCases = isEditing && cases.some(c => c.id === selectedCase.id)
      ? cases.map(c => c.id === selectedCase.id ? selectedCase : c)
      : [...cases, selectedCase];

    setCases(updatedCases);
    setIsEditing(false);
    setSelectedCase(null);
    toast.success(`Caso ${selectedCase.title} salvo com sucesso!`);
  };

  // Função para excluir um caso
  const handleDeleteCase = () => {
    if (!selectedCase) return;

    const updatedCases = cases.filter(c => c.id !== selectedCase.id);
    setCases(updatedCases);
    setShowDeleteConfirm(false);
    setSelectedCase(null);
    toast.success(`Caso ${selectedCase.title} excluído com sucesso!`);
  };

  // Função para mover um caso para cima na ordem
  const handleMoveUp = (index: number) => {
    if (index === 0) return;
    const updatedCases = [...cases];
    [updatedCases[index], updatedCases[index - 1]] = [updatedCases[index - 1], updatedCases[index]];
    setCases(updatedCases);
  };

  // Função para mover um caso para baixo na ordem
  const handleMoveDown = (index: number) => {
    if (index === cases.length - 1) return;
    const updatedCases = [...cases];
    [updatedCases[index], updatedCases[index + 1]] = [updatedCases[index + 1], updatedCases[index]];
    setCases(updatedCases);
  };

  // Função para exportar os dados
  const handleExportData = () => {
    const dataString = `
// Arquivo gerado automaticamente pelo Gerenciador de Casos
// Última atualização: ${new Date().toLocaleString()}

import { Project } from "@/components/ProjectGallery";

export const projectsData: Project[] = ${JSON.stringify(cases, null, 2)};
`;
    setExportedData(dataString);
    setShowExportDialog(true);
  };

  // Função para atualizar um campo do caso selecionado
  const updateSelectedCase = (field: string, value: any) => {
    if (!selectedCase) return;
    setSelectedCase({
      ...selectedCase,
      [field]: value
    });
  };

  // Função para adicionar uma imagem à galeria
  const handleAddGalleryImage = (url: string) => {
    if (!selectedCase) return;
    const galleryImages = selectedCase.galleryImages || [];
    setSelectedCase({
      ...selectedCase,
      galleryImages: [...galleryImages, url]
    });
  };

  // Função para remover uma imagem da galeria
  const handleRemoveGalleryImage = (index: number) => {
    if (!selectedCase || !selectedCase.galleryImages) return;
    const updatedImages = [...selectedCase.galleryImages];
    updatedImages.splice(index, 1);
    setSelectedCase({
      ...selectedCase,
      galleryImages: updatedImages
    });
  };

  // Renderizar a tela de login se não estiver autenticado
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center p-4">
        <div className="bg-gray-900 p-6 rounded-lg shadow-lg max-w-md w-full">
          <h1 className="text-2xl font-bold text-white mb-6">Acesso ao Painel de Administração</h1>
          <div className="space-y-4">
            <div>
              <Label htmlFor="password" className="text-white">Senha</Label>
              <Input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleAuthenticate()}
                className="bg-gray-800 border-gray-700 text-white"
              />
            </div>
            <Button onClick={handleAuthenticate} className="w-full">Acessar</Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-950 text-white">
      {/* Cabeçalho */}
      <header className="bg-gray-900 p-4 shadow-md">
        <div className="container mx-auto flex justify-between items-center">
          <h1 className="text-xl font-bold">Gerenciador de Casos - Portfólio Odontológico</h1>
          <div className="flex gap-2 items-center">
            <ThemeToggle variant="button" size="md" />
            <SaveDirectlyButton
              projects={cases}
              onSuccess={() => toast.success('Dados salvos com sucesso!')}
            />
            <Button onClick={handleExportData} variant="outline">
              <Download className="mr-2 h-4 w-4" />
              Exportar Código
            </Button>
            <Button onClick={() => setIsAuthenticated(false)} variant="destructive">Sair</Button>
          </div>
        </div>
      </header>

      {/* Conteúdo principal */}
      <main className="container mx-auto p-4">
        <div className="bg-blue-900/20 border border-blue-800/50 p-4 rounded-lg mb-6">
          <h3 className="text-base font-medium flex items-center mb-3 text-blue-300">
            <AlertTriangle className="h-5 w-5 mr-2 text-blue-400" />
            Sistema STL do EXOCAD Funcionando!
          </h3>
          <div className="text-sm text-gray-300 space-y-2">
            <p>
              ✅ Suporte completo a arquivos STL do EXOCAD implementado
            </p>
            <p>
              ✅ Múltiplos modelos por caso com controles individuais
            </p>
            <p>
              ✅ Controles flutuantes visíveis no portfólio principal
            </p>
          </div>
        </div>

        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold">Lista de Casos ({cases.length})</h2>
          <Button onClick={handleCreateCase} className="bg-green-600 hover:bg-green-700">
            <Plus className="mr-2 h-4 w-4" /> Novo Caso
          </Button>
        </div>

        {/* Lista de casos simplificada */}
        <div className="bg-gray-900 rounded-lg shadow-lg overflow-hidden">
          <table className="w-full">
            <thead>
              <tr className="bg-gray-800">
                <th className="p-3 text-left">ID</th>
                <th className="p-3 text-left">Título</th>
                <th className="p-3 text-left">Categoria</th>
                <th className="p-3 text-right">Ações</th>
              </tr>
            </thead>
            <tbody>
              {cases.map((caseItem, index) => (
                <tr key={caseItem.id} className="border-t border-gray-800 hover:bg-gray-800/50">
                  <td className="p-3">{caseItem.id}</td>
                  <td className="p-3">{caseItem.title}</td>
                  <td className="p-3">
                    <span className="px-2 py-1 bg-purple-900/30 border border-purple-800 rounded-full text-xs">
                      {caseItem.type}
                    </span>
                  </td>
                  <td className="p-3 text-right">
                    <div className="flex gap-1 justify-end">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleEditCase(caseItem)}
                      >
                        <Edit size={16} />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </main>

      {/* Modal de exportação */}
      <Dialog open={showExportDialog} onOpenChange={setShowExportDialog}>
        <DialogContent className="bg-gray-900 text-white border-gray-800 max-w-4xl">
          <DialogHeader>
            <DialogTitle>Código Exportado</DialogTitle>
            <DialogDescription>
              Copie o código abaixo e substitua o conteúdo do arquivo src/data/projectsData.ts
            </DialogDescription>
          </DialogHeader>
          <div className="mt-4">
            <Textarea
              value={exportedData}
              readOnly
              className="bg-gray-800 border-gray-700 font-mono text-sm min-h-[400px]"
            />
          </div>
          <DialogFooter className="mt-4">
            <Button onClick={() => setShowExportDialog(false)}>Entendi</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AdminFixed;
