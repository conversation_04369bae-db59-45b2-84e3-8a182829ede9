
import React from 'react';
import { cn } from '@/lib/utils';
import { useIsMobile } from '@/hooks/use-mobile';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { User, MessageCircle } from 'lucide-react';

interface HeaderProps {
  openContact: () => void;
  openAbout: () => void; // Added new prop for opening About modal
  className?: string;
  currentPage?: string;
}

const Header = ({ openContact, openAbout, className }: HeaderProps) => {
  const isMobile = useIsMobile();
  const navigate = useNavigate();

  return (
    <header className={cn(
      "w-full py-6 flex justify-between items-center fixed top-0 left-0 right-0 z-10",
      "bg-black/5 backdrop-blur-md",
      isMobile ? "px-4" : "px-6", // Ajustar padding horizontal no mobile para alinhar com outros elementos
      className
    )}>
      {/* Desktop version */}
      <div
        className="hidden md:flex md:items-center text-3xl font-medium tracking-tighter cursor-pointer"
        onClick={() => navigate('/')}
      >
        <span className="font-semibold bg-gradient-to-r from-purple-400 to-blue-500 text-transparent bg-clip-text">FORM & FUNCTION</span>
        <span className="text-sm font-light text-white/60 uppercase ml-0.5">/ JONHNATAS LIMA</span>
      </div>

      {/* Desktop navigation */}
      <nav className="hidden md:flex md:items-center md:space-x-6">
        <Button
          onClick={openAbout}
          variant="ghost"
          className="text-sm font-medium relative overflow-hidden group px-2 py-1 h-auto uppercase text-white hover:bg-white/5"
        >
          <span className="relative z-10 flex items-center">
            <User className="h-3.5 w-3.5 mr-2 opacity-70 group-hover:opacity-100" />
            SOBRE MIM
          </span>
          <span className="absolute bottom-0 left-0 w-full h-0.5 bg-white scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></span>
        </Button>

        <Button
          onClick={openContact}
          variant="ghost"
          className="text-sm font-medium relative overflow-hidden group px-2 py-1 h-auto uppercase text-white hover:bg-white/5"
        >
          <span className="relative z-10 flex items-center">
            <MessageCircle className="h-3.5 w-3.5 mr-2 opacity-70 group-hover:opacity-100" />
            CONTATO
          </span>
          <span className="absolute bottom-0 left-0 w-full h-0.5 bg-white scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></span>
        </Button>
      </nav>

      {/* Mobile version - horizontal blocks layout */}
      <div className="md:hidden flex flex-col w-full space-y-1 relative">
        {/* Linha 1: FORM & + JONHNATAS + SOBRE MIM */}
        <div className="flex items-center w-full justify-between">
          <div className="flex">
            {/* Coluna da esquerda com FORM & e FUNCTION */}
            <div className="flex flex-col justify-between pr-3 h-[52px]">
              <span className="font-semibold text-base bg-gradient-to-r from-purple-400 to-blue-500 text-transparent bg-clip-text">FORM &</span>
              <span className="font-semibold text-base bg-gradient-to-r from-blue-500 to-purple-400 text-transparent bg-clip-text">FUNCTION</span>
            </div>

            {/* Barra vertical */}
            <div className="w-[1.5px] bg-white/30 mx-2 self-stretch"></div>

            {/* Coluna da direita com JONHNATAS e LIMA */}
            <div className="flex flex-col justify-between pl-1 h-[52px]">
              <span className="text-xs font-light text-white/60 uppercase">JONHNATAS</span>
              <span className="text-xs font-light text-white/60 uppercase">LIMA</span>
            </div>
          </div>

          {/* Coluna dos botões */}
          <div className="flex flex-col justify-between items-end h-[52px]">
            <Button
              onClick={openAbout}
              variant="ghost"
              className="text-[10px] font-medium px-1 py-0.5 h-auto uppercase text-white hover:bg-white/5"
            >
              <span className="flex items-center">
                <User className="h-3 w-3 mr-1 opacity-70" />
                SOBRE MIM
              </span>
            </Button>

            <Button
              onClick={openContact}
              variant="ghost"
              className="text-[10px] font-medium px-1 py-0.5 h-auto uppercase text-white hover:bg-white/5"
            >
              <span className="flex items-center">
                <MessageCircle className="h-3 w-3 mr-1 opacity-70" />
                CONTATO
              </span>
            </Button>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
