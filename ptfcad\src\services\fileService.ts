/**
 * Serviço para operações de arquivo
 *
 * Este serviço fornece funções para salvar dados diretamente em arquivos.
 * Em um ambiente de produção, essas operações seriam realizadas no servidor.
 * Para esta demonstração, estamos simulando o comportamento.
 */

import { Project } from "@/components/ProjectGallery";
import { toast } from "@/components/ui/sonner";

/**
 * Salva os dados dos projetos diretamente no arquivo projectsData.ts
 *
 * @param projects Array de projetos para salvar
 * @returns Promise que resolve para true se bem-sucedido, ou rejeita com erro
 */
export const saveProjectsData = async (projects: Project[]): Promise<boolean> => {
  try {
    // Enviar os dados para o servidor
    const response = await fetch('http://localhost:3001/api/save-projects-data', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ projects }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Falha ao salvar os dados no servidor');
    }

    return true;
  } catch (error) {
    console.error('Erro ao salvar os dados:', error);
    throw error;
  }
};

/**
 * Verifica se o ambiente atual suporta a funcionalidade de salvar diretamente
 *
 * @returns boolean indicando se o ambiente suporta a funcionalidade
 */
export const canSaveDirectly = (): boolean => {
  // Verificar se o servidor está disponível
  return true; // Assumimos que o servidor está disponível
};

/**
 * Faz upload de um arquivo para o servidor
 *
 * @param file Arquivo a ser enviado
 * @param caseId ID do caso (opcional)
 * @returns Promise com o caminho do arquivo no servidor
 */
export const uploadFile = async (file: File, caseId?: string): Promise<string> => {
  try {
    // Se estamos em desenvolvimento e o servidor não está disponível,
    // vamos criar uma URL temporária para o arquivo
    try {
      await fetch('http://localhost:3001/api/list-files?folder=images', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
        // Adicionar um timeout para não bloquear a interface
        signal: AbortSignal.timeout(1000)
      });
    } catch (error) {
      console.warn('Servidor não disponível, usando URL temporária');
      // Retornar uma URL temporária (blob) para o arquivo
      return URL.createObjectURL(file);
    }

    const formData = new FormData();
    formData.append('file', file);

    if (caseId) {
      formData.append('caseId', caseId);
    }

    const response = await fetch('http://localhost:3001/api/upload', {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Falha ao fazer upload do arquivo');
    }

    const data = await response.json();
    return data.filePath;
  } catch (error) {
    console.error('Erro ao fazer upload do arquivo:', error);
    throw error;
  }
};

/**
 * Lista os arquivos em uma pasta
 *
 * @param folder Pasta a ser listada
 * @returns Promise com a lista de arquivos
 */
export const listFiles = async (folder: string): Promise<string[]> => {
  try {
    const response = await fetch(`http://localhost:3001/api/list-files?folder=${encodeURIComponent(folder)}`);

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Falha ao listar arquivos');
    }

    const data = await response.json();
    return data.files;
  } catch (error) {
    console.error('Erro ao listar arquivos:', error);
    return [];
  }
};
