import { useState, useEffect } from 'react';

/**
 * Hook para verificar se a aplicação está rodando em modo de desenvolvimento
 * @returns {boolean} true se estiver em modo de desenvolvimento
 */
export function useDevMode(): boolean {
  const [isDevMode, setIsDevMode] = useState(false);

  useEffect(() => {
    // Verificar se estamos em modo de desenvolvimento
    // process.env.NODE_ENV é definido pelo Vite/React durante o build
    setIsDevMode(process.env.NODE_ENV === 'development');
  }, []);

  return isDevMode;
}
