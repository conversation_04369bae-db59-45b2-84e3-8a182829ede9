import React, { useState, useRef } from 'react';
import { Upload, X, Check, Loader2, FolderOpen } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from '@/components/ui/sonner';
import ImageSelector from './ImageSelector';
import { uploadFile } from '@/services/fileService';

interface FileUploaderProps {
  onFileUploaded: (fileUrl: string) => void;
  acceptedTypes?: string;
  label?: string;
  uploadPath?: string;
}

const FileUploader: React.FC<FileUploaderProps> = ({
  onFileUploaded,
  acceptedTypes = "image/*",
  label = "Upload de Arquivo",
  uploadPath = "uploads"
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isImageSelectorOpen, setIsImageSelectorOpen] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      handleFile(e.dataTransfer.files[0]);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      handleFile(e.target.files[0]);
    }
  };

  const handleFile = async (file: File) => {
    // Verificar o tipo do arquivo de forma mais robusta
    const fileName = file.name.toLowerCase();
    const fileExtension = fileName.split('.').pop();

    // Lista de extensões aceitas baseada no acceptedTypes
    const acceptedExtensions: string[] = [];

    if (acceptedTypes.includes('.stl')) acceptedExtensions.push('stl');
    if (acceptedTypes.includes('.glb')) acceptedExtensions.push('glb');
    if (acceptedTypes.includes('.gltf')) acceptedExtensions.push('gltf');
    if (acceptedTypes.includes('.obj')) acceptedExtensions.push('obj');
    if (acceptedTypes.includes('image/*') || acceptedTypes.includes('.jpg') || acceptedTypes.includes('.jpeg')) {
      acceptedExtensions.push('jpg', 'jpeg', 'png', 'gif', 'webp');
    }
    if (acceptedTypes.includes('.html')) acceptedExtensions.push('html');

    // Se não há extensões específicas e é image/*, aceitar tipos de imagem
    if (acceptedExtensions.length === 0 && acceptedTypes.includes('image/*')) {
      if (!file.type.startsWith('image/')) {
        toast.error(`Tipo de arquivo não suportado. Por favor, envie uma imagem.`);
        return;
      }
    } else if (acceptedExtensions.length > 0) {
      if (!fileExtension || !acceptedExtensions.includes(fileExtension)) {
        toast.error(`Tipo de arquivo não suportado. Extensões aceitas: ${acceptedExtensions.join(', ')}`);
        return;
      }
    } else {
      // Fallback para validação original
      if (!file.type.match(acceptedTypes.replace(/\*/g, '.*'))) {
        toast.error(`Tipo de arquivo não suportado. Por favor, envie ${acceptedTypes}`);
        return;
      }
    }

    // Iniciar upload
    setIsUploading(true);
    setUploadProgress(10);

    try {
      // Simular progresso enquanto o upload está em andamento
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          const newProgress = prev + 5;
          return newProgress < 90 ? newProgress : prev;
        });
      }, 200);

      // Verificar se o servidor está disponível
      try {
        await fetch('http://localhost:3001/api/list-files?folder=images');
      } catch (error) {
        clearInterval(progressInterval);
        throw new Error('Servidor não está disponível. Certifique-se de que o servidor está rodando com "npm run server".');
      }

      // Fazer upload do arquivo para o servidor
      const filePath = await uploadFile(file);

      // Limpar o intervalo e definir o progresso como 100%
      clearInterval(progressInterval);
      setUploadProgress(100);

      // Pequeno atraso para mostrar o 100%
      setTimeout(() => {
        onFileUploaded(filePath);
        setIsUploading(false);
        setUploadProgress(0);

        toast.success(`Arquivo ${file.name} enviado com sucesso!`);
      }, 300);
    } catch (error) {
      console.error('Erro ao fazer upload:', error);
      toast.error(`Erro ao fazer upload do arquivo: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  // Função para lidar com a seleção de imagem existente
  const handleSelectExistingImage = (imagePath: string) => {
    onFileUploaded(imagePath);
    toast.success('Imagem selecionada com sucesso!');
  };

  return (
    <div className="space-y-2">
      <div className="flex justify-between items-center">
        <Label>{label}</Label>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsImageSelectorOpen(true)}
          className="text-xs"
        >
          <FolderOpen className="h-3 w-3 mr-1" />
          Selecionar Existente
        </Button>
      </div>

      <div
        className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ${
          isDragging ? 'border-blue-500 bg-blue-500/10' : 'border-gray-700 hover:border-gray-500'
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={() => fileInputRef.current?.click()}
      >
        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileChange}
          accept={acceptedTypes}
          className="hidden"
        />

        {isUploading ? (
          <div className="py-4">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2 text-blue-500" />
            <p className="text-sm text-gray-400">Enviando arquivo... {uploadProgress}%</p>
            <div className="w-full bg-gray-700 rounded-full h-2 mt-2">
              <div
                className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${uploadProgress}%` }}
              />
            </div>
          </div>
        ) : (
          <div className="py-4">
            <Upload className="h-8 w-8 mx-auto mb-2 text-gray-500" />
            <p className="text-sm text-gray-400">
              Arraste e solte um arquivo aqui, ou clique para selecionar
            </p>
            <p className="text-xs text-gray-500 mt-1">
              Tipos aceitos: {acceptedTypes}
            </p>
          </div>
        )}
      </div>

      <div className="flex items-center space-x-2 text-xs text-gray-500">
        <div className="w-1 h-1 bg-blue-400 rounded-full"></div>
        <p>
          <strong>Dica:</strong> Para usar imagens permanentes, clique em "Selecionar Existente" e escolha uma imagem da pasta do projeto.
        </p>
      </div>

      {/* Seletor de imagens existentes */}
      <ImageSelector
        isOpen={isImageSelectorOpen}
        onOpenChange={setIsImageSelectorOpen}
        onSelectImage={handleSelectExistingImage}
        title={`Selecionar ${label.toLowerCase()}`}
      />
    </div>
  );
};

export default FileUploader;
