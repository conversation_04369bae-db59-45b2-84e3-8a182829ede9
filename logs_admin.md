client:495 [vite] connecting...
client:614 [vite] connected.
react-dom.development.js:29895 Download the React DevTools for a better development experience: https://reactjs.org/link/react-devtools
gerenciador-casos:1 [DOM] Password field is not contained in a form: (More info: https://www.chromium.org/developers/design-documents/create-amazing-password-forms) <input type=​"password" class=​"flex h-10 w-full rounded-md border px-3 py-2 text-base ring-offset-background file:​border-0 file:​bg-transparent file:​text-sm file:​font-medium file:​text-foreground placeholder:​text-muted-foreground focus-visible:​outline-none focus-visible:​ring-2 focus-visible:​ring-ring focus-visible:​ring-offset-2 disabled:​cursor-not-allowed disabled:​opacity-50 md:​text-sm bg-gray-800 border-gray-700 text-white" id=​"password" value=​"admin123">​
HybridModelViewer.tsx:256 HybridModelViewer Debug: Object
HybridModelViewer.tsx:271 Renderizando STL Viewer com modelos: Array(3)
HybridModelViewer.tsx:256 HybridModelViewer Debug: Object
HybridModelViewer.tsx:271 Renderizando STL Viewer com modelos: Array(3)
STLModelViewer.tsx:65  Erro ao carregar modelo STL: /models/caso6/sup.stl Promise
STLModel @ STLModelViewer.tsx:65
STLModelViewer.tsx:65  Erro ao carregar modelo STL: /models/caso6/inf.stl Promise
STLModel @ STLModelViewer.tsx:65
STLModelViewer.tsx:65  Erro ao carregar modelo STL: /models/caso6/crow.stl Promise
STLModel @ STLModelViewer.tsx:65
HybridModelViewer.tsx:256 HybridModelViewer Debug: Object
HybridModelViewer.tsx:271 Renderizando STL Viewer com modelos: Array(2)
HybridModelViewer.tsx:256 HybridModelViewer Debug: Object
HybridModelViewer.tsx:271 Renderizando STL Viewer com modelos: Array(1)
HybridModelViewer.tsx:256 HybridModelViewer Debug: Object
HybridModelViewer.tsx:271 Renderizando STL Viewer com modelos: Array(1)
HybridModelViewer.tsx:256 HybridModelViewer Debug: Object
HybridModelViewer.tsx:271 Renderizando STL Viewer com modelos: Array(1)
HybridModelViewer.tsx:256 HybridModelViewer Debug: Object
HybridModelViewer.tsx:271 Renderizando STL Viewer com modelos: Array(1)
three.module.js:28857 THREE.WebGLRenderer: Context Lost.
HybridModelViewer.tsx:256 HybridModelViewer Debug: Object
HybridModelViewer.tsx:271 Renderizando STL Viewer com modelos: Array(3)
HybridModelViewer.tsx:256 HybridModelViewer Debug: Object
HybridModelViewer.tsx:271 Renderizando STL Viewer com modelos: Array(3)
HybridModelViewer.tsx:256 HybridModelViewer Debug: Object
HybridModelViewer.tsx:271 Renderizando STL Viewer com modelos: Array(2)
HybridModelViewer.tsx:256 HybridModelViewer Debug: Object
HybridModelViewer.tsx:271 Renderizando STL Viewer com modelos: Array(1)
HybridModelViewer.tsx:256 HybridModelViewer Debug: Object
HybridModelViewer.tsx:271 Renderizando STL Viewer com modelos: Array(1)
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
[Violation]'message' handler demorou <N>ms
scheduler.development.js:517 [Violation]'message' handler took 670ms
scheduler.development.js:517 [Violation]'message' handler took 541ms
scheduler.development.js:517 [Violation]'message' handler took 538ms
scheduler.development.js:517 [Violation]'message' handler took 549ms
scheduler.development.js:517 [Violation]'message' handler took 546ms
scheduler.development.js:517 [Violation]'message' handler took 430ms
scheduler.development.js:517 [Violation]'message' handler took 406ms
scheduler.development.js:517 [Violation]'message' handler took 510ms
scheduler.development.js:517 [Violation]'message' handler took 399ms
scheduler.development.js:517 [Violation]'message' handler took 439ms
scheduler.development.js:517 [Violation]'message' handler took 435ms
scheduler.development.js:517 [Violation]'message' handler took 529ms
scheduler.development.js:517 [Violation]'message' handler took 438ms
scheduler.development.js:517 [Violation]'message' handler took 393ms
scheduler.development.js:517 [Violation]'message' handler took 468ms
scheduler.development.js:517 [Violation]'message' handler took 434ms
scheduler.development.js:517 [Violation]'message' handler took 407ms
scheduler.development.js:517 [Violation]'message' handler took 432ms
scheduler.development.js:517 [Violation]'message' handler took 392ms
scheduler.development.js:517 [Violation]'message' handler took 401ms
scheduler.development.js:517 [Violation]'message' handler took 397ms
scheduler.development.js:517 [Violation]'message' handler took 421ms
scheduler.development.js:517 [Violation]'message' handler took 418ms
scheduler.development.js:517 [Violation]'message' handler took 366ms
scheduler.development.js:517 [Violation]'message' handler took 361ms
scheduler.development.js:517 [Violation]'message' handler took 361ms
scheduler.development.js:517 [Violation]'message' handler took 361ms
scheduler.development.js:517 [Violation]'message' handler took 362ms
scheduler.development.js:517 [Violation]'message' handler took 362ms
scheduler.development.js:517 [Violation]'message' handler took 377ms
scheduler.development.js:517 [Violation]'message' handler took 361ms
scheduler.development.js:517 [Violation]'message' handler took 362ms
scheduler.development.js:517 [Violation]'message' handler took 361ms
scheduler.development.js:517 [Violation]'message' handler took 379ms
scheduler.development.js:517 [Violation]'message' handler took 368ms
scheduler.development.js:517 [Violation]'message' handler took 377ms
scheduler.development.js:517 [Violation]'message' handler took 386ms
scheduler.development.js:517 [Violation]'message' handler took 367ms
scheduler.development.js:517 [Violation]'message' handler took 379ms
scheduler.development.js:517 [Violation]'message' handler took 378ms
scheduler.development.js:517 [Violation]'message' handler took 394ms
scheduler.development.js:517 [Violation]'message' handler took 365ms
scheduler.development.js:517 [Violation]'message' handler took 368ms
scheduler.development.js:517 [Violation]'message' handler took 376ms
scheduler.development.js:517 [Violation]'message' handler took 380ms
scheduler.development.js:517 [Violation]'message' handler took 364ms
scheduler.development.js:517 [Violation]'message' handler took 365ms
scheduler.development.js:517 [Violation]'message' handler took 389ms
scheduler.development.js:517 [Violation]'message' handler took 386ms
scheduler.development.js:517 [Violation]'message' handler took 379ms
scheduler.development.js:517 [Violation]'message' handler took 365ms
scheduler.development.js:517 [Violation]'message' handler took 367ms
scheduler.development.js:517 [Violation]'message' handler took 426ms
scheduler.development.js:517 [Violation]'message' handler took 363ms
chunk-ELWJUCVG.js:455 [Violation]'message' handler took 371ms
chunk-ELWJUCVG.js:455 [Violation]'message' handler took 385ms
chunk-ELWJUCVG.js:455 [Violation]'message' handler took 367ms
chunk-ELWJUCVG.js:455 [Violation]'message' handler took 363ms
chunk-ELWJUCVG.js:455 [Violation]'message' handler took 373ms
chunk-ELWJUCVG.js:455 [Violation]'message' handler took 373ms
chunk-ELWJUCVG.js:455 [Violation]'message' handler took 366ms
chunk-ELWJUCVG.js:455 [Violation]'message' handler took 370ms
chunk-ELWJUCVG.js:455 [Violation]'message' handler took 364ms
chunk-ELWJUCVG.js:455 [Violation]'message' handler took 369ms
chunk-ELWJUCVG.js:455 [Violation]'message' handler took 365ms
chunk-ELWJUCVG.js:455 [Violation]'message' handler took 361ms
chunk-ELWJUCVG.js:455 [Violation]'message' handler took 382ms
