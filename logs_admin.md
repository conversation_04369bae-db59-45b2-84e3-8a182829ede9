client:495 [vite] connecting...
client:614 [vite] connected.
react-dom.development.js:29895 Download the React DevTools for a better development experience: https://reactjs.org/link/react-devtools
gerenciador-casos:1 [DOM] Password field is not contained in a form: (More info: https://www.chromium.org/developers/design-documents/create-amazing-password-forms) <input type=​"password" class=​"flex h-10 w-full rounded-md border px-3 py-2 text-base ring-offset-background file:​border-0 file:​bg-transparent file:​text-sm file:​font-medium file:​text-foreground placeholder:​text-muted-foreground focus-visible:​outline-none focus-visible:​ring-2 focus-visible:​ring-ring focus-visible:​ring-offset-2 disabled:​cursor-not-allowed disabled:​opacity-50 md:​text-sm bg-gray-800 border-gray-700 text-white" id=​"password" value=​"admin123">​
HybridModelViewer.tsx:256 HybridModelViewer Debug: {hasValidSTLModels: true, hasValidSketchfabUrl: undefined, modelsCount: 3, modelType: 'stl-multiple', modelUrl: undefined, …}
HybridModelViewer.tsx:271 Renderizando STL Viewer com modelos: (3) [{…}, {…}, {…}]
HybridModelViewer.tsx:256 HybridModelViewer Debug: {hasValidSTLModels: true, hasValidSketchfabUrl: undefined, modelsCount: 3, modelType: 'stl-multiple', modelUrl: undefined, …}
HybridModelViewer.tsx:271 Renderizando STL Viewer com modelos: (3) [{…}, {…}, {…}]
STLModelViewer.tsx:46  Erro ao carregar modelo STL: /models/caso6/sup.stl Promise {<pending>}
STLModel @ STLModelViewer.tsx:46
renderWithHooks @ react-reconciler.development.js:7363
mountIndeterminateComponent @ react-reconciler.development.js:12327
beginWork @ react-reconciler.development.js:13831
beginWork$1 @ react-reconciler.development.js:19513
performUnitOfWork @ react-reconciler.development.js:18686
workLoopSync @ react-reconciler.development.js:18597
renderRootSync @ react-reconciler.development.js:18565
performConcurrentWorkOnRoot @ react-reconciler.development.js:17836
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
STLModelViewer.tsx:46  Erro ao carregar modelo STL: /models/caso6/inf.stl Promise {<pending>}
STLModel @ STLModelViewer.tsx:46
renderWithHooks @ react-reconciler.development.js:7363
mountIndeterminateComponent @ react-reconciler.development.js:12327
beginWork @ react-reconciler.development.js:13831
beginWork$1 @ react-reconciler.development.js:19513
performUnitOfWork @ react-reconciler.development.js:18686
workLoopSync @ react-reconciler.development.js:18597
renderRootSync @ react-reconciler.development.js:18565
performConcurrentWorkOnRoot @ react-reconciler.development.js:17836
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
STLModelViewer.tsx:46  Erro ao carregar modelo STL: /models/caso6/crow.stl Promise {<pending>}
STLModel @ STLModelViewer.tsx:46
renderWithHooks @ react-reconciler.development.js:7363
mountIndeterminateComponent @ react-reconciler.development.js:12327
beginWork @ react-reconciler.development.js:13831
beginWork$1 @ react-reconciler.development.js:19513
performUnitOfWork @ react-reconciler.development.js:18686
workLoopSync @ react-reconciler.development.js:18597
renderRootSync @ react-reconciler.development.js:18565
performConcurrentWorkOnRoot @ react-reconciler.development.js:17836
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
index-e6b5343a.esm.js:1551 [Violation]'requestAnimationFrame' handler took 210ms
OrbitControls.ts:419 [Violation]Added non-passive event listener to a scroll-blocking 'wheel' event. Consider marking event handler as 'passive' to make the page more responsive. See https://www.chromestatus.com/feature/5745543795965952
OrbitControls.connect @ OrbitControls.ts:419
(anônimo) @ OrbitControls.js:36
commitHookEffectListMount @ react-reconciler.development.js:14669
commitPassiveMountOnFiber @ react-reconciler.development.js:16531
commitPassiveMountEffects_complete @ react-reconciler.development.js:16495
commitPassiveMountEffects_begin @ react-reconciler.development.js:16482
commitPassiveMountEffects @ react-reconciler.development.js:16470
flushPassiveEffectsImpl @ react-reconciler.development.js:19142
flushPassiveEffects @ react-reconciler.development.js:19095
(anônimo) @ react-reconciler.development.js:18891
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
index-e6b5343a.esm.js:1551 [Violation]'requestAnimationFrame' handler took 67ms
