client:495 [vite] connecting...
client:614 [vite] connected.
react-dom.development.js:29895 Download the React DevTools for a better development experience: https://reactjs.org/link/react-devtools
gerenciador-casos:1 [DOM] Password field is not contained in a form: (More info: https://www.chromium.org/developers/design-documents/create-amazing-password-forms) <input type=​"password" class=​"flex h-10 w-full rounded-md border px-3 py-2 text-base ring-offset-background file:​border-0 file:​bg-transparent file:​text-sm file:​font-medium file:​text-foreground placeholder:​text-muted-foreground focus-visible:​outline-none focus-visible:​ring-2 focus-visible:​ring-ring focus-visible:​ring-offset-2 disabled:​cursor-not-allowed disabled:​opacity-50 md:​text-sm bg-gray-800 border-gray-700 text-white" id=​"password" value=​"admin123">​
Dialog.tsx:540  Warning: Missing `Description` or `aria-describedby={undefined}` for {DialogContent}.
(anônimo) @ Dialog.tsx:540
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
commitRootImpl @ react-dom.development.js:26974
commitRoot @ react-dom.development.js:26721
performSyncWorkOnRoot @ react-dom.development.js:26156
flushSyncCallbacks @ react-dom.development.js:12042
(anônimo) @ react-dom.development.js:25690
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation]Added non-passive event listener to a scroll-blocking <algum> evento. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
a71993b83e4fc29775e91744c89b50ad-v2.js:2 [Violation]'setTimeout' handler took 53ms
a71993b83e4fc29775e91744c89b50ad-v2.js:2  [Violation]Permissions policy violation: accelerometer is not allowed in this document.
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
e @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setEnable @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
_addEvent @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
addMappings @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
addMappings @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
init @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
wi @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
(anônimo) @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
init @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
Ui @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
setupManipulator @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
initialize @ dcd90f2609921bc1c172e143bc388605-v2.js:1
<computed> @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
t.View @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
V.r @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
createScene @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
onModelLoad @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
add @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
Deferred @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
then @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
sync @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
fetch @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
get @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
load @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
start @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
l @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromiseFromHandler @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromise @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromiseCtx @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
c @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
a @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s._drainQueues @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
drainQueues @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
attributes
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
attributes
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s._queueTick @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s.invoke @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._then @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O.then @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
initializeViewer @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 6f0757f2090c0b8b379aaa8d498f3538-v2.js:1
initialize @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
<computed> @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
t.View @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
V.r @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
add @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
Deferred @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
then @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
sync @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
fetch @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
get @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
c @ 451d38d26ff6171c541e4f63c1418836-v2.js:1
Pu2i @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
r @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
r.O @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
a @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
a71993b83e4fc29775e91744c89b50ad-v2.js:2  The deviceorientation events are blocked by permissions policy. See https://go.microsoft.com/fwlink/?linkid=2047894
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
e @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setEnable @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
_addEvent @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
addMappings @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
addMappings @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
init @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
wi @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
(anônimo) @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
init @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
Ui @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
setupManipulator @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
initialize @ dcd90f2609921bc1c172e143bc388605-v2.js:1
<computed> @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
t.View @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
V.r @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
createScene @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
onModelLoad @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
add @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
Deferred @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
then @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
sync @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
fetch @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
get @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
load @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
start @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
l @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromiseFromHandler @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromise @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromiseCtx @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
c @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
a @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s._drainQueues @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
drainQueues @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
attributes
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
attributes
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s._queueTick @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s.invoke @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._then @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O.then @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
initializeViewer @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 6f0757f2090c0b8b379aaa8d498f3538-v2.js:1
initialize @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
<computed> @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
t.View @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
V.r @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
add @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
Deferred @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
then @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
sync @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
fetch @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
get @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
c @ 451d38d26ff6171c541e4f63c1418836-v2.js:1
Pu2i @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
r @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
r.O @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
a @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
a71993b83e4fc29775e91744c89b50ad-v2.js:2  [Violation]Permissions policy violation: accelerometer is not allowed in this document.
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
e @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setEnable @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
_addEvent @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
addMappings @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
addMappings @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
init @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
bi @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
(anônimo) @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
init @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
Mi @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
se @ dcd90f2609921bc1c172e143bc388605-v2.js:1
init @ dcd90f2609921bc1c172e143bc388605-v2.js:1
we @ dcd90f2609921bc1c172e143bc388605-v2.js:1
initialize @ dcd90f2609921bc1c172e143bc388605-v2.js:1
<computed> @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
t.View @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
V.r @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
i @ dcd90f2609921bc1c172e143bc388605-v2.js:1
(anônimo) @ dcd90f2609921bc1c172e143bc388605-v2.js:1
init @ dcd90f2609921bc1c172e143bc388605-v2.js:1
addFeature @ dcd90f2609921bc1c172e143bc388605-v2.js:1
(anônimo) @ dcd90f2609921bc1c172e143bc388605-v2.js:1
run @ dcd90f2609921bc1c172e143bc388605-v2.js:1
onModelLoad @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
add @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
Deferred @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
then @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
sync @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
fetch @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
get @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
load @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
start @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
l @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromiseFromHandler @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromise @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromiseCtx @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
c @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
a @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s._drainQueues @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
drainQueues @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
attributes
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
attributes
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s._queueTick @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s.invoke @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._then @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O.then @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
initializeViewer @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 6f0757f2090c0b8b379aaa8d498f3538-v2.js:1
initialize @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
<computed> @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
t.View @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
V.r @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
add @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
Deferred @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
then @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
sync @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
fetch @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
get @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
c @ 451d38d26ff6171c541e4f63c1418836-v2.js:1
Pu2i @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
r @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
r.O @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
a @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
a71993b83e4fc29775e91744c89b50ad-v2.js:2  The deviceorientation events are blocked by permissions policy. See https://go.microsoft.com/fwlink/?linkid=2047894
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
e @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setEnable @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
_addEvent @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
addMappings @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
addMappings @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
init @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
bi @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
(anônimo) @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
init @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
Mi @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
se @ dcd90f2609921bc1c172e143bc388605-v2.js:1
init @ dcd90f2609921bc1c172e143bc388605-v2.js:1
we @ dcd90f2609921bc1c172e143bc388605-v2.js:1
initialize @ dcd90f2609921bc1c172e143bc388605-v2.js:1
<computed> @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
t.View @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
V.r @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
i @ dcd90f2609921bc1c172e143bc388605-v2.js:1
(anônimo) @ dcd90f2609921bc1c172e143bc388605-v2.js:1
init @ dcd90f2609921bc1c172e143bc388605-v2.js:1
addFeature @ dcd90f2609921bc1c172e143bc388605-v2.js:1
(anônimo) @ dcd90f2609921bc1c172e143bc388605-v2.js:1
run @ dcd90f2609921bc1c172e143bc388605-v2.js:1
onModelLoad @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
add @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
Deferred @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
then @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
sync @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
fetch @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
get @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
load @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
start @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
l @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromiseFromHandler @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromise @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromiseCtx @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
c @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
a @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s._drainQueues @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
drainQueues @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
attributes
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
attributes
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s._queueTick @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s.invoke @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._then @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O.then @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
initializeViewer @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 6f0757f2090c0b8b379aaa8d498f3538-v2.js:1
initialize @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
<computed> @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
t.View @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
V.r @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
add @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
Deferred @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
then @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
sync @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
fetch @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
get @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
c @ 451d38d26ff6171c541e4f63c1418836-v2.js:1
Pu2i @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
r @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
r.O @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
a @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
f45fb3a3d002823dba31659e9462bdc3-v2.js:2  [Violation]Permissions policy violation: xr-spatial-tracking is not allowed in this document.
pollHeadset @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
initialize @ dcd90f2609921bc1c172e143bc388605-v2.js:1
<computed> @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
t.View @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
V.r @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
i @ dcd90f2609921bc1c172e143bc388605-v2.js:1
(anônimo) @ dcd90f2609921bc1c172e143bc388605-v2.js:1
init @ dcd90f2609921bc1c172e143bc388605-v2.js:1
addFeature @ dcd90f2609921bc1c172e143bc388605-v2.js:1
(anônimo) @ dcd90f2609921bc1c172e143bc388605-v2.js:1
run @ dcd90f2609921bc1c172e143bc388605-v2.js:1
onModelLoad @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
add @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
Deferred @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
then @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
sync @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
fetch @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
get @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
load @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
start @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
l @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromiseFromHandler @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromise @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromiseCtx @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
c @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
a @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s._drainQueues @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
drainQueues @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
attributes
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
attributes
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s._queueTick @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s.invoke @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._then @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O.then @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
initializeViewer @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 6f0757f2090c0b8b379aaa8d498f3538-v2.js:1
initialize @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
<computed> @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
t.View @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
V.r @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
add @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
Deferred @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
then @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
sync @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
fetch @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
get @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
c @ 451d38d26ff6171c541e4f63c1418836-v2.js:1
Pu2i @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
r @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
r.O @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
a @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
a71993b83e4fc29775e91744c89b50ad-v2.js:2 [Violation]'setTimeout' handler took 226ms
a71993b83e4fc29775e91744c89b50ad-v2.js:2  [Violation]Permissions policy violation: accelerometer is not allowed in this document.
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
e @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setEnable @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
_addEvent @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
addMappings @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
addMappings @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
init @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
wi @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
(anônimo) @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
init @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
Ui @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
setupManipulator @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
initialize @ dcd90f2609921bc1c172e143bc388605-v2.js:1
<computed> @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
t.View @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
V.r @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
createScene @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
onModelLoad @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
add @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
Deferred @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
then @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
sync @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
fetch @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
get @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
load @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
start @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
l @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromiseFromHandler @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromise @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromiseCtx @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
c @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
a @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s._drainQueues @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
drainQueues @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
attributes
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
attributes
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s._queueTick @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s.invoke @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._then @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O.then @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
initializeViewer @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 6f0757f2090c0b8b379aaa8d498f3538-v2.js:1
initialize @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
<computed> @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
t.View @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
V.r @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
add @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
Deferred @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
then @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
sync @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
fetch @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
get @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
c @ 451d38d26ff6171c541e4f63c1418836-v2.js:1
Pu2i @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
r @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
r.O @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
a @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
a71993b83e4fc29775e91744c89b50ad-v2.js:2  The deviceorientation events are blocked by permissions policy. See https://go.microsoft.com/fwlink/?linkid=2047894
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
e @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setEnable @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
_addEvent @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
addMappings @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
addMappings @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
init @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
wi @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
(anônimo) @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
init @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
Ui @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
setupManipulator @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
initialize @ dcd90f2609921bc1c172e143bc388605-v2.js:1
<computed> @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
t.View @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
V.r @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
createScene @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
onModelLoad @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
add @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
Deferred @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
then @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
sync @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
fetch @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
get @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
load @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
start @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
l @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromiseFromHandler @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromise @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromiseCtx @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
c @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
a @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s._drainQueues @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
drainQueues @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
attributes
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
attributes
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s._queueTick @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s.invoke @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._then @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O.then @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
initializeViewer @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 6f0757f2090c0b8b379aaa8d498f3538-v2.js:1
initialize @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
<computed> @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
t.View @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
V.r @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
add @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
Deferred @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
then @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
sync @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
fetch @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
get @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
c @ 451d38d26ff6171c541e4f63c1418836-v2.js:1
Pu2i @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
r @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
r.O @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
a @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
a71993b83e4fc29775e91744c89b50ad-v2.js:2  [Violation]Permissions policy violation: accelerometer is not allowed in this document.
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
e @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setEnable @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
_addEvent @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
addMappings @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
addMappings @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
init @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
bi @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
(anônimo) @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
init @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
Mi @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
se @ dcd90f2609921bc1c172e143bc388605-v2.js:1
init @ dcd90f2609921bc1c172e143bc388605-v2.js:1
we @ dcd90f2609921bc1c172e143bc388605-v2.js:1
initialize @ dcd90f2609921bc1c172e143bc388605-v2.js:1
<computed> @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
t.View @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
V.r @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
i @ dcd90f2609921bc1c172e143bc388605-v2.js:1
(anônimo) @ dcd90f2609921bc1c172e143bc388605-v2.js:1
init @ dcd90f2609921bc1c172e143bc388605-v2.js:1
addFeature @ dcd90f2609921bc1c172e143bc388605-v2.js:1
(anônimo) @ dcd90f2609921bc1c172e143bc388605-v2.js:1
run @ dcd90f2609921bc1c172e143bc388605-v2.js:1
onModelLoad @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
add @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
Deferred @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
then @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
sync @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
fetch @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
get @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
load @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
start @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
l @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromiseFromHandler @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromise @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromiseCtx @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
c @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
a @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s._drainQueues @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
drainQueues @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
attributes
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
attributes
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s._queueTick @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s.invoke @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._then @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O.then @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
initializeViewer @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 6f0757f2090c0b8b379aaa8d498f3538-v2.js:1
initialize @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
<computed> @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
t.View @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
V.r @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
add @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
Deferred @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
then @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
sync @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
fetch @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
get @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
c @ 451d38d26ff6171c541e4f63c1418836-v2.js:1
Pu2i @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
r @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
r.O @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
a @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
a71993b83e4fc29775e91744c89b50ad-v2.js:2  The deviceorientation events are blocked by permissions policy. See https://go.microsoft.com/fwlink/?linkid=2047894
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
e @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setEnable @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
_addEvent @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
addMappings @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
addMappings @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
init @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
bi @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
(anônimo) @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
init @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
Mi @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
se @ dcd90f2609921bc1c172e143bc388605-v2.js:1
init @ dcd90f2609921bc1c172e143bc388605-v2.js:1
we @ dcd90f2609921bc1c172e143bc388605-v2.js:1
initialize @ dcd90f2609921bc1c172e143bc388605-v2.js:1
<computed> @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
t.View @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
V.r @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
i @ dcd90f2609921bc1c172e143bc388605-v2.js:1
(anônimo) @ dcd90f2609921bc1c172e143bc388605-v2.js:1
init @ dcd90f2609921bc1c172e143bc388605-v2.js:1
addFeature @ dcd90f2609921bc1c172e143bc388605-v2.js:1
(anônimo) @ dcd90f2609921bc1c172e143bc388605-v2.js:1
run @ dcd90f2609921bc1c172e143bc388605-v2.js:1
onModelLoad @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
add @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
Deferred @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
then @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
sync @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
fetch @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
get @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
load @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
start @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
l @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromiseFromHandler @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromise @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromiseCtx @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
c @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
a @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s._drainQueues @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
drainQueues @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
attributes
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
attributes
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s._queueTick @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s.invoke @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._then @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O.then @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
initializeViewer @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 6f0757f2090c0b8b379aaa8d498f3538-v2.js:1
initialize @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
<computed> @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
t.View @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
V.r @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
add @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
Deferred @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
then @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
sync @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
fetch @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
get @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
c @ 451d38d26ff6171c541e4f63c1418836-v2.js:1
Pu2i @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
r @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
r.O @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
a @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
f45fb3a3d002823dba31659e9462bdc3-v2.js:2  [Violation]Permissions policy violation: xr-spatial-tracking is not allowed in this document.
pollHeadset @ f45fb3a3d002823dba31659e9462bdc3-v2.js:2
initialize @ dcd90f2609921bc1c172e143bc388605-v2.js:1
<computed> @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
t.View @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
V.r @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
i @ dcd90f2609921bc1c172e143bc388605-v2.js:1
(anônimo) @ dcd90f2609921bc1c172e143bc388605-v2.js:1
init @ dcd90f2609921bc1c172e143bc388605-v2.js:1
addFeature @ dcd90f2609921bc1c172e143bc388605-v2.js:1
(anônimo) @ dcd90f2609921bc1c172e143bc388605-v2.js:1
run @ dcd90f2609921bc1c172e143bc388605-v2.js:1
onModelLoad @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
add @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
Deferred @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
then @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
sync @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
fetch @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
get @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
load @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
start @ daa012aa60c29d32bc875705fb34eb0b-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
l @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromiseFromHandler @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromise @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._settlePromiseCtx @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
c @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
a @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s._drainQueues @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
drainQueues @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
attributes
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
attributes
(anônimo) @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s._queueTick @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
s.invoke @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O._then @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
O.then @ ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2
initializeViewer @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 6f0757f2090c0b8b379aaa8d498f3538-v2.js:1
initialize @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
<computed> @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
t.View @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
constructor @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
V.r @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fire @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
fireWith @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
l @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
c @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
a @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
setTimeout
(anônimo) @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
u @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
add @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
(anônimo) @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
Deferred @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
then @ b840b924fd2da64fb2a78740d51c076a-v2.js:2
sync @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
fetch @ a71993b83e4fc29775e91744c89b50ad-v2.js:2
get @ fad4b8186583c4a7ea863bd1ff284550-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
c @ 451d38d26ff6171c541e4f63c1418836-v2.js:1
Pu2i @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
r @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
r.O @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
a @ a8cb43f28cf7c9645dcf713a205c8dd6-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
(anônimo) @ 139723119fc6ea305d0068d2ee18b58f-v2.js:1
a71993b83e4fc29775e91744c89b50ad-v2.js:2 [Violation]'setTimeout' handler took 176ms
ac0f732c4fc1a30c77920d75c1a9be83-v2.js:2 [Violation]'load' handler took 378ms
Dialog.tsx:540  Warning: Missing `Description` or `aria-describedby={undefined}` for {DialogContent}.
(anônimo) @ Dialog.tsx:540
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
commitRootImpl @ react-dom.development.js:26974
commitRoot @ react-dom.development.js:26721
performSyncWorkOnRoot @ react-dom.development.js:26156
flushSyncCallbacks @ react-dom.development.js:12042
(anônimo) @ react-dom.development.js:25690
[Violation]'load' handler took 253ms
