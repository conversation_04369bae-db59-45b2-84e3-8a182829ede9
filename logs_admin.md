client:495 [vite] connecting...
client:614 [vite] connected.
react-dom.development.js:29895 Download the React DevTools for a better development experience: https://reactjs.org/link/react-devtools
gerenciador-casos:1 [DOM] Password field is not contained in a form: (More info: https://www.chromium.org/developers/design-documents/create-amazing-password-forms) <input type=​"password" class=​"flex h-10 w-full rounded-md border px-3 py-2 text-base ring-offset-background file:​border-0 file:​bg-transparent file:​text-sm file:​font-medium file:​text-foreground placeholder:​text-muted-foreground focus-visible:​outline-none focus-visible:​ring-2 focus-visible:​ring-ring focus-visible:​ring-offset-2 disabled:​cursor-not-allowed disabled:​opacity-50 md:​text-sm bg-gray-800 border-gray-700 text-white" id=​"password" value=​"admin123">​
Dialog.tsx:540  Warning: Missing `Description` or `aria-describedby={undefined}` for {DialogContent}.
(anônimo) @ Dialog.tsx:540
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
commitRootImpl @ react-dom.development.js:26974
commitRoot @ react-dom.development.js:26721
performSyncWorkOnRoot @ react-dom.development.js:26156
flushSyncCallbacks @ react-dom.development.js:12042
(anônimo) @ react-dom.development.js:25690
index-e6b5343a.esm.js:1667  Uncaught Error: Could not load : Invalid typed array length: 16070586912
    at index-e6b5343a.esm.js:1667:36
    at Object.onLoad (STLLoader.js:84:13)
    at three.module.js:42369:38
(anônimo) @ index-e6b5343a.esm.js:1667
(anônimo) @ STLLoader.js:84
(anônimo) @ three.module.js:42369
Promise.then
load @ three.module.js:42357
load @ STLLoader.js:77
(anônimo) @ index-e6b5343a.esm.js:1664
(anônimo) @ index-e6b5343a.esm.js:1664
(anônimo) @ index-e6b5343a.esm.js:1664
query @ index.js:51
suspend @ index.js:68
useLoader @ index-e6b5343a.esm.js:1679
STLModel @ STLModelViewer.tsx:36
renderWithHooks @ react-reconciler.development.js:7363
mountIndeterminateComponent @ react-reconciler.development.js:12327
beginWork @ react-reconciler.development.js:13831
beginWork$1 @ react-reconciler.development.js:19513
performUnitOfWork @ react-reconciler.development.js:18686
workLoopSync @ react-reconciler.development.js:18597
renderRootSync @ react-reconciler.development.js:18565
performConcurrentWorkOnRoot @ react-reconciler.development.js:17836
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
index-e6b5343a.esm.js:1667  Uncaught Error: Could not load : Invalid typed array length: 16070586912
    at index-e6b5343a.esm.js:1667:36
    at Object.onLoad (STLLoader.js:84:13)
    at three.module.js:42369:38
(anônimo) @ index-e6b5343a.esm.js:1667
(anônimo) @ STLLoader.js:84
(anônimo) @ three.module.js:42369
Promise.then
load @ three.module.js:42357
load @ STLLoader.js:77
(anônimo) @ index-e6b5343a.esm.js:1664
(anônimo) @ index-e6b5343a.esm.js:1664
(anônimo) @ index-e6b5343a.esm.js:1664
query @ index.js:51
suspend @ index.js:68
useLoader @ index-e6b5343a.esm.js:1679
STLModel @ STLModelViewer.tsx:36
renderWithHooks @ react-reconciler.development.js:7363
mountIndeterminateComponent @ react-reconciler.development.js:12327
beginWork @ react-reconciler.development.js:13831
beginWork$1 @ react-reconciler.development.js:19513
performUnitOfWork @ react-reconciler.development.js:18686
workLoopSync @ react-reconciler.development.js:18597
renderRootSync @ react-reconciler.development.js:18565
performConcurrentWorkOnRoot @ react-reconciler.development.js:17836
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
index.tsx:86  The above error occurred in the <STLModel> component:

    at STLModel (http://localhost:8080/src/components/3d/STLModelViewer.tsx:19:21)
    at group
    at Suspense
    at Suspense
    at ErrorBoundary (http://localhost:8080/node_modules/.vite/deps/chunk-ELWJUCVG.js?v=2449da8e:16487:5)
    at FiberProvider (http://localhost:8080/node_modules/.vite/deps/chunk-ELWJUCVG.js?v=2449da8e:18071:21)
    at Provider (http://localhost:8080/node_modules/.vite/deps/chunk-ELWJUCVG.js?v=2449da8e:17758:3)

React will try to recreate this component tree from scratch using the error boundary you provided, ErrorBoundary.
console.error @ index.tsx:86
logCapturedError @ react-reconciler.development.js:9747
callback @ react-reconciler.development.js:9815
callCallback @ react-reconciler.development.js:4279
commitUpdateQueue @ react-reconciler.development.js:4300
commitLayoutEffectOnFiber @ react-reconciler.development.js:14877
commitLayoutMountEffects_complete @ react-reconciler.development.js:16290
commitLayoutEffects_begin @ react-reconciler.development.js:16276
commitLayoutEffects @ react-reconciler.development.js:16214
commitRootImpl @ react-reconciler.development.js:18945
commitRoot @ react-reconciler.development.js:18811
finishConcurrentRender @ react-reconciler.development.js:17990
performConcurrentWorkOnRoot @ react-reconciler.development.js:17907
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
index-e6b5343a.esm.js:1667  Uncaught Error: Could not load : Invalid typed array length: 16070586912
    at index-e6b5343a.esm.js:1667:36
    at Object.onLoad (STLLoader.js:84:13)
    at three.module.js:42369:38
(anônimo) @ index-e6b5343a.esm.js:1667
(anônimo) @ STLLoader.js:84
(anônimo) @ three.module.js:42369
Promise.then
load @ three.module.js:42357
load @ STLLoader.js:77
(anônimo) @ index-e6b5343a.esm.js:1664
(anônimo) @ index-e6b5343a.esm.js:1664
(anônimo) @ index-e6b5343a.esm.js:1664
query @ index.js:51
suspend @ index.js:68
useLoader @ index-e6b5343a.esm.js:1679
STLModel @ STLModelViewer.tsx:36
renderWithHooks @ react-reconciler.development.js:7363
mountIndeterminateComponent @ react-reconciler.development.js:12327
beginWork @ react-reconciler.development.js:13831
beginWork$1 @ react-reconciler.development.js:19513
performUnitOfWork @ react-reconciler.development.js:18686
workLoopSync @ react-reconciler.development.js:18597
renderRootSync @ react-reconciler.development.js:18565
performConcurrentWorkOnRoot @ react-reconciler.development.js:17836
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
index-e6b5343a.esm.js:1667  Uncaught Error: Could not load : Invalid typed array length: 16070586912
    at index-e6b5343a.esm.js:1667:36
    at Object.onLoad (STLLoader.js:84:13)
    at three.module.js:42369:38
(anônimo) @ index-e6b5343a.esm.js:1667
(anônimo) @ STLLoader.js:84
(anônimo) @ three.module.js:42369
Promise.then
load @ three.module.js:42357
load @ STLLoader.js:77
(anônimo) @ index-e6b5343a.esm.js:1664
(anônimo) @ index-e6b5343a.esm.js:1664
(anônimo) @ index-e6b5343a.esm.js:1664
query @ index.js:51
suspend @ index.js:68
useLoader @ index-e6b5343a.esm.js:1679
STLModel @ STLModelViewer.tsx:36
renderWithHooks @ react-reconciler.development.js:7363
mountIndeterminateComponent @ react-reconciler.development.js:12327
beginWork @ react-reconciler.development.js:13831
beginWork$1 @ react-reconciler.development.js:19513
performUnitOfWork @ react-reconciler.development.js:18686
workLoopSync @ react-reconciler.development.js:18597
renderRootSync @ react-reconciler.development.js:18565
performConcurrentWorkOnRoot @ react-reconciler.development.js:17836
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
index.tsx:86  The above error occurred in the <ForwardRef(Canvas)> component:

    at Canvas (http://localhost:8080/node_modules/.vite/deps/chunk-ELWJUCVG.js?v=2449da8e:18215:3)
    at FiberProvider (http://localhost:8080/node_modules/.vite/deps/chunk-ELWJUCVG.js?v=2449da8e:18071:21)
    at CanvasWrapper
    at div
    at STLModelViewer (http://localhost:8080/src/components/3d/STLModelViewer.tsx:112:27)
    at div
    at div
    at HybridModelViewer (http://localhost:8080/src/components/3d/HybridModelViewer.tsx:16:30)
    at div
    at div
    at div
    at div
    at div
    at http://localhost:8080/node_modules/.vite/deps/chunk-RDHMCQ3E.js?v=2449da8e:41:13
    at Presence (http://localhost:8080/node_modules/.vite/deps/chunk-MHLXIYJU.js?v=2449da8e:174:11)
    at http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-tabs.js?v=2449da8e:450:13
    at _c4 (http://localhost:8080/src/components/ui/tabs.tsx:36:61)
    at div
    at http://localhost:8080/node_modules/.vite/deps/chunk-RDHMCQ3E.js?v=2449da8e:41:13
    at Provider (http://localhost:8080/node_modules/.vite/deps/chunk-MHLXIYJU.js?v=2449da8e:51:15)
    at http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-tabs.js?v=2449da8e:328:7
    at div
    at http://localhost:8080/node_modules/.vite/deps/chunk-RDHMCQ3E.js?v=2449da8e:41:13
    at http://localhost:8080/node_modules/.vite/deps/chunk-LRUEDOTG.js?v=2449da8e:59:7
    at http://localhost:8080/node_modules/.vite/deps/chunk-TQG5UYZM.js?v=2449da8e:52:11
    at http://localhost:8080/node_modules/.vite/deps/chunk-TQG5UYZM.js?v=2449da8e:33:11
    at http://localhost:8080/node_modules/.vite/deps/chunk-RDHMCQ3E.js?v=2449da8e:41:13
    at http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-dialog.js?v=2449da8e:47:5
    at http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-dialog.js?v=2449da8e:1305:13
    at http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-dialog.js?v=2449da8e:1228:58
    at Presence (http://localhost:8080/node_modules/.vite/deps/chunk-MHLXIYJU.js?v=2449da8e:174:11)
    at http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-dialog.js?v=2449da8e:1219:64
    at http://localhost:8080/node_modules/.vite/deps/chunk-TQG5UYZM.js?v=2449da8e:52:11
    at http://localhost:8080/node_modules/.vite/deps/chunk-TQG5UYZM.js?v=2449da8e:33:11
    at http://localhost:8080/node_modules/.vite/deps/chunk-RDHMCQ3E.js?v=2449da8e:41:13
    at http://localhost:8080/node_modules/.vite/deps/chunk-LRUEDOTG.js?v=2449da8e:258:22
    at Presence (http://localhost:8080/node_modules/.vite/deps/chunk-MHLXIYJU.js?v=2449da8e:174:11)
    at Provider (http://localhost:8080/node_modules/.vite/deps/chunk-MHLXIYJU.js?v=2449da8e:51:15)
    at DialogPortal (http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-dialog.js?v=2449da8e:1182:11)
    at _c1 (http://localhost:8080/src/components/ui/dialog.tsx:28:63)
    at Provider (http://localhost:8080/node_modules/.vite/deps/chunk-MHLXIYJU.js?v=2449da8e:51:15)
    at Dialog (http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-dialog.js?v=2449da8e:1123:5)
    at div
    at Admin (http://localhost:8080/src/pages/Admin.tsx?t=1750482949420:31:51)
    at RenderedRoute (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4069:5)
    at Routes (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4508:5)
    at Router (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:4451:15)
    at BrowserRouter (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=2449da8e:5196:5)
    at Provider (http://localhost:8080/node_modules/.vite/deps/chunk-MHLXIYJU.js?v=2449da8e:51:15)
    at TooltipProvider (http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=2449da8e:87:5)
    at QueryClientProvider (http://localhost:8080/node_modules/.vite/deps/@tanstack_react-query.js?v=2449da8e:2933:3)
    at ThemeProvider (http://localhost:8080/src/hooks/use-theme.tsx:20:33)
    at App

Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://reactjs.org/link/error-boundaries to learn more about error boundaries.
console.error @ index.tsx:86
logCapturedError @ react-dom.development.js:18704
update.callback @ react-dom.development.js:18737
callCallback @ react-dom.development.js:15036
commitUpdateQueue @ react-dom.development.js:15057
commitLayoutEffectOnFiber @ react-dom.development.js:23430
commitLayoutMountEffects_complete @ react-dom.development.js:24727
commitLayoutEffects_begin @ react-dom.development.js:24713
commitLayoutEffects @ react-dom.development.js:24651
commitRootImpl @ react-dom.development.js:26862
commitRoot @ react-dom.development.js:26721
finishConcurrentRender @ react-dom.development.js:25931
performConcurrentWorkOnRoot @ react-dom.development.js:25848
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
react-dom.development.js:26962  Uncaught Error: Could not load : Invalid typed array length: 16070586912
    at index-e6b5343a.esm.js:1667:36
    at Object.onLoad (STLLoader.js:84:13)
    at three.module.js:42369:38
(anônimo) @ index-e6b5343a.esm.js:1667
(anônimo) @ STLLoader.js:84
(anônimo) @ three.module.js:42369
Promise.then
load @ three.module.js:42357
load @ STLLoader.js:77
(anônimo) @ index-e6b5343a.esm.js:1664
(anônimo) @ index-e6b5343a.esm.js:1664
(anônimo) @ index-e6b5343a.esm.js:1664
query @ index.js:51
suspend @ index.js:68
useLoader @ index-e6b5343a.esm.js:1679
STLModel @ STLModelViewer.tsx:36
renderWithHooks @ react-reconciler.development.js:7363
mountIndeterminateComponent @ react-reconciler.development.js:12327
beginWork @ react-reconciler.development.js:13831
beginWork$1 @ react-reconciler.development.js:19513
performUnitOfWork @ react-reconciler.development.js:18686
workLoopSync @ react-reconciler.development.js:18597
renderRootSync @ react-reconciler.development.js:18565
performConcurrentWorkOnRoot @ react-reconciler.development.js:17836
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
three.module.js:28857 THREE.WebGLRenderer: Context Lost.
