
import React from 'react';
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { ThemeProvider } from "@/hooks/use-theme";
import Cases from "./pages/Cases";
import NotFound from "./pages/NotFound";
import Index from "./pages/Index";
import Admin from './pages/Admin';

// Create a new QueryClient instance
const queryClient = new QueryClient();

// Make App a proper React functional component
const App: React.FC = () => {
  return (
    <ThemeProvider defaultTheme="system" storageKey="dental-portfolio-theme">
      <QueryClientProvider client={queryClient}>
        <TooltipProvider>
          <Toaster />
          <Sonner />
          <BrowserRouter>
            <Routes>
              <Route path="/" element={<Cases />} />
              <Route path="/gallery" element={<Index />} />
              <Route path="/gerenciador-casos" element={<Admin />} />
              {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
              <Route path="*" element={<NotFound />} />
            </Routes>
          </BrowserRouter>
        </TooltipProvider>
      </QueryClientProvider>
    </ThemeProvider>
  );
};

export default App;
