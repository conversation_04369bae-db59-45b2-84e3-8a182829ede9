
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Cases from "./pages/Cases";
import NotFound from "./pages/NotFound";
import Index from "./pages/Index";
import React from 'react';


// Carregamento lazy do componente de teste de responsividade (apenas em desenvolvimento)


// Importação do componente Admin
import Admin from './pages/Admin';

// Create a new QueryClient instance
const queryClient = new QueryClient();

// Make App a proper React functional component
const App: React.FC = () => {
  // Verificar se estamos em modo de desenvolvimento


  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Routes>
            <Route path="/" element={<Cases />} />
            <Route path="/gallery" element={<Index />} />
            <Route path="/gerenciador-casos" element={<Admin />} />
            {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>

        {/* Ferramenta de teste de responsividade (apenas em modo de desenvolvimento) */}

          <Suspense fallback={null}>
            <ResponsiveTestingTool />
          </Suspense>
        )}
      </TooltipProvider>
    </QueryClientProvider>
  );
};

export default App;
