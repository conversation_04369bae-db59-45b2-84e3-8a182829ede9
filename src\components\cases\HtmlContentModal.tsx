import React, { useState, useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';
import { Dialog, DialogContent, DialogTitle, DialogDescription, DialogClose } from '@/components/ui/dialog';
import { Loader2, X, ExternalLink, ChevronLeft, ChevronRight, Image as ImageIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { toast } from '@/components/ui/sonner';
import { setupExocadViewerBackgroundObserver } from '@/utils/exocad-viewer-helper';
import { cn } from '@/lib/utils';
import { useIsMobile } from '@/hooks/use-mobile';

// Imagens padrão para a galeria (caso não sejam fornecidas)
const DEFAULT_CASE_IMAGES = [
  '/images/caso1/20250514-202931-2023-06-08_00059-007-0.png',
  '/images/caso1/20250514-202958-2023-06-08_00059-007-1.png',
  '/images/caso1/20250514-203015-2023-06-08_00059-007-2.png',
  '/images/caso1/20250514-203045-2023-06-08_00059-007-3.png',
  '/images/caso1/20250514-203221-2023-06-08_00059-007-5.png',
];

interface HtmlContentModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  htmlContent: string;
  exocadHtmlUrl?: string;
  galleryImages?: string[];
  title?: string;
  type?: string;
  description?: string;
}

const HtmlContentModal = ({ isOpen, onOpenChange, htmlContent, exocadHtmlUrl, galleryImages = DEFAULT_CASE_IMAGES, title = "Caso Odontológico", type = "COROAS", description }: HtmlContentModalProps) => {
  const isMobile = useIsMobile();
  const [isIframeLoading, setIsIframeLoading] = useState<boolean>(true);
  const [iframeError, setIframeError] = useState<boolean>(false);
  const [currentImageIndex, setCurrentImageIndex] = useState<number>(0);
  const [lightboxOpen, setLightboxOpen] = useState<boolean>(false);
  const [isClosing, setIsClosing] = useState<boolean>(false);
  const [zoomLevel, setZoomLevel] = useState<number>(1.0); // Começamos com 100% do tamanho original
  const [imagePosition, setImagePosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [lastWheelTime, setLastWheelTime] = useState(0);
  const lightboxRef = useRef<HTMLDivElement>(null);
  const imageRef = useRef<HTMLImageElement>(null);
  const iframeRef = useRef<HTMLIFrameElement>(null);

  // Reset loading state when the modal opens
  useEffect(() => {
    if (isOpen) {
      setIsIframeLoading(true);
      setIframeError(false);
      setIsClosing(false); // Reset closing state when modal opens
    }
  }, [isOpen]);

  // Configurar o observador para modificar o fundo do visualizador 3D
  useEffect(() => {
    if (isOpen && exocadHtmlUrl) {
      // Configura o observador para modificar o fundo para transparente
      const cleanupObserver = setupExocadViewerBackgroundObserver(iframeRef, 'transparent');

      // Limpa o observador quando o componente for desmontado ou o modal fechado
      return () => cleanupObserver();
    }
  }, [isOpen, exocadHtmlUrl]);

  const handleIframeLoad = () => {
    setIsIframeLoading(false);

    // Check if iframe loaded correctly by trying to access its content
    try {
      if (iframeRef.current && iframeRef.current.contentWindow) {
        // If we can access contentWindow, it's likely loaded correctly
        setIframeError(false);
      }
    } catch (error) {
      // If there's a security error or other issue, mark as error
      setIframeError(true);
      console.error("Error accessing iframe content:", error);
    }
  };

  const handleIframeError = () => {
    setIsIframeLoading(false);
    setIframeError(true);
  };

  // Process the Exocad URL to ensure proper HTML rendering
  const getProcessedExocadUrl = () => {
    if (!exocadHtmlUrl) return '';

    // Check if it's a local file path or a URL
    if (exocadHtmlUrl.startsWith('/')) {
      // Local file path
      return exocadHtmlUrl;
    } else if (exocadHtmlUrl.startsWith('./')) {
      // Local file path with relative notation
      return exocadHtmlUrl;
    } else if (exocadHtmlUrl.startsWith('http')) {
      // External URL
      // If the URL already has a query parameter, add content_type=text/html
      if (exocadHtmlUrl.includes('?')) {
        return `${exocadHtmlUrl}&content_type=text/html&response-content-disposition=inline`;
      }

      // Otherwise, add the query parameter with a question mark
      return `${exocadHtmlUrl}?content_type=text/html&response-content-disposition=inline`;
    }

    // Default to treating as a local file in the public folder
    return `/html/${exocadHtmlUrl}`;
  };



  // Função para navegar entre as imagens da galeria
  const navigateGallery = (direction: 'next' | 'prev') => {
    if (!lightboxOpen) return; // Só funciona quando o lightbox está aberto

    if (direction === 'next') {
      setCurrentImageIndex((prev) => (prev + 1) % galleryImages.length);
    } else {
      setCurrentImageIndex((prev) => (prev - 1 + galleryImages.length) % galleryImages.length);
    }

    // Reset zoom and position when changing images
    setZoomLevel(1.0); // Resetamos para 100% do tamanho original
    setImagePosition({ x: 0, y: 0 });

    // Feedback visual para o usuário
    toast.dismiss(); // Remove toasts anteriores
    toast(`Imagem ${direction === 'next' ? 'seguinte' : 'anterior'}`, {
      position: "bottom-center",
      duration: 1000,
    });
  };

  // Funções para controlar o zoom
  const handleZoomIn = () => {
    setZoomLevel((prev) => {
      // Incremento de zoom em níveis fixos: 1.00, 1.25, 1.50, 1.75, 2.00
      if (prev < 1.25) return 1.25;
      if (prev < 1.50) return 1.50;
      if (prev < 1.75) return 1.75;
      if (prev < 2.00) return 2.00;
      // Se já estiver no máximo, mantém
      return 2.00;
    });
  };

  const handleZoomOut = () => {
    setZoomLevel((prev) => {
      // Decremento de zoom em níveis fixos: 2.00, 1.75, 1.50, 1.25, 1.00
      if (prev > 1.75) return 1.75;
      if (prev > 1.50) return 1.50;
      if (prev > 1.25) return 1.25;
      if (prev > 1.00) return 1.00;
      // Se já estiver no mínimo, mantém
      return 1.00;
    });
  };

  const handleZoomReset = () => {
    setZoomLevel(1.0); // Reset para 100%
    setImagePosition({ x: 0, y: 0 });
  };

  // Função para controlar o zoom com o scroll do mouse
  const handleWheel = (e: React.WheelEvent) => {
    // Impedir comportamento padrão
    e.preventDefault();

    // Determina a direção do scroll
    const delta = e.deltaY;

    // Throttle para evitar zoom muito rápido
    if (Date.now() - lastWheelTime < 150) return;
    setLastWheelTime(Date.now());

    if (delta > 0) {
      // Scroll para baixo - diminui o zoom
      if (zoomLevel > 1.0) {
        handleZoomOut();
      }
    } else {
      // Scroll para cima - aumenta o zoom
      if (zoomLevel < 2.0) {
        handleZoomIn();
      }
    }

    // Feedback visual para o usuário
    toast.dismiss(); // Remove toasts anteriores
    toast(`Zoom: ${Math.round(zoomLevel * 100)}%`, {
      position: "bottom-center",
      duration: 500,
    });
  };

  // Funções para arrastar a imagem quando ampliada
  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    // Permitir arrastar em qualquer nível de zoom
    setIsDragging(true);
    setDragStart({ x: e.clientX - imagePosition.x, y: e.clientY - imagePosition.y });
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (isDragging) {
      const newX = e.clientX - dragStart.x;
      const newY = e.clientY - dragStart.y;
      setImagePosition({ x: newX, y: newY });
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // Função para alternar o modo tela cheia
  const toggleFullscreen = () => {
    try {
      if (!document.fullscreenElement && lightboxRef.current) {
        // Entrar em modo tela cheia
        const element = lightboxRef.current;
        if (element.requestFullscreen) {
          element.requestFullscreen().catch(err => {
            console.error(`Erro ao entrar em modo tela cheia: ${err.message}`);
          });
        } else if ((element as any).webkitRequestFullscreen) {
          (element as any).webkitRequestFullscreen();
        } else if ((element as any).msRequestFullscreen) {
          (element as any).msRequestFullscreen();
        }
        setIsFullscreen(true);
      } else {
        // Sair do modo tela cheia
        if (document.exitFullscreen) {
          document.exitFullscreen().catch(err => {
            console.error(`Erro ao sair do modo tela cheia: ${err.message}`);
          });
        } else if ((document as any).webkitExitFullscreen) {
          (document as any).webkitExitFullscreen();
        } else if ((document as any).msExitFullscreen) {
          (document as any).msExitFullscreen();
        }
        setIsFullscreen(false);
      }
    } catch (error) {
      console.error("Erro ao alternar modo tela cheia:", error);
    }
  };

  // Detectar quando o usuário sai do modo tela cheia usando Esc
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, []);

  // Reset zoom e posição quando o lightbox é fechado
  useEffect(() => {
    if (lightboxOpen) {
      document.body.classList.add('lightbox-open');
      setZoomLevel(1.0); // Começamos com 100% do tamanho original
      setImagePosition({ x: 0, y: 0 });
    } else {
      document.body.classList.remove('lightbox-open');
      setZoomLevel(1.0); // Resetamos para 100% do tamanho original
      setImagePosition({ x: 0, y: 0 });
    }

    return () => {
      document.body.classList.remove('lightbox-open');
    };
  }, [lightboxOpen]);

  // Adicionar suporte a teclas de atalho
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!lightboxOpen) return;

      switch (e.key) {
        case 'ArrowLeft':
          navigateGallery('prev');
          break;
        case 'ArrowRight':
          navigateGallery('next');
          break;
        case '+':
        case '=':
          handleZoomIn();
          break;
        case '-':
          handleZoomOut();
          break;
        case '0':
          handleZoomReset();
          break;
        case 'Escape':
          setLightboxOpen(false);
          break;
        case 'f':
          toggleFullscreen();
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [lightboxOpen, currentImageIndex, zoomLevel]);

  return (
    <>
      <Dialog
        open={isOpen}
        onOpenChange={(open) => {
          // Não fechar o modal principal se o lightbox estiver aberto
          if (!open && lightboxOpen) {
            setLightboxOpen(false);
            return false; // Impede o fechamento do modal principal
          } else if (!open) {
            // Inicia a animação de fechamento
            setIsClosing(true);
            // Agenda o fechamento real após a animação
            const timer = setTimeout(() => {
              onOpenChange(false);
            }, 350);
            return () => clearTimeout(timer);
          } else {
            onOpenChange(open);
          }
        }}
        className="custom-dialog-positioning">
        <DialogContent
          className="max-w-[95vw] md:max-w-[85vw] w-full md:w-[1260px] p-0 overflow-hidden border-none bg-transparent"
          aria-describedby="modal-description"
          style={{
            position: 'fixed',
            top: '50%',
            right: isMobile ? '2.5vw' : '2vw',
            left: 'auto',
            transform: 'translateY(-50%)',
            animation: isClosing
              ? 'slideOutToRight 0.35s ease-in forwards'
              : 'slideInFromRight 0.65s ease-out',
            margin: 0,
            maxHeight: isMobile ? '98vh' : '90vh'
          }}
          data-state="open">
          <DialogClose asChild>
            <Button
              variant="ghost"
              size="icon"
              onClick={(e) => {
                e.preventDefault();
                // Inicia a animação de fechamento
                setIsClosing(true);
                // Agenda o fechamento real após a animação
                setTimeout(() => {
                  onOpenChange(false);
                }, 350);
              }}
              className="absolute right-3 top-3 z-[100] bg-black/60 hover:bg-black/80 text-white"
            >
              <X className="h-5 w-5" />
            </Button>
          </DialogClose>
          <DialogTitle className="sr-only">Visualização do Caso {title}</DialogTitle>
          <div id="modal-description" className="sr-only">Visualizador 3D e detalhes do caso odontológico</div>
          <div className="w-full h-[90vh] md:h-[73vh] p-3 pb-0 rounded-lg relative bg-gradient-to-r from-[#483c6c]/50 via-[#404040] to-black">

          <div className="w-full h-full flex flex-col">
            {/* Main content area with new layout */}
            <div className="flex-1 flex flex-col">
              {/* Main section with viewer on left and title/info on right */}
              <div className="flex flex-col md:flex-row gap-2 md:gap-3 h-[75%] md:h-[65%] mb-0">
                {/* HTML Viewer - positioned on the left */}
                <div className="w-full md:w-[70%] h-full">
                  {exocadHtmlUrl && (
                    <div className="exocad-viewer-container h-full relative rounded-lg overflow-hidden bg-gradient-to-br from-[#483c6c]/60 to-[#404040]">
                      {isIframeLoading && (
                        <div className="absolute inset-0 flex items-center justify-center bg-black/90 text-white z-10">
                          <div className="flex flex-col items-center">
                            <Loader2 className="h-10 w-10 animate-spin mb-2" />
                            <span className="text-sm uppercase">Carregando visualização 3D...</span>
                          </div>
                        </div>
                      )}

                      {iframeError ? (
                        <div className="error-container h-full flex items-center justify-center bg-black/40">
                          <div className="flex flex-col items-center text-center max-w-md">
                            <div className="error-icon">
                              <X className="h-8 w-8 text-red-400" />
                            </div>
                            <h3 className="error-title text-white font-medium">Não foi possível carregar a visualização</h3>
                            <p className="error-message text-white/70 text-sm mb-4">
                              O arquivo HTML do EXOCAD não pôde ser carregado diretamente.
                            </p>

                          </div>
                        </div>
                      ) : (
                        <iframe
                          ref={iframeRef}
                          src={getProcessedExocadUrl()}
                          className="exocad-viewer w-full h-full"
                          onLoad={handleIframeLoad}
                          onError={handleIframeError}
                          title="Exocad 3D Viewer"
                          sandbox="allow-same-origin allow-scripts allow-popups allow-forms"
                          allow="autoplay; fullscreen"
                        />
                      )}


                    </div>
                  )}
                </div>

                {/* Title and Information section - positioned on the right */}
                <div className="w-full md:w-[30%] h-full md:h-full">
                  {/* Title and type at the top left */}
                  <div className="flex flex-col items-start mb-3">
                    <h2 className="text-base sm:text-lg font-medium text-white uppercase break-words text-left w-full">{title}</h2>
                    <span className="px-2 py-0.5 bg-[#483c6c]/40 border border-[#483c6c]/60 rounded-full text-xs text-white uppercase mt-1">{type}</span>
                  </div>

                  {/* Information section */}
                  <div className="rounded-lg p-2 md:p-3 h-[calc(100%-3rem)] overflow-auto">
                    <h3 className="text-base font-medium text-white mb-2 md:mb-3">Informações do Modelo</h3>
                    <div className="space-y-2 md:space-y-3">
                      <div className="p-2 md:p-3 rounded-md border border-gray-700/30">
                        <h4 className="text-sm md:text-base font-medium text-white/90 mb-1 md:mb-2">Descrição</h4>
                        <p className="text-xs md:text-sm text-white/80">
                          {description || "Modelo 3D de caso odontológico criado com EXOCAD, demonstrando técnicas avançadas de modelagem digital para próteses dentárias."}
                        </p>
                      </div>



                      <div className="p-2 md:p-3 rounded-md border border-gray-700/30">
                        <h4 className="text-sm md:text-base font-medium text-white/90 mb-1 md:mb-2">Instruções de Visualização</h4>
                        <ul className="text-xs md:text-sm text-white/80 space-y-1 md:space-y-2 list-disc pl-4">
                          <li>Clique e arraste para rotacionar o modelo</li>
                          <li>Use o scroll para zoom</li>
                          <li>Clique duplo para centralizar</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Bottom section with enlarged image gallery layout - responsive */}
              <div className="h-[25%] md:h-[35%] mt-0 md:mt-3 pb-1 md:pb-3 flex items-end">
                <div className="rounded-lg p-2 md:p-3 w-full">
                  {/* Responsive grid of images - optimized for mobile */}
                  <div className="grid grid-cols-3 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-5 gap-1 md:gap-3 h-full overflow-x-auto">
                    {galleryImages.map((imagePath, idx) => (
                      <button
                        key={idx}
                        onClick={() => {
                          setCurrentImageIndex(idx);
                          setLightboxOpen(true);
                        }}
                        className="relative aspect-[4/3.2] rounded-md overflow-hidden border border-gray-700/30 hover:border-gray-500/50 transition-all group"
                        aria-label={`Ver imagem ${idx + 1}`}
                      >
                        {/* Real image */}
                        <img
                          src={imagePath}
                          alt={`Imagem ${idx + 1} do caso ${title}`}
                          className="w-full h-full object-contain scale-[0.85] md:scale-[0.825] transform-gpu"
                        />

                        {/* Hover overlay */}
                        <div className="absolute inset-0 bg-black/30 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                          <span className="text-sm text-white font-medium px-3 py-1 bg-black/50 rounded-full">Ampliar</span>
                        </div>
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>

    {/* Lightbox para visualização ampliada da imagem - Versão simplificada apenas com zoom */}
    {lightboxOpen && createPortal(
      <div
        className="fixed inset-0 bg-black/90 z-[9999]"
        onClick={(e) => {
          // Fechar o lightbox apenas se clicar diretamente no fundo
          if (e.target === e.currentTarget) {
            setLightboxOpen(false);
          }
        }}
      >
        <div
          ref={lightboxRef}
          className="absolute inset-0 flex items-center justify-center"
        >
          {/* Título e informações */}
          <div className="absolute top-6 left-0 right-0 text-center">
            <h3 className="text-xl font-medium text-white">{title} - Imagem {currentImageIndex + 1}</h3>
            <p className="text-sm text-white/70">Caso de {type.toLowerCase()}</p>
          </div>

          {/* Controles do lightbox */}
          <div className="absolute right-6 top-6 flex gap-2">
            {/* Botão de reset zoom */}
            {zoomLevel !== 1 && (
              <div
                className="bg-black/50 hover:bg-black/70 text-white rounded-full h-10 w-10 flex items-center justify-center cursor-pointer"
                onClick={() => {
                  setZoomLevel(1);
                  setImagePosition({ x: 0, y: 0 });
                }}
                title="Reset Zoom"
              >
                <span className="text-xs font-bold">1:1</span>
              </div>
            )}

            {/* Botão de fechar */}
            <div
              className="bg-black/50 hover:bg-black/70 text-white rounded-full h-10 w-10 flex items-center justify-center cursor-pointer"
              onClick={() => setLightboxOpen(false)}
              title="Fechar"
            >
              <X className="h-6 w-6" />
            </div>
          </div>

          {/* Navegação entre imagens */}
          {galleryImages.length > 1 && (
            <>
              {/* Botão anterior */}
              <div
                className="absolute left-6 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full h-12 w-12 flex items-center justify-center cursor-pointer"
                onClick={() => {
                  const newIndex = currentImageIndex > 0 ? currentImageIndex - 1 : galleryImages.length - 1;
                  setCurrentImageIndex(newIndex);
                  setZoomLevel(1);
                  setImagePosition({ x: 0, y: 0 });
                }}
                title="Imagem anterior"
              >
                <ChevronLeft className="h-6 w-6" />
              </div>

              {/* Botão próximo */}
              <div
                className="absolute right-6 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full h-12 w-12 flex items-center justify-center cursor-pointer"
                onClick={() => {
                  const newIndex = currentImageIndex < galleryImages.length - 1 ? currentImageIndex + 1 : 0;
                  setCurrentImageIndex(newIndex);
                  setZoomLevel(1);
                  setImagePosition({ x: 0, y: 0 });
                }}
                title="Próxima imagem"
              >
                <ChevronRight className="h-6 w-6" />
              </div>
            </>
          )}

          {/* Container da imagem com zoom */}
          <div className="w-full max-w-5xl h-[80%] flex items-center justify-center p-8">
            <div
              className="relative w-full h-full flex items-center justify-center"
              onMouseDown={handleMouseDown}
              onMouseMove={handleMouseMove}
              onMouseUp={handleMouseUp}
              onMouseLeave={handleMouseUp}
              onWheel={handleWheel}
              style={{ cursor: isDragging ? 'grabbing' : (zoomLevel > 1 ? 'grab' : 'default') }}
            >
              <img
                ref={imageRef}
                src={galleryImages[currentImageIndex]}
                alt={`Imagem ${currentImageIndex + 1} do caso ${title}`}
                className="object-contain transition-transform duration-200"
                style={{
                  transform: `scale(${zoomLevel}) translate(${imagePosition.x / zoomLevel}px, ${imagePosition.y / zoomLevel}px)`,
                  maxWidth: '100%',
                  maxHeight: '100%'
                }}
                draggable="false"
              />
            </div>
          </div>

          {/* Informações do lightbox */}
          <div className="absolute bottom-6 left-0 right-0 text-center">
            <div className="flex flex-col items-center gap-2">
              {/* Indicadores de navegação */}
              {galleryImages.length > 1 && (
                <div className="flex gap-1">
                  {galleryImages.map((_, idx) => (
                    <button
                      key={idx}
                      onClick={() => {
                        setCurrentImageIndex(idx);
                        setZoomLevel(1);
                        setImagePosition({ x: 0, y: 0 });
                      }}
                      className={`w-2 h-2 rounded-full transition-colors ${
                        idx === currentImageIndex ? 'bg-white' : 'bg-white/40 hover:bg-white/60'
                      }`}
                    />
                  ))}
                </div>
              )}

              {/* Informações de zoom e navegação */}
              <div className="inline-block bg-black/50 px-3 py-1 rounded-full text-sm text-white/80">
                {galleryImages.length > 1 && `${currentImageIndex + 1}/${galleryImages.length} • `}
                Zoom: {Math.round(zoomLevel * 100)}% • Roda do mouse para zoom
                {galleryImages.length > 1 && ' • ← → para navegar'}
              </div>
            </div>
          </div>
        </div>
      </div>,
      document.body
    )}
  </>
  );
};

export default HtmlContentModal;
