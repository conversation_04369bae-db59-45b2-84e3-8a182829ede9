import React, { useState, useEffect } from 'react';
import { useIsMobile } from '@/hooks/use-mobile';
import { useSketchfabConverter } from '@/hooks/use-model-loader';
import NativeModelViewer from './NativeModelViewer';
import { cn } from '@/lib/utils';
import { Settings, Monitor, Smartphone } from 'lucide-react';

interface HybridModelViewerProps {
  modelUrl: string;
  title: string;
  modelLoading?: boolean;
  transitioning?: boolean;
  onLoad?: () => void;
  introVisible?: boolean;
  className?: string;
  forceNative?: boolean; // Forçar uso do visualizador nativo
}

type ViewerMode = 'auto' | 'native' | 'sketchfab';

const HybridModelViewer: React.FC<HybridModelViewerProps> = ({
  modelUrl,
  title,
  modelLoading = false,
  transitioning = false,
  onLoad,
  introVisible = false,
  className,
  forceNative = false
}) => {
  const isMobile = useIsMobile();
  const { convertSketchfabUrl, isSketchfabUrl } = useSketchfabConverter();
  const [viewerMode, setViewerMode] = useState<ViewerMode>('auto');
  const [showModeSelector, setShowModeSelector] = useState(false);
  const [nativeModelUrl, setNativeModelUrl] = useState<string | null>(null);
  const [useNativeViewer, setUseNativeViewer] = useState(forceNative);

  // Determinar qual visualizador usar
  useEffect(() => {
    if (forceNative) {
      setUseNativeViewer(true);
      return;
    }

    if (viewerMode === 'native') {
      setUseNativeViewer(true);
    } else if (viewerMode === 'sketchfab') {
      setUseNativeViewer(false);
    } else {
      // Modo automático: usar nativo se disponível, senão Sketchfab
      if (isSketchfabUrl(modelUrl)) {
        const convertedUrl = convertSketchfabUrl(modelUrl);
        if (convertedUrl) {
          setNativeModelUrl(convertedUrl);
          // Verificar se o arquivo existe
          checkModelExists(convertedUrl).then(exists => {
            setUseNativeViewer(exists);
          });
        } else {
          setUseNativeViewer(false);
        }
      } else {
        // URL já é de modelo nativo
        setNativeModelUrl(modelUrl);
        setUseNativeViewer(true);
      }
    }
  }, [modelUrl, viewerMode, forceNative, isSketchfabUrl, convertSketchfabUrl]);

  // Verificar se modelo nativo existe
  const checkModelExists = async (url: string): Promise<boolean> => {
    try {
      const response = await fetch(url, { method: 'HEAD' });
      return response.ok;
    } catch {
      return false;
    }
  };

  // Renderizar visualizador Sketchfab (fallback)
  const renderSketchfabViewer = () => (
    <div className={cn(
      "fixed inset-0 w-full h-full overflow-hidden pointer-events-auto",
      introVisible || transitioning ? "opacity-0" : "opacity-100 transition-opacity duration-500"
    )}>
      <iframe
        title={`3D MODEL - ${title.toUpperCase()}`}
        src={`${modelUrl}${modelUrl.includes('?') ? '&' : '?'}autospin=1&autostart=1&ui_controls=0&ui_infos=0&ui_watermark=0&ui_stop=0&ui_inspector=0&ui_ar=0&ui_help=0&ui_settings=0&ui_vr=0&ui_fullscreen=0&ui_animations=0&transparent=1${isMobile ? '&zoom=0.5&camera_zoom=0.75' : ''}`}
        className="w-full h-full scale-125"
        frameBorder="0"
        allow="autoplay; fullscreen"
        onLoad={onLoad}
      />
      
      {/* Indicador de modo Sketchfab */}
      <div className="absolute bottom-4 right-4 z-10">
        <div className="bg-orange-500/80 backdrop-blur-sm rounded-lg px-3 py-1">
          <span className="text-white text-xs font-medium">Sketchfab Mode</span>
        </div>
      </div>
    </div>
  );

  // Renderizar seletor de modo (apenas em desenvolvimento)
  const renderModeSelector = () => {
    if (process.env.NODE_ENV !== 'development') return null;

    return (
      <div className="absolute top-4 left-4 z-20">
        <div className="flex flex-col gap-2">
          <button
            onClick={() => setShowModeSelector(!showModeSelector)}
            className="bg-black/50 hover:bg-black/70 text-white p-2 rounded-lg backdrop-blur-sm transition-colors"
            title="Configurações do Visualizador"
          >
            <Settings className="h-4 w-4" />
          </button>

          {showModeSelector && (
            <div className="bg-black/80 backdrop-blur-sm rounded-lg p-3 space-y-2 min-w-[180px]">
              <p className="text-white text-xs font-medium mb-2">Modo do Visualizador:</p>
              
              <button
                onClick={() => setViewerMode('auto')}
                className={cn(
                  "w-full flex items-center gap-2 text-sm transition-colors p-2 rounded",
                  viewerMode === 'auto' ? "bg-blue-500 text-white" : "text-gray-300 hover:text-white"
                )}
              >
                <Monitor className="h-3 w-3" />
                Automático
              </button>
              
              <button
                onClick={() => setViewerMode('native')}
                className={cn(
                  "w-full flex items-center gap-2 text-sm transition-colors p-2 rounded",
                  viewerMode === 'native' ? "bg-green-500 text-white" : "text-gray-300 hover:text-white"
                )}
              >
                <Smartphone className="h-3 w-3" />
                Nativo (Three.js)
              </button>
              
              <button
                onClick={() => setViewerMode('sketchfab')}
                className={cn(
                  "w-full flex items-center gap-2 text-sm transition-colors p-2 rounded",
                  viewerMode === 'sketchfab' ? "bg-orange-500 text-white" : "text-gray-300 hover:text-white"
                )}
              >
                <Monitor className="h-3 w-3" />
                Sketchfab
              </button>

              <div className="border-t border-white/20 mt-2 pt-2">
                <p className="text-xs text-gray-400">
                  Atual: {useNativeViewer ? 'Nativo' : 'Sketchfab'}
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className={cn("relative", className)}>
      {/* Renderizar visualizador apropriado */}
      {useNativeViewer && nativeModelUrl ? (
        <NativeModelViewer
          modelUrl={nativeModelUrl}
          title={title}
          modelLoading={modelLoading}
          transitioning={transitioning}
          onLoad={onLoad}
          introVisible={introVisible}
        />
      ) : (
        renderSketchfabViewer()
      )}

      {/* Seletor de modo (desenvolvimento) */}
      {renderModeSelector()}

      {/* Indicador de performance */}
      {useNativeViewer && (
        <div className="absolute bottom-4 left-4 z-10">
          <div className="bg-green-500/80 backdrop-blur-sm rounded-lg px-3 py-1">
            <span className="text-white text-xs font-medium">
              Native 3D {isMobile ? '(Mobile Optimized)' : ''}
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default HybridModelViewer;
