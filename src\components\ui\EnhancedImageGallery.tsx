import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence, PanInfo } from 'framer-motion';
import { 
  X, 
  ChevronLeft, 
  ChevronRight, 
  ZoomIn, 
  ZoomOut, 
  RotateCw,
  Download,
  Share2,
  Grid3X3,
  Maximize2,
  Info
} from 'lucide-react';
import { cn } from '@/lib/utils';
import OptimizedImage from './OptimizedImage';

interface GalleryImage {
  id: string;
  src: string;
  alt: string;
  title?: string;
  description?: string;
  thumbnail?: string;
}

interface EnhancedImageGalleryProps {
  images: GalleryImage[];
  initialIndex?: number;
  isOpen: boolean;
  onClose: () => void;
  onImageChange?: (index: number) => void;
  showThumbnails?: boolean;
  showInfo?: boolean;
  allowDownload?: boolean;
  allowShare?: boolean;
  className?: string;
}

const EnhancedImageGallery: React.FC<EnhancedImageGalleryProps> = ({
  images,
  initialIndex = 0,
  isOpen,
  onClose,
  onImageChange,
  showThumbnails = true,
  showInfo = true,
  allowDownload = true,
  allowShare = true,
  className
}) => {
  const [currentIndex, setCurrentIndex] = useState(initialIndex);
  const [zoom, setZoom] = useState(1);
  const [rotation, setRotation] = useState(0);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [showControls, setShowControls] = useState(true);
  const [showThumbnailGrid, setShowThumbnailGrid] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);

  const currentImage = images[currentIndex];

  // Reset ao mudar imagem
  useEffect(() => {
    setZoom(1);
    setRotation(0);
    setPosition({ x: 0, y: 0 });
    setImageLoaded(false);
  }, [currentIndex]);

  // Notificar mudança de imagem
  useEffect(() => {
    onImageChange?.(currentIndex);
  }, [currentIndex, onImageChange]);

  // Controles de teclado
  useEffect(() => {
    if (!isOpen) return;

    const handleKeyPress = (event: KeyboardEvent) => {
      switch (event.key) {
        case 'ArrowLeft':
          event.preventDefault();
          goToPrevious();
          break;
        case 'ArrowRight':
          event.preventDefault();
          goToNext();
          break;
        case 'Escape':
          event.preventDefault();
          onClose();
          break;
        case '+':
        case '=':
          event.preventDefault();
          zoomIn();
          break;
        case '-':
          event.preventDefault();
          zoomOut();
          break;
        case '0':
          event.preventDefault();
          resetZoom();
          break;
        case 'r':
        case 'R':
          event.preventDefault();
          rotate();
          break;
        case 'i':
        case 'I':
          event.preventDefault();
          setShowControls(prev => !prev);
          break;
        case 'g':
        case 'G':
          event.preventDefault();
          setShowThumbnailGrid(prev => !prev);
          break;
      }
    };

    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [isOpen]);

  // Auto-hide controles
  useEffect(() => {
    if (!isOpen) return;

    let timeout: NodeJS.Timeout;
    
    const resetTimeout = () => {
      clearTimeout(timeout);
      setShowControls(true);
      timeout = setTimeout(() => setShowControls(false), 3000);
    };

    const handleMouseMove = () => resetTimeout();
    
    resetTimeout();
    document.addEventListener('mousemove', handleMouseMove);
    
    return () => {
      clearTimeout(timeout);
      document.removeEventListener('mousemove', handleMouseMove);
    };
  }, [isOpen]);

  // Navegação
  const goToNext = useCallback(() => {
    setCurrentIndex(prev => (prev + 1) % images.length);
  }, [images.length]);

  const goToPrevious = useCallback(() => {
    setCurrentIndex(prev => (prev - 1 + images.length) % images.length);
  }, [images.length]);

  const goToImage = useCallback((index: number) => {
    setCurrentIndex(index);
    setShowThumbnailGrid(false);
  }, []);

  // Controles de zoom
  const zoomIn = useCallback(() => {
    setZoom(prev => Math.min(prev * 1.5, 5));
  }, []);

  const zoomOut = useCallback(() => {
    setZoom(prev => Math.max(prev / 1.5, 0.5));
  }, []);

  const resetZoom = useCallback(() => {
    setZoom(1);
    setPosition({ x: 0, y: 0 });
  }, []);

  // Rotação
  const rotate = useCallback(() => {
    setRotation(prev => (prev + 90) % 360);
  }, []);

  // Drag para pan
  const handleDrag = useCallback((event: any, info: PanInfo) => {
    if (zoom > 1) {
      setPosition(prev => ({
        x: prev.x + info.delta.x,
        y: prev.y + info.delta.y
      }));
    }
  }, [zoom]);

  // Swipe para navegação
  const handleSwipe = useCallback((event: any, info: PanInfo) => {
    if (zoom === 1 && Math.abs(info.offset.x) > 100) {
      if (info.offset.x > 0) {
        goToPrevious();
      } else {
        goToNext();
      }
    }
  }, [zoom, goToNext, goToPrevious]);

  // Download da imagem
  const downloadImage = useCallback(async () => {
    if (!currentImage) return;

    try {
      const response = await fetch(currentImage.src);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      
      const link = document.createElement('a');
      link.href = url;
      link.download = currentImage.title || `image-${currentIndex + 1}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Erro ao baixar imagem:', error);
    }
  }, [currentImage, currentIndex]);

  // Compartilhar imagem
  const shareImage = useCallback(async () => {
    if (!currentImage) return;

    if (navigator.share) {
      try {
        await navigator.share({
          title: currentImage.title || 'Imagem',
          text: currentImage.description || '',
          url: currentImage.src
        });
      } catch (error) {
        console.log('Compartilhamento cancelado');
      }
    } else {
      // Fallback: copiar URL
      try {
        await navigator.clipboard.writeText(currentImage.src);
        // Aqui você poderia mostrar um toast
      } catch (error) {
        console.error('Erro ao copiar URL:', error);
      }
    }
  }, [currentImage]);

  if (!isOpen || !currentImage) return null;

  return (
    <AnimatePresence>
      <motion.div
        className={cn(
          "fixed inset-0 z-50 bg-black/95 backdrop-blur-sm",
          className
        )}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.3 }}
      >
        {/* Controles superiores */}
        <AnimatePresence>
          {showControls && (
            <motion.div
              className="absolute top-0 left-0 right-0 z-10 bg-gradient-to-b from-black/50 to-transparent p-4"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <span className="text-white text-sm">
                    {currentIndex + 1} de {images.length}
                  </span>
                  {currentImage.title && (
                    <h3 className="text-white font-medium">{currentImage.title}</h3>
                  )}
                </div>

                <div className="flex items-center gap-2">
                  {/* Controles de zoom */}
                  <button
                    onClick={zoomOut}
                    className="p-2 text-white hover:bg-white/20 rounded-lg transition-colors"
                    disabled={zoom <= 0.5}
                  >
                    <ZoomOut className="w-5 h-5" />
                  </button>
                  
                  <span className="text-white text-sm min-w-[3rem] text-center">
                    {Math.round(zoom * 100)}%
                  </span>
                  
                  <button
                    onClick={zoomIn}
                    className="p-2 text-white hover:bg-white/20 rounded-lg transition-colors"
                    disabled={zoom >= 5}
                  >
                    <ZoomIn className="w-5 h-5" />
                  </button>

                  {/* Rotação */}
                  <button
                    onClick={rotate}
                    className="p-2 text-white hover:bg-white/20 rounded-lg transition-colors"
                  >
                    <RotateCw className="w-5 h-5" />
                  </button>

                  {/* Reset */}
                  <button
                    onClick={resetZoom}
                    className="p-2 text-white hover:bg-white/20 rounded-lg transition-colors"
                  >
                    <Maximize2 className="w-5 h-5" />
                  </button>

                  {/* Grid de thumbnails */}
                  {showThumbnails && (
                    <button
                      onClick={() => setShowThumbnailGrid(!showThumbnailGrid)}
                      className="p-2 text-white hover:bg-white/20 rounded-lg transition-colors"
                    >
                      <Grid3X3 className="w-5 h-5" />
                    </button>
                  )}

                  {/* Download */}
                  {allowDownload && (
                    <button
                      onClick={downloadImage}
                      className="p-2 text-white hover:bg-white/20 rounded-lg transition-colors"
                    >
                      <Download className="w-5 h-5" />
                    </button>
                  )}

                  {/* Compartilhar */}
                  {allowShare && (
                    <button
                      onClick={shareImage}
                      className="p-2 text-white hover:bg-white/20 rounded-lg transition-colors"
                    >
                      <Share2 className="w-5 h-5" />
                    </button>
                  )}

                  {/* Fechar */}
                  <button
                    onClick={onClose}
                    className="p-2 text-white hover:bg-white/20 rounded-lg transition-colors"
                  >
                    <X className="w-5 h-5" />
                  </button>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Imagem principal */}
        <div className="absolute inset-0 flex items-center justify-center">
          <motion.div
            className="relative max-w-full max-h-full cursor-grab active:cursor-grabbing"
            drag={zoom > 1}
            dragConstraints={{ left: -200, right: 200, top: -200, bottom: 200 }}
            onDrag={handleDrag}
            onDragStart={() => setIsDragging(true)}
            onDragEnd={(event, info) => {
              setIsDragging(false);
              handleSwipe(event, info);
            }}
            animate={{
              scale: zoom,
              rotate: rotation,
              x: position.x,
              y: position.y
            }}
            transition={{ type: "spring", stiffness: 300, damping: 30 }}
          >
            <OptimizedImage
              src={currentImage.src}
              alt={currentImage.alt}
              className="max-w-[90vw] max-h-[90vh] object-contain"
              onLoad={() => setImageLoaded(true)}
              priority
            />
          </motion.div>
        </div>

        {/* Navegação lateral */}
        <AnimatePresence>
          {showControls && images.length > 1 && (
            <>
              <motion.button
                className="absolute left-4 top-1/2 -translate-y-1/2 p-3 text-white hover:bg-white/20 rounded-full transition-colors"
                onClick={goToPrevious}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <ChevronLeft className="w-6 h-6" />
              </motion.button>

              <motion.button
                className="absolute right-4 top-1/2 -translate-y-1/2 p-3 text-white hover:bg-white/20 rounded-full transition-colors"
                onClick={goToNext}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <ChevronRight className="w-6 h-6" />
              </motion.button>
            </>
          )}
        </AnimatePresence>

        {/* Grid de thumbnails */}
        <AnimatePresence>
          {showThumbnailGrid && showThumbnails && (
            <motion.div
              className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4"
              initial={{ opacity: 0, y: 100 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 100 }}
            >
              <div className="flex gap-2 overflow-x-auto pb-2">
                {images.map((image, index) => (
                  <motion.button
                    key={image.id}
                    className={cn(
                      "flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-all",
                      index === currentIndex 
                        ? "border-white shadow-lg" 
                        : "border-transparent hover:border-white/50"
                    )}
                    onClick={() => goToImage(index)}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <OptimizedImage
                      src={image.thumbnail || image.src}
                      alt={image.alt}
                      className="w-full h-full object-cover"
                    />
                  </motion.button>
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Informações da imagem */}
        <AnimatePresence>
          {showInfo && showControls && currentImage.description && (
            <motion.div
              className="absolute bottom-4 left-4 right-4 bg-black/50 backdrop-blur-sm rounded-lg p-4 text-white"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 20 }}
            >
              <p className="text-sm">{currentImage.description}</p>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    </AnimatePresence>
  );
};

export default EnhancedImageGallery;
