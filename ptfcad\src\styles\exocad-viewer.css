/* Estilos específicos para visualização de arquivos HTML do EXOCAD */

/* Ajuste de cores de fundo para o visualizador 3D */
.styled-html-content {
  color: white !important;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Ajustes para o visualizador 3D do EXOCAD */
iframe.exocad-viewer {
  background-color: transparent !important;
  border: none !important;
}

/* Estilos para o conteúdo HTML do EXOCAD quando renderizado */
.exocad-html-content {
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
}

/* Ajustes para o visualizador 3D do EXOCAD */
.exocad-viewer-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-radius: 0.5rem;
  background-color: rgba(0, 0, 0, 0.8);
}

/* Estilos para o botão de fallback */
.fallback-button {
  position: absolute;
  top: 1rem;
  right: 1rem;
  z-index: 20;
  background-color: rgba(255, 255, 255, 0.8);
  color: black;
  border: none;
  border-radius: 0.25rem;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.fallback-button:hover {
  background-color: white;
}

/* Estilos para a mensagem de erro */
.error-container {
  position: absolute;
  inset: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.9);
  color: white;
  z-index: 10;
  padding: 2rem;
  text-align: center;
}

.error-icon {
  background-color: rgba(239, 68, 68, 0.2);
  padding: 0.75rem;
  border-radius: 9999px;
  margin-bottom: 1rem;
}

.error-title {
  font-size: 1.125rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.error-message {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 1.5rem;
  max-width: 24rem;
}

/* Ajustes para o fundo do visualizador 3D */
.renderer-background {
  background-color: transparent !important;
}
