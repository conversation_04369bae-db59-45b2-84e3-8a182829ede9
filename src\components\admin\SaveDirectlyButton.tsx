import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Loader2, Save, Check, AlertTriangle } from 'lucide-react';
import { toast } from '@/components/ui/sonner';
import { saveProjectsData, canSaveDirectly } from '@/services/fileService';
import { Project } from '@/components/ProjectGallery';

interface SaveDirectlyButtonProps {
  projects: Project[];
  onSuccess?: () => void;
}

const SaveDirectlyButton: React.FC<SaveDirectlyButtonProps> = ({
  projects,
  onSuccess
}) => {
  const [isSaving, setIsSaving] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const isSupported = canSaveDirectly();

  const handleSave = async () => {
    if (!isSupported) {
      toast.error('Esta funcionalidade não está disponível no ambiente atual.');
      return;
    }

    setIsSaving(true);
    setIsSuccess(false);

    try {
      // Verificar se o servidor está disponível
      try {
        await fetch('http://localhost:3001/api/list-files?folder=images', {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' },
          // Adicionar um timeout para não bloquear a interface
          signal: AbortSignal.timeout(1000)
        });
      } catch (error) {
        throw new Error('Servidor não está disponível. Certifique-se de que o servidor está rodando com "npm run server".');
      }

      await saveProjectsData(projects);

      setIsSuccess(true);
      toast.success('Dados salvos com sucesso no arquivo projectsData.ts!');

      if (onSuccess) {
        onSuccess();
      }

      // Reset success state after 3 seconds
      setTimeout(() => {
        setIsSuccess(false);
      }, 3000);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
      toast.error(`Erro ao salvar os dados: ${errorMessage}`);
      console.error('Erro ao salvar diretamente:', error);
    } finally {
      setIsSaving(false);
    }
  };

  if (!isSupported) {
    return (
      <div className="inline-block relative group">
        <Button
          variant="outline"
          className="bg-gray-800 text-gray-400 border-gray-700 cursor-not-allowed"
          disabled
        >
          <AlertTriangle className="h-4 w-4 mr-2 text-yellow-500" />
          Salvar Diretamente (Não Disponível)
        </Button>

        <div className="absolute left-0 bottom-full mb-2 w-64 bg-gray-800 text-xs text-gray-300 p-2 rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity z-50 pointer-events-none">
          Esta funcionalidade só está disponível em ambiente de desenvolvimento ou com um servidor configurado para salvar arquivos.
        </div>
      </div>
    );
  }

  return (
    <div className="inline-block relative group">
      <Button
        onClick={handleSave}
        disabled={isSaving}
        className={isSuccess ? 'bg-green-600 hover:bg-green-700' : ''}
      >
        {isSaving ? (
          <>
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            Salvando no Servidor...
          </>
        ) : isSuccess ? (
          <>
            <Check className="h-4 w-4 mr-2" />
            Salvo com Sucesso!
          </>
        ) : (
          <>
            <Save className="h-4 w-4 mr-2" />
            Salvar no Servidor
          </>
        )}
      </Button>

      <div className="absolute left-0 bottom-full mb-2 w-80 bg-gray-800 text-xs text-gray-300 p-2 rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity z-50 pointer-events-none">
        Salva os dados diretamente no arquivo projectsData.ts através do servidor local. As alterações serão aplicadas imediatamente.
      </div>
    </div>
  );
};

export default SaveDirectlyButton;
