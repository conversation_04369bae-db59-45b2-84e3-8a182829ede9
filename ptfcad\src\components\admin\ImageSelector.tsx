import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { FolderOpen, Image as ImageIcon, Search, Check } from 'lucide-react';
import { listFiles } from '@/services/fileService';

// Estrutura de pastas de imagens predefinidas
const IMAGE_FOLDERS = [
  {
    name: 'Casos',
    path: '/arquivosCasos',
    subfolders: [
      { name: 'Caso 1', path: '/arquivosCasos/caso1' },
      { name: 'Caso 2', path: '/arquivosCasos/caso2' },
      { name: 'Caso 3', path: '/arquivosCasos/caso3' },
    ]
  },
  {
    name: 'Imagens Gerais',
    path: '/images',
    subfolders: []
  }
];

// Imagens de exemplo em cada pasta (em um ambiente real, isso seria carregado do servidor)
const SAMPLE_IMAGES: Record<string, string[]> = {
  '/arquivosCasos/caso1': [
    '/arquivosCasos/caso1/20250514-202931-2023-06-08_00059-007-0.png',
    '/arquivosCasos/caso1/20250514-202958-2023-06-08_00059-007-1.png',
    '/arquivosCasos/caso1/20250514-203015-2023-06-08_00059-007-2.png',
    '/arquivosCasos/caso1/20250514-203045-2023-06-08_00059-007-3.png',
    '/arquivosCasos/caso1/20250514-203221-2023-06-08_00059-007-5.png',
  ],
  '/arquivosCasos/caso2': [
    '/arquivosCasos/caso2/(IMAGEM) 44-45 COROA - PRE MOLARES.png',
    '/arquivosCasos/caso2/(IMAGEM) 44-45 COROA - PRE MOLARES (2).png',
  ],
  '/arquivosCasos/caso3': [],
  '/images': [
    '/images/thumbnail1.jpg',
    '/images/thumbnail2.jpg',
    '/images/thumbnail3.jpg',
  ]
};

interface ImageSelectorProps {
  onSelectImage: (imagePath: string) => void;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  title?: string;
}

const ImageSelector: React.FC<ImageSelectorProps> = ({
  onSelectImage,
  isOpen,
  onOpenChange,
  title = 'Selecionar Imagem'
}) => {
  const [selectedFolder, setSelectedFolder] = useState<string>('/arquivosCasos/caso1');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [images, setImages] = useState<string[]>([]);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  // Carregar imagens da pasta selecionada
  useEffect(() => {
    const loadImages = async () => {
      try {
        // Verificar se o servidor está disponível
        try {
          await fetch('http://localhost:3001/api/list-files?folder=images', {
            method: 'GET',
            headers: { 'Content-Type': 'application/json' },
            // Adicionar um timeout para não bloquear a interface
            signal: AbortSignal.timeout(1000)
          });
        } catch (error) {
          console.warn('Servidor não disponível, usando imagens de exemplo');
          // Fallback para imagens de exemplo se o servidor não estiver disponível
          setImages(SAMPLE_IMAGES[selectedFolder] || []);
          return;
        }

        // Remover a barra inicial para compatibilidade com a API
        const folderPath = selectedFolder.startsWith('/') ? selectedFolder.substring(1) : selectedFolder;
        const files = await listFiles(folderPath);

        if (files.length > 0) {
          setImages(files);
        } else {
          // Se não houver arquivos, usar imagens de exemplo
          setImages(SAMPLE_IMAGES[selectedFolder] || []);
        }
      } catch (error) {
        console.error('Erro ao carregar imagens:', error);
        // Fallback para imagens de exemplo se o servidor não estiver disponível
        setImages(SAMPLE_IMAGES[selectedFolder] || []);
      }
    };

    loadImages();
  }, [selectedFolder]);

  // Filtrar imagens com base na pesquisa
  const filteredImages = images.filter(img =>
    img.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Função para selecionar uma imagem
  const handleSelectImage = (imagePath: string) => {
    setSelectedImage(imagePath);
  };

  // Função para confirmar a seleção
  const handleConfirmSelection = () => {
    if (selectedImage) {
      onSelectImage(selectedImage);
      onOpenChange(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="bg-gray-900 text-white border-gray-800 max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
        </DialogHeader>

        <div className="flex flex-1 overflow-hidden">
          {/* Painel de pastas */}
          <div className="w-64 bg-gray-800 p-3 overflow-y-auto border-r border-gray-700">
            <h3 className="text-sm font-medium mb-2 text-gray-400">Pastas de Imagens</h3>
            <div className="space-y-1">
              {IMAGE_FOLDERS.map(folder => (
                <div key={folder.path} className="space-y-1">
                  <Button
                    variant="ghost"
                    className={`w-full justify-start text-left ${selectedFolder === folder.path ? 'bg-gray-700' : ''}`}
                    onClick={() => setSelectedFolder(folder.path)}
                  >
                    <FolderOpen className="h-4 w-4 mr-2" />
                    {folder.name}
                  </Button>

                  {folder.subfolders.map(subfolder => (
                    <Button
                      key={subfolder.path}
                      variant="ghost"
                      className={`w-full justify-start text-left pl-6 ${selectedFolder === subfolder.path ? 'bg-gray-700' : ''}`}
                      onClick={() => setSelectedFolder(subfolder.path)}
                    >
                      <FolderOpen className="h-4 w-4 mr-2" />
                      {subfolder.name}
                    </Button>
                  ))}
                </div>
              ))}
            </div>
          </div>

          {/* Painel de imagens */}
          <div className="flex-1 flex flex-col overflow-hidden">
            {/* Barra de pesquisa */}
            <div className="p-3 border-b border-gray-700">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-500" />
                <Input
                  placeholder="Pesquisar imagens..."
                  className="pl-8 bg-gray-800 border-gray-700"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>

            {/* Grade de imagens */}
            <div className="flex-1 p-3 overflow-y-auto">
              {filteredImages.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-full text-gray-500">
                  <ImageIcon className="h-12 w-12 mb-2" />
                  <p>Nenhuma imagem encontrada nesta pasta</p>
                </div>
              ) : (
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3">
                  {filteredImages.map((img) => (
                    <div
                      key={img}
                      className={`relative group cursor-pointer rounded-lg overflow-hidden border ${
                        selectedImage === img ? 'border-blue-500 ring-2 ring-blue-500' : 'border-gray-700'
                      }`}
                      onClick={() => handleSelectImage(img)}
                    >
                      <img
                        src={img}
                        alt={img.split('/').pop() || ''}
                        className="w-full aspect-[4/3] object-cover"
                        onError={(e) => {
                          // Fallback para imagens que não carregam
                          (e.target as HTMLImageElement).src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgZmlsbD0iIzI0MjQyNCIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMTgiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGFsaWdubWVudC1iYXNlbGluZT0ibWlkZGxlIiBmaWxsPSIjNTU1Ij5JbWFnZW0gbsOjbyBkaXNwb27DrXZlbDwvdGV4dD48L3N2Zz4=';
                        }}
                      />

                      {selectedImage === img && (
                        <div className="absolute top-2 right-2 bg-blue-500 rounded-full p-1">
                          <Check className="h-4 w-4 text-white" />
                        </div>
                      )}

                      <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-end">
                        <div className="p-2 w-full text-xs truncate">
                          {img.split('/').pop()}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        <DialogFooter className="border-t border-gray-700 pt-3">
          <div className="flex-1 text-xs text-gray-400 truncate">
            {selectedImage ? `Selecionado: ${selectedImage}` : 'Nenhuma imagem selecionada'}
          </div>
          <Button variant="outline" onClick={() => onOpenChange(false)}>Cancelar</Button>
          <Button
            onClick={handleConfirmSelection}
            disabled={!selectedImage}
          >
            Selecionar Imagem
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ImageSelector;
