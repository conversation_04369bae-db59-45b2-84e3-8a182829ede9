import React, { Suspense, useRef, useState, useEffect } from 'react';
import { <PERSON><PERSON>, useFrame, useLoader } from '@react-three/fiber';
import { OrbitControls, Html, Environment, ContactShadows } from '@react-three/drei';
import { STLLoader } from 'three-stdlib';
import { useIsMobile } from '@/hooks/use-mobile';
import { cn } from '@/lib/utils';
import { Loader2, Eye, EyeOff, RotateCcw, Download, Palette } from 'lucide-react';
import * as THREE from 'three';

interface ModelPart {
  id: string;
  name: string;
  url: string;
  color?: string;
  visible?: boolean;
  position?: [number, number, number];
  rotation?: [number, number, number];
  scale?: [number, number, number];
}

interface STLModelViewerProps {
  models: ModelPart[];
  title: string;
  autoRotate?: boolean;
  showControls?: boolean;
  className?: string;
  onLoad?: () => void;
}

// Componente para renderizar um modelo STL individual
const STLModel: React.FC<{
  model: ModelPart;
  onLoad?: () => void;
}> = ({ model, onLoad }) => {
  const meshRef = useRef<THREE.Mesh>(null);

  // Verificar se a URL é válida antes de tentar carregar
  if (!model.url || model.url.trim() === '') {
    return null;
  }

  let geometry;
  try {
    geometry = useLoader(STLLoader, model.url);

    // Normalizar o tamanho do modelo automaticamente
    if (geometry) {
      geometry.computeBoundingBox();
      const box = geometry.boundingBox;
      if (box) {
        const size = box.getSize(new THREE.Vector3());
        const maxDimension = Math.max(size.x, size.y, size.z);

        // Escalar para um tamanho padrão (máximo 5 unidades)
        const targetSize = 5;
        const scale = targetSize / maxDimension;
        geometry.scale(scale, scale, scale);

        // Centralizar o modelo
        const center = box.getCenter(new THREE.Vector3());
        geometry.translate(-center.x * scale, -center.y * scale, -center.z * scale);
      }
    }
  } catch (error) {
    console.warn('Erro ao carregar modelo STL:', model.url, error);
    return null;
  }

  useEffect(() => {
    if (geometry && onLoad) {
      onLoad();
    }
  }, [geometry, onLoad]);

  // Rotação automática suave
  useFrame((state, delta) => {
    if (meshRef.current) {
      meshRef.current.rotation.y += delta * 0.3; // Rotação mais visível
    }
  });

  if (!model.visible || !geometry) return null;

  return (
    <mesh
      ref={meshRef}
      geometry={geometry}
      position={model.position || [0, 0, 0]}
      rotation={model.rotation || [0, 0, 0]}
      scale={model.scale || [1, 1, 1]}
    >
      <meshStandardMaterial
        color={model.color || '#ffffff'}
        metalness={0.1}
        roughness={0.3}
      />
    </mesh>
  );
};

// Componente de loading
const LoadingSpinner = () => (
  <Html center>
    <div className="flex flex-col items-center justify-center text-white">
      <Loader2 className="h-8 w-8 animate-spin mb-2" />
      <p className="text-sm opacity-75">Carregando modelos STL...</p>
    </div>
  </Html>
);

// Componente principal
const STLModelViewer: React.FC<STLModelViewerProps> = ({
  models,
  title,
  autoRotate = true,
  showControls = true,
  className,
  onLoad
}) => {
  const isMobile = useIsMobile();
  const controlsRef = useRef<any>(null);
  const [modelVisibility, setModelVisibility] = useState<Record<string, boolean>>(
    models.reduce((acc, model) => ({
      ...acc,
      [model.id]: model.visible !== false
    }), {})
  );
  const [showControlPanel, setShowControlPanel] = useState(false);
  const [loadedModels, setLoadedModels] = useState<Set<string>>(new Set());

  // Configurações da câmera otimizadas - MUITO MAIS DISTANTE
  const cameraConfig = {
    position: isMobile ? [0, 0, 25] : [0, 0, 30],
    fov: isMobile ? 35 : 30,
  };

  // Toggle de visibilidade do modelo
  const toggleModelVisibility = (modelId: string) => {
    setModelVisibility(prev => ({
      ...prev,
      [modelId]: !prev[modelId]
    }));
  };

  // Reset da câmera
  const resetCamera = () => {
    if (controlsRef.current) {
      controlsRef.current.reset();
    }
  };

  // Callback quando modelo carrega
  const handleModelLoad = (modelId: string) => {
    setLoadedModels(prev => new Set(prev).add(modelId));
    
    // Se todos os modelos carregaram, chamar onLoad
    if (loadedModels.size + 1 === models.length && onLoad) {
      onLoad();
    }
  };

  // Cores predefinidas para os modelos
  const predefinedColors = [
    '#e3f2fd', // Azul claro
    '#f3e5f5', // Rosa claro
    '#e8f5e8', // Verde claro
    '#fff3e0', // Laranja claro
    '#fce4ec', // Rosa
    '#e1f5fe'  // Ciano claro
  ];

  return (
    <div className={cn("relative w-full h-full", className)}>
      {/* Canvas 3D */}
      <Canvas
        camera={cameraConfig}
        shadows
        className="w-full h-full"
        gl={{ 
          antialias: true, 
          alpha: true,
          powerPreference: isMobile ? "low-power" : "high-performance"
        }}
      >
        {/* Iluminação otimizada */}
        <ambientLight intensity={0.8} />
        <directionalLight
          position={[10, 10, 5]}
          intensity={1.2}
          castShadow
          shadow-mapSize-width={1024}
          shadow-mapSize-height={1024}
        />
        <pointLight position={[-10, -10, -10]} intensity={0.4} />
        <pointLight position={[10, -10, 10]} intensity={0.3} color="#ffffff" />
        
        {/* Ambiente */}
        <Environment preset="studio" />
        
        {/* Sombras de contato */}
        <ContactShadows 
          position={[0, -2, 0]} 
          opacity={0.3} 
          scale={15} 
          blur={2} 
          far={6} 
        />
        
        {/* Modelos STL */}
        <Suspense fallback={<LoadingSpinner />}>
          <group>
            {models.map((model, index) => (
              <STLModel
                key={model.id}
                model={{
                  ...model,
                  visible: modelVisibility[model.id],
                  color: model.color || predefinedColors[index % predefinedColors.length]
                }}
                onLoad={() => handleModelLoad(model.id)}
              />
            ))}
          </group>
        </Suspense>
        
        {/* Controles de órbita */}
        <OrbitControls
          ref={controlsRef}
          enablePan={true}
          enableZoom={true}
          enableRotate={true}
          minDistance={5}
          maxDistance={100}
          autoRotate={autoRotate}
          autoRotateSpeed={0.3}
        />
      </Canvas>

      {/* Controles de interface */}
      {showControls && (
        <div className="absolute top-4 right-4 z-10">
          <div className="flex flex-col gap-2">
            {/* Toggle de painel de controles */}
            <button
              onClick={() => setShowControlPanel(!showControlPanel)}
              className="bg-black/50 hover:bg-black/70 text-white p-2 rounded-lg backdrop-blur-sm transition-colors"
              title="Controles dos Modelos"
            >
              <Palette className="h-4 w-4" />
            </button>

            {/* Reset da câmera */}
            <button
              onClick={resetCamera}
              className="bg-black/50 hover:bg-black/70 text-white p-2 rounded-lg backdrop-blur-sm transition-colors"
              title="Reset Câmera"
            >
              <RotateCcw className="h-4 w-4" />
            </button>

            {/* Painel de controles */}
            {showControlPanel && (
              <div className="bg-black/80 backdrop-blur-sm rounded-lg p-3 space-y-3 min-w-[250px]">
                <h3 className="text-white text-sm font-medium mb-3">Controle dos Modelos</h3>
                
                {models.map((model, index) => (
                  <div key={model.id} className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div 
                        className="w-3 h-3 rounded-full border border-white/30"
                        style={{ 
                          backgroundColor: model.color || predefinedColors[index % predefinedColors.length] 
                        }}
                      />
                      <span className="text-white text-xs">{model.name}</span>
                    </div>
                    
                    <button
                      onClick={() => toggleModelVisibility(model.id)}
                      className={cn(
                        "p-1 rounded transition-colors",
                        modelVisibility[model.id] 
                          ? "text-green-400 hover:text-green-300" 
                          : "text-gray-400 hover:text-gray-300"
                      )}
                      title={modelVisibility[model.id] ? "Ocultar" : "Mostrar"}
                    >
                      {modelVisibility[model.id] ? 
                        <Eye className="h-3 w-3" /> : 
                        <EyeOff className="h-3 w-3" />
                      }
                    </button>
                  </div>
                ))}

                <div className="border-t border-white/20 pt-2 mt-3">
                  <div className="flex gap-2">
                    <button
                      onClick={() => {
                        const allVisible = Object.values(modelVisibility).every(v => v);
                        const newVisibility = models.reduce((acc, model) => ({
                          ...acc,
                          [model.id]: !allVisible
                        }), {});
                        setModelVisibility(newVisibility);
                      }}
                      className="flex-1 text-xs text-white/80 hover:text-white bg-white/10 hover:bg-white/20 px-2 py-1 rounded transition-colors"
                    >
                      {Object.values(modelVisibility).every(v => v) ? 'Ocultar Todos' : 'Mostrar Todos'}
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Título */}
      <div className="absolute bottom-4 left-4 z-10">
        <div className="bg-black/50 backdrop-blur-sm rounded-lg px-4 py-2">
          <h3 className="text-white font-medium text-sm">{title}</h3>
          <p className="text-white/70 text-xs">
            {models.length} modelo{models.length !== 1 ? 's' : ''} • 
            {Object.values(modelVisibility).filter(v => v).length} visível
            {Object.values(modelVisibility).filter(v => v).length !== 1 ? 'is' : ''}
          </p>
        </div>
      </div>

      {/* Instruções */}
      <div className="absolute bottom-4 right-4 z-10">
        <div className="bg-black/50 backdrop-blur-sm rounded-lg px-3 py-2">
          <p className="text-white/70 text-xs">
            Arraste para rotacionar • Scroll para zoom
          </p>
        </div>
      </div>
    </div>
  );
};

export default STLModelViewer;
