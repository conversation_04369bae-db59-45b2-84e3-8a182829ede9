import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Share2, 
  Facebook, 
  Twitter, 
  Linkedin, 
  Instagram, 
  Copy, 
  Mail,
  MessageCircle,
  Download,
  QrCode
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from '@/components/ui/sonner';

interface ShareButtonProps {
  item: {
    id: string;
    title: string;
    description: string;
    thumbnail: string;
    type: string;
  };
  variant?: 'button' | 'fab' | 'minimal';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  showLabel?: boolean;
}

interface ShareOption {
  name: string;
  icon: React.ComponentType<any>;
  color: string;
  action: (url: string, title: string, description: string) => void;
}

const ShareButton: React.FC<ShareButtonProps> = ({
  item,
  variant = 'button',
  size = 'md',
  className,
  showLabel = false
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [copied, setCopied] = useState(false);

  // Gerar URL do caso
  const generateShareUrl = () => {
    const baseUrl = window.location.origin;
    return `${baseUrl}/?case=${item.id}`;
  };

  // Gerar texto de compartilhamento
  const generateShareText = () => {
    return `Confira este trabalho odontológico: ${item.title} - ${item.description}`;
  };

  // Opções de compartilhamento
  const shareOptions: ShareOption[] = [
    {
      name: 'WhatsApp',
      icon: MessageCircle,
      color: 'bg-green-500',
      action: (url, title, description) => {
        const text = encodeURIComponent(`${title}\n\n${description}\n\n${url}`);
        window.open(`https://wa.me/?text=${text}`, '_blank');
      }
    },
    {
      name: 'Facebook',
      icon: Facebook,
      color: 'bg-blue-600',
      action: (url, title, description) => {
        const shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}&quote=${encodeURIComponent(`${title} - ${description}`)}`;
        window.open(shareUrl, '_blank', 'width=600,height=400');
      }
    },
    {
      name: 'Twitter',
      icon: Twitter,
      color: 'bg-blue-400',
      action: (url, title, description) => {
        const text = encodeURIComponent(`${title} - ${description}`);
        const shareUrl = `https://twitter.com/intent/tweet?text=${text}&url=${encodeURIComponent(url)}&hashtags=odontologia,exocad,3d`;
        window.open(shareUrl, '_blank', 'width=600,height=400');
      }
    },
    {
      name: 'LinkedIn',
      icon: Linkedin,
      color: 'bg-blue-700',
      action: (url, title, description) => {
        const shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`;
        window.open(shareUrl, '_blank', 'width=600,height=400');
      }
    },
    {
      name: 'Email',
      icon: Mail,
      color: 'bg-gray-600',
      action: (url, title, description) => {
        const subject = encodeURIComponent(`Confira: ${title}`);
        const body = encodeURIComponent(`${description}\n\nVeja mais detalhes em: ${url}`);
        window.open(`mailto:?subject=${subject}&body=${body}`);
      }
    },
    {
      name: 'Copiar Link',
      icon: Copy,
      color: 'bg-gray-500',
      action: async (url) => {
        try {
          await navigator.clipboard.writeText(url);
          setCopied(true);
          toast.success('Link copiado para a área de transferência!');
          setTimeout(() => setCopied(false), 2000);
        } catch (error) {
          // Fallback para navegadores mais antigos
          const textArea = document.createElement('textarea');
          textArea.value = url;
          document.body.appendChild(textArea);
          textArea.select();
          document.execCommand('copy');
          document.body.removeChild(textArea);
          toast.success('Link copiado!');
        }
      }
    }
  ];

  // Configurações de tamanho
  const sizes = {
    sm: {
      button: "w-8 h-8",
      icon: "w-4 h-4",
      text: "text-xs"
    },
    md: {
      button: "w-10 h-10",
      icon: "w-5 h-5",
      text: "text-sm"
    },
    lg: {
      button: "w-12 h-12",
      icon: "w-6 h-6",
      text: "text-base"
    }
  };

  const sizeConfig = sizes[size];

  const handleShare = (option: ShareOption) => {
    const url = generateShareUrl();
    const title = item.title;
    const description = generateShareText();
    
    option.action(url, title, description);
    setIsOpen(false);
  };

  // Usar Web Share API se disponível
  const handleNativeShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: item.title,
          text: generateShareText(),
          url: generateShareUrl()
        });
      } catch (error) {
        // Usuário cancelou ou erro
        console.log('Compartilhamento cancelado');
      }
    } else {
      setIsOpen(!isOpen);
    }
  };

  const renderButton = () => {
    switch (variant) {
      case 'fab':
        return (
          <motion.button
            className={cn(
              "fixed bottom-20 right-6 rounded-full bg-gradient-primary text-white shadow-lg z-40",
              "flex items-center justify-center hover-lift hover-glow",
              sizeConfig.button,
              className
            )}
            onClick={handleNativeShare}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <Share2 className={sizeConfig.icon} />
          </motion.button>
        );

      case 'minimal':
        return (
          <motion.button
            className={cn(
              "flex items-center gap-2 text-gray-600 hover:text-primary-600 transition-colors",
              sizeConfig.text,
              className
            )}
            onClick={handleNativeShare}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Share2 className={sizeConfig.icon} />
            {showLabel && <span>Compartilhar</span>}
          </motion.button>
        );

      default:
        return (
          <motion.button
            className={cn(
              "flex items-center justify-center rounded-lg bg-white/10 backdrop-blur-sm border border-white/20",
              "text-white hover:bg-white/20 transition-all duration-200",
              sizeConfig.button,
              className
            )}
            onClick={handleNativeShare}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Share2 className={sizeConfig.icon} />
          </motion.button>
        );
    }
  };

  return (
    <div className="relative">
      {renderButton()}

      {/* Menu de opções de compartilhamento */}
      <AnimatePresence>
        {isOpen && (
          <>
            {/* Overlay */}
            <motion.div
              className="fixed inset-0 bg-black/20 backdrop-blur-sm z-50"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={() => setIsOpen(false)}
            />

            {/* Menu */}
            <motion.div
              className="absolute bottom-full right-0 mb-2 bg-white rounded-xl shadow-xl border border-gray-200 overflow-hidden z-50"
              initial={{ opacity: 0, scale: 0.8, y: 10 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.8, y: 10 }}
              transition={{ duration: 0.2 }}
            >
              {/* Header */}
              <div className="px-4 py-3 border-b border-gray-100">
                <h3 className="font-medium text-gray-900">Compartilhar</h3>
                <p className="text-sm text-gray-500 truncate max-w-48">{item.title}</p>
              </div>

              {/* Opções */}
              <div className="p-2">
                {shareOptions.map((option, index) => (
                  <motion.button
                    key={option.name}
                    className="w-full flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-gray-50 transition-colors"
                    onClick={() => handleShare(option)}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.05 }}
                    whileHover={{ scale: 1.02 }}
                  >
                    <div className={cn(
                      "w-8 h-8 rounded-full flex items-center justify-center text-white",
                      option.color
                    )}>
                      <option.icon className="w-4 h-4" />
                    </div>
                    <span className="text-sm font-medium text-gray-700">
                      {option.name}
                    </span>
                    {option.name === 'Copiar Link' && copied && (
                      <motion.span
                        className="ml-auto text-xs text-green-600"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                      >
                        Copiado!
                      </motion.span>
                    )}
                  </motion.button>
                ))}
              </div>

              {/* Footer com preview */}
              <div className="px-4 py-3 bg-gray-50 border-t border-gray-100">
                <div className="flex items-center gap-3">
                  <img
                    src={item.thumbnail}
                    alt={item.title}
                    className="w-12 h-12 object-cover rounded"
                  />
                  <div className="flex-1 min-w-0">
                    <p className="text-xs font-medium text-gray-900 truncate">
                      {item.title}
                    </p>
                    <p className="text-xs text-gray-500">
                      {item.type}
                    </p>
                  </div>
                </div>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  );
};

export default ShareButton;
