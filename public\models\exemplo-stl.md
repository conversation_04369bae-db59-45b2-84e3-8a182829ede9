# Exemplo de Estrutura para Modelos STL do EXOCAD

## Estrutura de Pastas Recomendada:

```
public/models/
├── caso1/
│   ├── superior.stl
│   ├── inferior.stl
│   ├── coroa-11.stl
│   └── coroa-21.stl
├── caso2/
│   ├── arcada-superior.stl
│   ├── arcada-inferior.stl
│   └── ponte-anterior.stl
└── caso3/
    ├── modelo-superior.stl
    ├── modelo-inferior.stl
    ├── implante-16.stl
    └── implante-26.stl
```

## Como Configurar no Admin:

1. **Acesse o painel de administração** (`/gerenciador-casos`)
2. **Edite um caso** ou crie um novo
3. **Vá para a aba "3D Avançado"**
4. **Na seção "Modelos STL Múltiplos"**, adicione cada peça:
   - Nome: "Arcada Superior"
   - URL: `/models/caso1/superior.stl`
   - Cor: #e3f2fd (azul claro)
   
   - Nome: "Arcada Inferior"
   - URL: `/models/caso1/inferior.stl`
   - Cor: #f3e5f5 (rosa claro)
   
   - Nome: "Coroa 11"
   - URL: `/models/caso1/coroa-11.stl`
   - Cor: #e8f5e8 (verde claro)

## Formatos Suportados:

### ✅ **STL (Recomendado para EXOCAD)**
- **Vantagens**: Formato nativo do EXOCAD, suporte completo
- **Uso**: Ideal para modelos dentais, próteses, implantes
- **Performance**: Boa para modelos de tamanho médio

### ✅ **GLB/GLTF (Melhor Performance)**
- **Vantagens**: Menor tamanho, carregamento mais rápido
- **Uso**: Ideal para visualização web otimizada
- **Conversão**: Use Blender ou outras ferramentas

### ✅ **OBJ (Compatibilidade)**
- **Vantagens**: Formato universal
- **Uso**: Fallback para outros softwares
- **Limitações**: Maior tamanho de arquivo

## Conversão de STL para GLB (Recomendado):

### **Opção 1: Blender (Gratuito)**
1. Abra o Blender
2. Delete o cubo padrão
3. File > Import > STL
4. File > Export > glTF 2.0 (.glb)
5. Configure: Format = GLB, Include = Selected Objects

### **Opção 2: Online (Rápido)**
- Use: https://products.aspose.app/3d/conversion/stl-to-glb
- Upload do STL, download do GLB

### **Opção 3: Meshmixer (Autodesk)**
1. Import STL
2. Export > Binary STL ou OBJ
3. Use conversor online para GLB

## Configuração Ideal para EXOCAD:

```json
{
  "stlModels": [
    {
      "id": "superior",
      "name": "Arcada Superior",
      "url": "/models/caso1/superior.stl",
      "color": "#e3f2fd",
      "visible": true,
      "position": [0, 1, 0],
      "scale": [1, 1, 1]
    },
    {
      "id": "inferior", 
      "name": "Arcada Inferior",
      "url": "/models/caso1/inferior.stl",
      "color": "#f3e5f5",
      "visible": true,
      "position": [0, -1, 0],
      "scale": [1, 1, 1]
    }
  ]
}
```

## Dicas de Otimização:

1. **Reduza a resolução** dos STLs para web (0.1-0.2mm é suficiente)
2. **Use cores contrastantes** para diferentes peças
3. **Posicione os modelos** adequadamente no espaço 3D
4. **Teste o carregamento** antes de publicar
5. **Mantenha arquivos < 5MB** para melhor performance

## Workflow Recomendado:

1. **EXOCAD** → Export STL (resolução web)
2. **Blender** → Import STL → Export GLB (opcional)
3. **Upload** para `/public/models/`
4. **Configure** no painel admin
5. **Teste** a visualização
6. **Publique** o caso
