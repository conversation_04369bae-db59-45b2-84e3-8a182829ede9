import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Setting<PERSON>, 
  Heart, 
  Share2, 
  Presentation, 
  Palette,
  BarChart3,
  Download,
  Zap,
  X
} from 'lucide-react';
import { cn } from '@/lib/utils';
import ThemeToggle from './ThemeToggle';
import FavoriteButton from './FavoriteButton';
import ShareButton from './ShareButton';
import { FloatingActionButton } from './InteractiveButton';
import { useFavorites, useFavoritesStats } from '@/hooks/use-favorites';
import { useAnalytics } from '@/hooks/use-analytics';
import { usePresentationMode } from '@/hooks/use-presentation-mode';
import { useServiceWorker, useCacheStats } from '@/hooks/use-service-worker';
import { projectsData } from '@/data/projectsData';

interface FloatingControlsProps {
  currentCase?: any;
  className?: string;
}

const FloatingControls: React.FC<FloatingControlsProps> = ({
  currentCase,
  className
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [activePanel, setActivePanel] = useState<string | null>(null);

  // Hooks
  const { favoritesCount } = useFavorites();
  const favoritesStats = useFavoritesStats();
  const { sessionStats, exportAnalytics } = useAnalytics();
  const { startPresentation } = usePresentationMode();
  const { isOnline, cacheStatus } = useServiceWorker();
  const cacheStats = useCacheStats();

  // Controles principais
  const mainControls = [
    {
      id: 'favorites',
      icon: Heart,
      label: 'Favoritos',
      badge: favoritesCount > 0 ? favoritesCount : undefined,
      color: 'text-red-500',
      action: () => setActivePanel(activePanel === 'favorites' ? null : 'favorites')
    },
    {
      id: 'presentation',
      icon: Presentation,
      label: 'Apresentação',
      color: 'text-blue-500',
      action: () => {
        if (projectsData.length > 0) {
          startPresentation(projectsData);
          setIsOpen(false);
        }
      }
    },
    {
      id: 'theme',
      icon: Palette,
      label: 'Tema',
      color: 'text-purple-500',
      action: () => setActivePanel(activePanel === 'theme' ? null : 'theme')
    },
    {
      id: 'analytics',
      icon: BarChart3,
      label: 'Analytics',
      color: 'text-green-500',
      action: () => setActivePanel(activePanel === 'analytics' ? null : 'analytics')
    },
    {
      id: 'performance',
      icon: Zap,
      label: 'Performance',
      color: 'text-yellow-500',
      badge: isOnline ? undefined : '!',
      action: () => setActivePanel(activePanel === 'performance' ? null : 'performance')
    }
  ];

  // Renderizar painel de favoritos
  const renderFavoritesPanel = () => (
    <motion.div
      className="bg-card border border-border rounded-lg p-4 shadow-lg min-w-[300px]"
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
    >
      <h3 className="font-semibold mb-3 flex items-center gap-2">
        <Heart className="w-4 h-4 text-red-500" />
        Favoritos ({favoritesStats.total})
      </h3>
      
      <div className="space-y-2 text-sm text-muted-foreground">
        <div className="flex justify-between">
          <span>Total:</span>
          <span>{favoritesStats.total}</span>
        </div>
        <div className="flex justify-between">
          <span>Esta semana:</span>
          <span>{favoritesStats.addedThisWeek}</span>
        </div>
        <div className="flex justify-between">
          <span>Este mês:</span>
          <span>{favoritesStats.addedThisMonth}</span>
        </div>
      </div>

      {Object.entries(favoritesStats.byType).length > 0 && (
        <div className="mt-3 pt-3 border-t border-border">
          <p className="text-xs font-medium mb-2">Por tipo:</p>
          {Object.entries(favoritesStats.byType).map(([type, count]) => (
            <div key={type} className="flex justify-between text-xs">
              <span>{type}:</span>
              <span>{count}</span>
            </div>
          ))}
        </div>
      )}
    </motion.div>
  );

  // Renderizar painel de analytics
  const renderAnalyticsPanel = () => (
    <motion.div
      className="bg-card border border-border rounded-lg p-4 shadow-lg min-w-[300px]"
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
    >
      <h3 className="font-semibold mb-3 flex items-center gap-2">
        <BarChart3 className="w-4 h-4 text-green-500" />
        Analytics da Sessão
      </h3>
      
      <div className="space-y-2 text-sm text-muted-foreground">
        <div className="flex justify-between">
          <span>Duração:</span>
          <span>{Math.round(sessionStats.duration / 1000 / 60)}min</span>
        </div>
        <div className="flex justify-between">
          <span>Páginas vistas:</span>
          <span>{sessionStats.pageViews}</span>
        </div>
        <div className="flex justify-between">
          <span>Eventos:</span>
          <span>{sessionStats.events}</span>
        </div>
      </div>

      <button
        onClick={() => {
          const data = exportAnalytics();
          const blob = new Blob([data], { type: 'application/json' });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = 'analytics.json';
          a.click();
          URL.revokeObjectURL(url);
        }}
        className="mt-3 w-full flex items-center justify-center gap-2 px-3 py-2 bg-primary text-primary-foreground rounded-md text-sm hover:bg-primary/90 transition-colors"
      >
        <Download className="w-4 h-4" />
        Exportar Dados
      </button>
    </motion.div>
  );

  // Renderizar painel de performance
  const renderPerformancePanel = () => (
    <motion.div
      className="bg-card border border-border rounded-lg p-4 shadow-lg min-w-[300px]"
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
    >
      <h3 className="font-semibold mb-3 flex items-center gap-2">
        <Zap className="w-4 h-4 text-yellow-500" />
        Performance
      </h3>
      
      <div className="space-y-2 text-sm text-muted-foreground">
        <div className="flex justify-between">
          <span>Status:</span>
          <span className={isOnline ? "text-green-500" : "text-red-500"}>
            {isOnline ? "Online" : "Offline"}
          </span>
        </div>
        <div className="flex justify-between">
          <span>Cache total:</span>
          <span>{cacheStats.totalEntries} itens</span>
        </div>
        <div className="flex justify-between">
          <span>Utilização:</span>
          <span>{Math.round(cacheStats.utilizationPercentage)}%</span>
        </div>
      </div>

      {Object.entries(cacheStats.cachesByType).length > 0 && (
        <div className="mt-3 pt-3 border-t border-border">
          <p className="text-xs font-medium mb-2">Cache por tipo:</p>
          {Object.entries(cacheStats.cachesByType).map(([type, data]: [string, any]) => (
            <div key={type} className="flex justify-between text-xs">
              <span>{type}:</span>
              <span>{data.entries}/{data.maxEntries}</span>
            </div>
          ))}
        </div>
      )}
    </motion.div>
  );

  // Renderizar painel de tema
  const renderThemePanel = () => (
    <motion.div
      className="bg-card border border-border rounded-lg p-4 shadow-lg"
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
    >
      <h3 className="font-semibold mb-3 flex items-center gap-2">
        <Palette className="w-4 h-4 text-purple-500" />
        Tema
      </h3>
      
      <ThemeToggle variant="dropdown" size="lg" showLabel />
    </motion.div>
  );

  return (
    <div className={cn("fixed bottom-6 right-6 z-40", className)}>
      {/* Painéis */}
      <AnimatePresence>
        {activePanel && (
          <motion.div
            className="absolute bottom-16 right-0 mb-2"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
          >
            {activePanel === 'favorites' && renderFavoritesPanel()}
            {activePanel === 'analytics' && renderAnalyticsPanel()}
            {activePanel === 'performance' && renderPerformancePanel()}
            {activePanel === 'theme' && renderThemePanel()}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Controles expandidos */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            className="absolute bottom-16 right-0 flex flex-col gap-2 mb-2"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
          >
            {mainControls.map((control, index) => (
              <motion.button
                key={control.id}
                className="relative flex items-center justify-center w-12 h-12 bg-background border border-border rounded-full shadow-lg hover:shadow-xl transition-all group"
                onClick={control.action}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <control.icon className={cn("w-5 h-5", control.color)} />
                
                {/* Badge */}
                {control.badge && (
                  <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                    {control.badge}
                  </span>
                )}

                {/* Tooltip */}
                <span className="absolute right-full mr-2 px-2 py-1 bg-popover text-popover-foreground text-xs rounded whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity">
                  {control.label}
                </span>
              </motion.button>
            ))}

            {/* Compartilhar caso atual */}
            {currentCase && (
              <ShareButton
                item={{
                  id: currentCase.id,
                  title: currentCase.title,
                  description: currentCase.description,
                  thumbnail: currentCase.thumbnail,
                  type: currentCase.type
                }}
                variant="fab"
                size="md"
              />
            )}

            {/* Favoritar caso atual */}
            {currentCase && (
              <FavoriteButton
                item={{
                  id: currentCase.id,
                  title: currentCase.title,
                  thumbnail: currentCase.thumbnail,
                  type: currentCase.type
                }}
                size="md"
                className="w-12 h-12 bg-background border border-border rounded-full shadow-lg"
              />
            )}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Botão principal */}
      <motion.button
        className="w-14 h-14 bg-primary text-primary-foreground rounded-full shadow-lg flex items-center justify-center hover:shadow-xl transition-all"
        onClick={() => {
          setIsOpen(!isOpen);
          if (isOpen) setActivePanel(null);
        }}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        animate={{ rotate: isOpen ? 45 : 0 }}
      >
        {isOpen ? <X className="w-6 h-6" /> : <Settings className="w-6 h-6" />}
      </motion.button>
    </div>
  );
};

export default FloatingControls;
