import React from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { ExternalLink } from 'lucide-react';
import { cn } from '@/lib/utils';
import { usePulseAnimation } from '@/hooks/use-pulse-animation';

interface PulsingCTAButtonProps {
  onClick: () => void;
  className?: string;
  size?: 'sm' | 'default' | 'lg';
  children?: React.ReactNode;
  pulseInterval?: number; // Intervalo em milissegundos (padrão: 12000ms = 12s)
  initialDelay?: number; // Delay inicial antes do primeiro pulso
  enabled?: boolean; // Se a animação está habilitada
}

const PulsingCTAButton = ({
  onClick,
  className,
  size = 'sm',
  children,
  pulseInterval = 12000,
  initialDelay = 20000,
  enabled = true
}: PulsingCTAButtonProps) => {
  const { isPulsing, pulseKey, pauseAnimation } = usePulseAnimation({
    interval: pulseInterval,
    initialDelay,
    duration: 1200,
    enabled
  });

  const handleClick = () => {
    // Pausa a animação temporariamente quando o usuário clica
    pauseAnimation();
    onClick();
  };

  return (
    <motion.div
      key={pulseKey}
      initial={{ scale: 1 }}
      animate={isPulsing ? {
        scale: [1, 1.02, 1],
        transition: {
          duration: 1.2,
          ease: "easeInOut"
        }
      } : {}}
      className="relative"
    >
      <Button
        variant="outline"
        size={size}
        className={cn(
          "w-full justify-center uppercase text-white transition-all group relative overflow-hidden cta-button",
          "bg-white/10 hover:bg-white/20 border-white/10 hover:border-white/20",
          isPulsing && "cta-pulse-animation cta-glow active",
          className
        )}
        onClick={handleClick}
      >
        {/* Gradient overlay que aparece durante o pulse */}
        <motion.div
          className="absolute inset-0 opacity-0"
          style={{
            background: 'linear-gradient(135deg, rgba(168, 85, 247, 0.2) 0%, rgba(236, 72, 153, 0.2) 50%, rgba(59, 130, 246, 0.2) 100%)'
          }}
          animate={isPulsing ? {
            opacity: [0, 0.4, 0],
            scale: [1, 1.01, 1],
            transition: {
              duration: 0.1,
              ease: "easeInOut"
            }
          } : {}}
        />
        
        {/* Conteúdo do botão */}
        <div className="relative z-10 flex items-center">
          <ExternalLink className={cn(
            "group-hover:text-white transition-colors",
            size === 'sm' ? "mr-1 h-2.5 w-2.5" : "mr-2 h-4 w-4"
          )} />
          {children || 'VISUALIZAR CASO COMPLETO'}
        </div>

        {/* Efeito de brilho que se move durante o pulse */}
        <motion.div
          className="absolute inset-0 -skew-x-12 pointer-events-none"
          style={{
            background: 'linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.1) 20%, rgba(168, 85, 247, 0.2) 50%, rgba(255, 255, 255, 0.1) 80%, transparent 100%)'
          }}
          initial={{ x: '-120%' }}
          animate={isPulsing ? {
            x: '120%',
            transition: {
              duration: 0.9,
              ease: "easeInOut",
              delay: 0.1
            }
          } : { x: '-120%' }}
        />
      </Button>
    </motion.div>
  );
};

export default PulsingCTAButton;
