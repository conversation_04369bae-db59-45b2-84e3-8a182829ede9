import { useState, useEffect, useCallback } from 'react';
import { toast } from '@/components/ui/sonner';

interface PresentationSettings {
  autoAdvance: boolean;
  autoAdvanceInterval: number; // em segundos
  showControls: boolean;
  showInfo: boolean;
  showThumbnails: boolean;
  fullscreen: boolean;
  loop: boolean;
  startFromCurrent: boolean;
}

interface UsePresentationModeReturn {
  isPresenting: boolean;
  settings: PresentationSettings;
  currentSlide: number;
  totalSlides: number;
  isPlaying: boolean;
  timeRemaining: number;
  startPresentation: (slides: any[], startIndex?: number) => void;
  stopPresentation: () => void;
  nextSlide: () => void;
  previousSlide: () => void;
  goToSlide: (index: number) => void;
  togglePlay: () => void;
  updateSettings: (newSettings: Partial<PresentationSettings>) => void;
  toggleFullscreen: () => void;
}

const defaultSettings: PresentationSettings = {
  autoAdvance: false,
  autoAdvanceInterval: 10,
  showControls: true,
  showInfo: true,
  showThumbnails: false,
  fullscreen: true,
  loop: true,
  startFromCurrent: true
};

export const usePresentationMode = (): UsePresentationModeReturn => {
  const [isPresenting, setIsPresenting] = useState(false);
  const [settings, setSettings] = useState<PresentationSettings>(defaultSettings);
  const [slides, setSlides] = useState<any[]>([]);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [timeRemaining, setTimeRemaining] = useState(0);

  // Função nextSlide definida antes do useEffect
  const nextSlide = useCallback(() => {
    if (!isPresenting || slides.length === 0) return;

    setCurrentSlide(prev => {
      const next = prev + 1;
      if (next >= slides.length) {
        if (settings.loop) {
          setTimeRemaining(settings.autoAdvanceInterval);
          return 0;
        } else {
          setIsPlaying(false);
          toast.info('Fim da apresentação');
          return prev;
        }
      }
      setTimeRemaining(settings.autoAdvanceInterval);
      return next;
    });
  }, [isPresenting, slides.length, settings.loop, settings.autoAdvanceInterval]);

  // Timer para auto-advance
  useEffect(() => {
    if (!isPresenting || !isPlaying || !settings.autoAdvance) return;

    const interval = setInterval(() => {
      setTimeRemaining(prev => {
        if (prev <= 1) {
          nextSlide();
          return settings.autoAdvanceInterval;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [isPresenting, isPlaying, settings.autoAdvance, settings.autoAdvanceInterval, nextSlide]);

  // Controles de teclado
  useEffect(() => {
    if (!isPresenting) return;

    const handleKeyPress = (event: KeyboardEvent) => {
      switch (event.key) {
        case 'ArrowRight':
        case ' ':
          event.preventDefault();
          nextSlide();
          break;
        case 'ArrowLeft':
          event.preventDefault();
          previousSlide();
          break;
        case 'Escape':
          event.preventDefault();
          stopPresentation();
          break;
        case 'f':
        case 'F':
          event.preventDefault();
          toggleFullscreen();
          break;
        case 'p':
        case 'P':
          event.preventDefault();
          togglePlay();
          break;
        case 'Home':
          event.preventDefault();
          goToSlide(0);
          break;
        case 'End':
          event.preventDefault();
          goToSlide(slides.length - 1);
          break;
      }
    };

    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [isPresenting, slides.length]);

  // Detectar mudanças de fullscreen
  useEffect(() => {
    const handleFullscreenChange = () => {
      const isFullscreen = !!document.fullscreenElement;
      if (isPresenting && !isFullscreen && settings.fullscreen) {
        // Saiu do fullscreen durante apresentação
        setSettings(prev => ({ ...prev, fullscreen: false }));
      }
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
  }, [isPresenting, settings.fullscreen]);

  // Iniciar apresentação
  const startPresentation = useCallback((newSlides: any[], startIndex: number = 0) => {
    if (newSlides.length === 0) {
      toast.error('Nenhum slide disponível para apresentação');
      return;
    }

    setSlides(newSlides);
    setCurrentSlide(startIndex);
    setIsPresenting(true);
    setTimeRemaining(settings.autoAdvanceInterval);

    if (settings.autoAdvance) {
      setIsPlaying(true);
    }

    if (settings.fullscreen) {
      toggleFullscreen();
    }

    toast.success('Apresentação iniciada', {
      description: `${newSlides.length} slides • Use ← → para navegar • ESC para sair`
    });
  }, [settings]);

  // Parar apresentação
  const stopPresentation = useCallback(() => {
    setIsPresenting(false);
    setIsPlaying(false);
    setSlides([]);
    setCurrentSlide(0);
    setTimeRemaining(0);

    // Sair do fullscreen se estiver ativo
    if (document.fullscreenElement) {
      document.exitFullscreen().catch(console.warn);
    }

    toast.info('Apresentação finalizada');
  }, []);

  // Próximo slide (já definido acima)

  // Slide anterior
  const previousSlide = useCallback(() => {
    if (!isPresenting || slides.length === 0) return;

    setCurrentSlide(prev => {
      const previous = prev - 1;
      if (previous < 0) {
        if (settings.loop) {
          setTimeRemaining(settings.autoAdvanceInterval);
          return slides.length - 1;
        } else {
          return 0;
        }
      }
      setTimeRemaining(settings.autoAdvanceInterval);
      return previous;
    });
  }, [isPresenting, slides.length, settings.loop, settings.autoAdvanceInterval]);

  // Ir para slide específico
  const goToSlide = useCallback((index: number) => {
    if (!isPresenting || index < 0 || index >= slides.length) return;

    setCurrentSlide(index);
    setTimeRemaining(settings.autoAdvanceInterval);
  }, [isPresenting, slides.length, settings.autoAdvanceInterval]);

  // Toggle play/pause
  const togglePlay = useCallback(() => {
    if (!isPresenting || !settings.autoAdvance) return;

    setIsPlaying(prev => {
      const newState = !prev;
      if (newState) {
        setTimeRemaining(settings.autoAdvanceInterval);
        toast.info('Apresentação retomada');
      } else {
        toast.info('Apresentação pausada');
      }
      return newState;
    });
  }, [isPresenting, settings.autoAdvance, settings.autoAdvanceInterval]);

  // Atualizar configurações
  const updateSettings = useCallback((newSettings: Partial<PresentationSettings>) => {
    setSettings(prev => {
      const updated = { ...prev, ...newSettings };
      
      // Se desabilitou auto-advance, pausar
      if (!updated.autoAdvance && isPlaying) {
        setIsPlaying(false);
      }
      
      // Se mudou o intervalo, resetar timer
      if (newSettings.autoAdvanceInterval && newSettings.autoAdvanceInterval !== prev.autoAdvanceInterval) {
        setTimeRemaining(updated.autoAdvanceInterval);
      }
      
      return updated;
    });
  }, [isPlaying]);

  // Toggle fullscreen
  const toggleFullscreen = useCallback(async () => {
    try {
      if (document.fullscreenElement) {
        await document.exitFullscreen();
        setSettings(prev => ({ ...prev, fullscreen: false }));
      } else {
        await document.documentElement.requestFullscreen();
        setSettings(prev => ({ ...prev, fullscreen: true }));
      }
    } catch (error) {
      console.warn('Erro ao alternar fullscreen:', error);
      toast.error('Não foi possível alternar tela cheia');
    }
  }, []);

  return {
    isPresenting,
    settings,
    currentSlide,
    totalSlides: slides.length,
    isPlaying,
    timeRemaining,
    startPresentation,
    stopPresentation,
    nextSlide,
    previousSlide,
    goToSlide,
    togglePlay,
    updateSettings,
    toggleFullscreen
  };
};
