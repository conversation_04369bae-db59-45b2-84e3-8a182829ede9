import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Setting<PERSON>, 
  Heart, 
  Share2, 
  Presentation, 
  Palette,
  X,
  Sun,
  Moon,
  Monitor
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useTheme } from '@/hooks/use-theme';
import { projectsData } from '@/data/projectsData';

interface SimpleFloatingControlsProps {
  currentCase?: any;
  className?: string;
  show?: boolean; // Controlar quando mostrar os controles
}

const SimpleFloatingControls: React.FC<SimpleFloatingControlsProps> = ({
  currentCase,
  className,
  show = true
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [activePanel, setActivePanel] = useState<string | null>(null);
  const { theme, setTheme, actualTheme, toggleTheme } = useTheme();

  // Estado do fullscreen
  const [isFullscreen, setIsFullscreen] = useState(false);

  // Verificar estado do fullscreen
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
  }, []);

  // Toggle fullscreen
  const togglePresentation = () => {
    setIsOpen(false);

    if (isFullscreen) {
      // Sair do fullscreen
      if (document.exitFullscreen) {
        document.exitFullscreen();
      }
    } else {
      // Entrar em fullscreen
      if (document.documentElement.requestFullscreen) {
        document.documentElement.requestFullscreen().then(() => {
          // Adicionar listener para teclas de navegação
          const handleKeyPress = (e: KeyboardEvent) => {
            if (e.key === 'ArrowLeft') {
              // Navegar para caso anterior
              window.history.back();
            } else if (e.key === 'ArrowRight') {
              // Navegar para próximo caso
              window.history.forward();
            }
          };

          document.addEventListener('keydown', handleKeyPress);

          // Mostrar instruções
          const instructions = document.createElement('div');
          instructions.innerHTML = `
            <div style="position: fixed; top: 20px; right: 20px; background: rgba(0,0,0,0.8); color: white; padding: 15px; border-radius: 8px; z-index: 10000; font-family: system-ui;">
              <div style="font-weight: bold; margin-bottom: 8px;">Modo Apresentação</div>
              <div style="font-size: 14px;">
                ← → Navegar casos<br>
                ESC ou clique no botão para sair
              </div>
            </div>
          `;
          document.body.appendChild(instructions);

          // Remover instruções após 4 segundos
          setTimeout(() => {
            if (instructions.parentNode) {
              instructions.parentNode.removeChild(instructions);
            }
          }, 4000);

          // Cleanup quando sair do fullscreen
          const cleanup = () => {
            document.removeEventListener('keydown', handleKeyPress);
            document.removeEventListener('fullscreenchange', cleanup);
          };
          document.addEventListener('fullscreenchange', cleanup);
        }).catch(() => {
          alert('Não foi possível entrar em modo fullscreen. Use F11 manualmente.');
        });
      } else {
        alert('Modo fullscreen não suportado neste navegador. Use F11 manualmente.');
      }
    }
  };

  // Compartilhar caso atual
  const shareCurrentCase = async () => {
    setIsOpen(false);

    if (!currentCase) return;

    const shareData = {
      title: `Portfólio Odontológico - ${currentCase.title}`,
      text: `Confira este trabalho odontológico: ${currentCase.title}`,
      url: window.location.href
    };

    try {
      // Tentar usar Web Share API primeiro
      if (navigator.share && navigator.canShare && navigator.canShare(shareData)) {
        await navigator.share(shareData);
        return;
      }
    } catch (error) {
      console.log('Web Share API falhou, usando fallback');
    }

    // Fallback: copiar para clipboard
    try {
      await navigator.clipboard.writeText(window.location.href);

      // Mostrar notificação visual
      const notification = document.createElement('div');
      notification.innerHTML = `
        <div style="position: fixed; top: 20px; right: 20px; background: #10b981; color: white; padding: 12px 16px; border-radius: 8px; z-index: 10000; font-family: system-ui; box-shadow: 0 4px 12px rgba(0,0,0,0.3);">
          ✅ Link copiado para a área de transferência!
        </div>
      `;
      document.body.appendChild(notification);

      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 3000);
    } catch (error) {
      // Fallback final: prompt
      const userAgent = navigator.userAgent.toLowerCase();
      if (userAgent.includes('mobile') || userAgent.includes('android') || userAgent.includes('iphone')) {
        alert(`Compartilhe este link:\n${window.location.href}`);
      } else {
        prompt('Copie este link:', window.location.href);
      }
    }
  };

  // Controles principais
  const mainControls = [
    {
      id: 'share',
      icon: Share2,
      label: 'Compartilhar Caso',
      color: 'text-blue-500',
      action: shareCurrentCase
    },
    {
      id: 'presentation',
      icon: isFullscreen ? X : Presentation,
      label: isFullscreen ? 'Sair da Apresentação' : 'Modo Apresentação',
      color: isFullscreen ? 'text-red-500' : 'text-purple-500',
      action: togglePresentation
    }
  ];

  // Não mostrar se show for false ou se não há caso atual
  if (!show || !currentCase) {
    return null;
  }

  return (
    <div className={cn("fixed bottom-6 right-6", className)} style={{ zIndex: 9999 }}>
      {/* Controles expandidos */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            className="absolute bottom-16 right-0 flex flex-col gap-2 mb-2"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
          >
            {mainControls.map((control, index) => (
              <motion.button
                key={control.id}
                className="relative flex items-center justify-center w-12 h-12 bg-background border border-border rounded-full shadow-lg hover:shadow-xl transition-all group"
                onClick={control.action}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <control.icon className={cn("w-5 h-5", control.color)} />

                {/* Tooltip */}
                <span className="absolute right-full mr-2 px-2 py-1 bg-popover text-popover-foreground text-xs rounded whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity">
                  {control.label}
                </span>
              </motion.button>
            ))}


          </motion.div>
        )}
      </AnimatePresence>

      {/* Botão principal */}
      <motion.button
        className="w-12 h-12 bg-gray-800/90 backdrop-blur-sm text-white rounded-full shadow-lg flex items-center justify-center hover:shadow-xl transition-all border border-gray-600/50"
        onClick={() => {
          setIsOpen(!isOpen);
          if (isOpen) setActivePanel(null);
        }}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        animate={{ rotate: isOpen ? 45 : 0 }}
        style={{
          background: isOpen ? 'rgba(239, 68, 68, 0.9)' : 'rgba(31, 41, 55, 0.9)',
        }}
      >
        {isOpen ? <X className="w-5 h-5" /> : <Settings className="w-5 h-5" />}
      </motion.button>


    </div>
  );
};

export default SimpleFloatingControls;
