import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  <PERSON>ting<PERSON>, 
  Heart, 
  Share2, 
  Presentation, 
  Palette,
  X,
  Sun,
  Moon,
  Monitor
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useTheme } from '@/hooks/use-theme';
import { projectsData } from '@/data/projectsData';

interface SimpleFloatingControlsProps {
  currentCase?: any;
  className?: string;
}

const SimpleFloatingControls: React.FC<SimpleFloatingControlsProps> = ({
  currentCase,
  className
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [activePanel, setActivePanel] = useState<string | null>(null);
  const { theme, setTheme, actualTheme, toggleTheme } = useTheme();

  // Iniciar apresentação simples
  const startPresentation = () => {
    if (document.documentElement.requestFullscreen) {
      document.documentElement.requestFullscreen();
    }
    setIsOpen(false);
    alert('Modo apresentação ativado! Use as setas ← → para navegar e ESC para sair.');
  };

  // Compartilhar caso atual
  const shareCurrentCase = () => {
    if (currentCase && navigator.share) {
      navigator.share({
        title: currentCase.title,
        text: `Confira este trabalho odontológico: ${currentCase.title}`,
        url: window.location.href
      }).catch(() => {
        // Fallback: copiar URL
        navigator.clipboard.writeText(window.location.href);
        alert('Link copiado para a área de transferência!');
      });
    } else if (currentCase) {
      // Fallback: copiar URL
      navigator.clipboard.writeText(window.location.href);
      alert('Link copiado para a área de transferência!');
    }
    setIsOpen(false);
  };

  // Favoritar caso (simulado)
  const toggleFavorite = () => {
    if (currentCase) {
      const favorites = JSON.parse(localStorage.getItem('dental-favorites') || '[]');
      const isFavorite = favorites.some((fav: any) => fav.id === currentCase.id);
      
      if (isFavorite) {
        const newFavorites = favorites.filter((fav: any) => fav.id !== currentCase.id);
        localStorage.setItem('dental-favorites', JSON.stringify(newFavorites));
        alert('Removido dos favoritos!');
      } else {
        favorites.push({
          id: currentCase.id,
          title: currentCase.title,
          thumbnail: currentCase.thumbnail,
          addedAt: Date.now()
        });
        localStorage.setItem('dental-favorites', JSON.stringify(favorites));
        alert('Adicionado aos favoritos!');
      }
    }
    setIsOpen(false);
  };

  // Controles principais
  const mainControls = [
    {
      id: 'favorites',
      icon: Heart,
      label: 'Favoritos',
      color: 'text-red-500',
      action: toggleFavorite
    },
    {
      id: 'share',
      icon: Share2,
      label: 'Compartilhar',
      color: 'text-blue-500',
      action: shareCurrentCase
    },
    {
      id: 'presentation',
      icon: Presentation,
      label: 'Apresentação',
      color: 'text-purple-500',
      action: startPresentation
    },
    {
      id: 'theme',
      icon: actualTheme === 'dark' ? Moon : Sun,
      label: 'Tema',
      color: 'text-yellow-500',
      action: toggleTheme
    }
  ];

  return (
    <div className={cn("fixed bottom-6 right-6 z-40", className)}>
      {/* Controles expandidos */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            className="absolute bottom-16 right-0 flex flex-col gap-2 mb-2"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
          >
            {mainControls.map((control, index) => (
              <motion.button
                key={control.id}
                className="relative flex items-center justify-center w-12 h-12 bg-background border border-border rounded-full shadow-lg hover:shadow-xl transition-all group"
                onClick={control.action}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <control.icon className={cn("w-5 h-5", control.color)} />

                {/* Tooltip */}
                <span className="absolute right-full mr-2 px-2 py-1 bg-popover text-popover-foreground text-xs rounded whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity">
                  {control.label}
                </span>
              </motion.button>
            ))}

            {/* Informações do caso atual */}
            {currentCase && (
              <motion.div
                className="bg-card border border-border rounded-lg p-3 shadow-lg min-w-[200px]"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
              >
                <h3 className="font-medium text-sm mb-1">{currentCase.title}</h3>
                <p className="text-xs text-muted-foreground">{currentCase.type}</p>
                <div className="mt-2 pt-2 border-t border-border">
                  <p className="text-xs text-muted-foreground">
                    Tema: {theme === 'system' ? 'Sistema' : theme === 'dark' ? 'Escuro' : 'Claro'}
                  </p>
                </div>
              </motion.div>
            )}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Botão principal */}
      <motion.button
        className="w-14 h-14 bg-primary text-primary-foreground rounded-full shadow-lg flex items-center justify-center hover:shadow-xl transition-all"
        onClick={() => {
          setIsOpen(!isOpen);
          if (isOpen) setActivePanel(null);
        }}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        animate={{ rotate: isOpen ? 45 : 0 }}
      >
        {isOpen ? <X className="w-6 h-6" /> : <Settings className="w-6 h-6" />}
      </motion.button>

      {/* Instruções de uso */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            className="absolute bottom-full right-0 mb-4 bg-card border border-border rounded-lg p-3 shadow-lg max-w-[250px]"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 10 }}
          >
            <h4 className="font-medium text-sm mb-2">Controles Disponíveis:</h4>
            <ul className="text-xs text-muted-foreground space-y-1">
              <li>• ❤️ Favoritar caso atual</li>
              <li>• 📤 Compartilhar caso</li>
              <li>• 🎭 Modo apresentação</li>
              <li>• 🌙 Alternar tema</li>
            </ul>
            <div className="mt-2 pt-2 border-t border-border">
              <p className="text-xs text-muted-foreground">
                <strong>Dica:</strong> Use as setas ← → para navegar entre casos
              </p>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default SimpleFloatingControls;
