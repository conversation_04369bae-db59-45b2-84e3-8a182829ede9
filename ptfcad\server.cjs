const express = require('express');
const cors = require('cors');
const fs = require('fs');
const path = require('path');
const multer = require('multer');
const { exec } = require('child_process');

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Configuração do multer para upload de arquivos
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    // Determinar a pasta de destino com base no tipo de arquivo
    let uploadPath = path.join(__dirname, 'public/uploads');
    
    // Se for uma imagem para um caso específico
    if (req.body.caseId) {
      uploadPath = path.join(__dirname, `public/arquivosCasos/caso${req.body.caseId}`);
    } else if (file.mimetype.startsWith('image/')) {
      uploadPath = path.join(__dirname, 'public/images');
    } else if (file.mimetype === 'text/html') {
      uploadPath = path.join(__dirname, 'public/html');
    }
    
    // Criar a pasta se não existir
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }
    
    cb(null, uploadPath);
  },
  filename: function (req, file, cb) {
    // Gerar um nome de arquivo único
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, file.fieldname + '-' + uniqueSuffix + ext);
  }
});

const upload = multer({ storage: storage });

// Rota para salvar os dados no arquivo projectsData.ts
app.post('/api/save-projects-data', (req, res) => {
  try {
    const { projects } = req.body;
    
    if (!projects) {
      return res.status(400).json({ error: 'Dados de projetos não fornecidos' });
    }
    
    // Formatar os dados para exportação
    const dataString = `
// Arquivo gerado automaticamente pelo Gerenciador de Casos
// Última atualização: ${new Date().toLocaleString()}

import { Project } from "@/components/ProjectGallery";

export const projectsData: Project[] = ${JSON.stringify(projects, null, 2)};
`;
    
    // Caminho para o arquivo projectsData.ts
    const filePath = path.join(__dirname, 'src/data/projectsData.ts');
    
    // Salvar os dados no arquivo
    fs.writeFileSync(filePath, dataString, 'utf8');
    
    // Responder com sucesso
    res.json({ success: true, message: 'Dados salvos com sucesso!' });
  } catch (error) {
    console.error('Erro ao salvar os dados:', error);
    res.status(500).json({ error: 'Erro ao salvar os dados', details: error.message });
  }
});

// Rota para upload de arquivos
app.post('/api/upload', upload.single('file'), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'Nenhum arquivo enviado' });
    }
    
    // Caminho relativo para o arquivo (para uso no frontend)
    const relativePath = req.file.path.replace(path.join(__dirname, 'public'), '');
    
    // Responder com o caminho do arquivo
    res.json({ 
      success: true, 
      filePath: relativePath.replace(/\\/g, '/') // Converter barras invertidas para barras normais
    });
  } catch (error) {
    console.error('Erro ao fazer upload do arquivo:', error);
    res.status(500).json({ error: 'Erro ao fazer upload do arquivo', details: error.message });
  }
});

// Rota para listar arquivos em uma pasta
app.get('/api/list-files', (req, res) => {
  try {
    const { folder } = req.query;
    
    if (!folder) {
      return res.status(400).json({ error: 'Pasta não especificada' });
    }
    
    // Caminho completo para a pasta
    const folderPath = path.join(__dirname, 'public', folder);
    
    // Verificar se a pasta existe
    if (!fs.existsSync(folderPath)) {
      return res.json({ files: [] });
    }
    
    // Ler os arquivos na pasta
    const files = fs.readdirSync(folderPath)
      .filter(file => {
        // Filtrar apenas arquivos (não pastas)
        const filePath = path.join(folderPath, file);
        return fs.statSync(filePath).isFile();
      })
      .map(file => {
        // Converter para caminho relativo
        return `/${folder}/${file}`.replace(/\\/g, '/');
      });
    
    // Responder com a lista de arquivos
    res.json({ files });
  } catch (error) {
    console.error('Erro ao listar arquivos:', error);
    res.status(500).json({ error: 'Erro ao listar arquivos', details: error.message });
  }
});

// Rota para listar pastas
app.get('/api/list-folders', (req, res) => {
  try {
    // Pastas predefinidas
    const baseFolders = [
      { name: 'Casos', path: '/arquivosCasos' },
      { name: 'Imagens Gerais', path: '/images' },
      { name: 'HTML', path: '/html' },
    ];
    
    // Subpastas de casos
    const caseFolders = [];
    const casesPath = path.join(__dirname, 'public/arquivosCasos');
    
    if (fs.existsSync(casesPath)) {
      fs.readdirSync(casesPath)
        .filter(folder => {
          const folderPath = path.join(casesPath, folder);
          return fs.statSync(folderPath).isDirectory();
        })
        .forEach(folder => {
          caseFolders.push({
            name: `Caso ${folder.replace('caso', '')}`,
            path: `/arquivosCasos/${folder}`
          });
        });
    }
    
    // Adicionar subpastas aos folders principais
    const folders = baseFolders.map(folder => {
      if (folder.path === '/arquivosCasos') {
        return {
          ...folder,
          subfolders: caseFolders
        };
      }
      return {
        ...folder,
        subfolders: []
      };
    });
    
    // Responder com a lista de pastas
    res.json({ folders });
  } catch (error) {
    console.error('Erro ao listar pastas:', error);
    res.status(500).json({ error: 'Erro ao listar pastas', details: error.message });
  }
});

// Servir arquivos estáticos da pasta public
app.use(express.static(path.join(__dirname, 'public')));

// Servir a aplicação React em produção
if (process.env.NODE_ENV === 'production') {
  app.use(express.static(path.join(__dirname, 'dist')));
  
  app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, 'dist', 'index.html'));
  });
}

// Iniciar o servidor
app.listen(PORT, () => {
  console.log(`Servidor rodando na porta ${PORT}`);
  
  // Abrir o navegador automaticamente (apenas em desenvolvimento)
  if (process.env.NODE_ENV !== 'production') {
    const url = `http://localhost:${PORT}`;
    const command = process.platform === 'win32' ? 'start' : (process.platform === 'darwin' ? 'open' : 'xdg-open');
    exec(`${command} ${url}`);
  }
});
