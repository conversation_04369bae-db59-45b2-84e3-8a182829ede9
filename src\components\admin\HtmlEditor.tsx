import React, { useState } from 'react';
import { <PERSON>, Eye, Copy, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from '@/components/ui/sonner';

interface HtmlEditorProps {
  value: string;
  onChange: (value: string) => void;
  height?: string;
}

const HtmlEditor: React.FC<HtmlEditorProps> = ({
  value,
  onChange,
  height = '300px'
}) => {
  const [activeTab, setActiveTab] = useState<string>('code');
  const [copied, setCopied] = useState(false);

  const handleCopy = () => {
    navigator.clipboard.writeText(value);
    setCopied(true);
    toast.success('Código copiado para a área de transferência!');
    
    setTimeout(() => {
      setCopied(false);
    }, 2000);
  };

  return (
    <div className="border border-gray-700 rounded-lg overflow-hidden">
      <div className="flex items-center justify-between bg-gray-800 px-3 py-2">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <div className="flex items-center justify-between">
            <TabsList className="bg-gray-900">
              <TabsTrigger value="code" className="data-[state=active]:bg-gray-700">
                <Code className="h-4 w-4 mr-2" />
                Código
              </TabsTrigger>
              <TabsTrigger value="preview" className="data-[state=active]:bg-gray-700">
                <Eye className="h-4 w-4 mr-2" />
                Pré-visualização
              </TabsTrigger>
            </TabsList>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={handleCopy}
              className="text-gray-400 hover:text-white"
            >
              {copied ? (
                <>
                  <Check className="h-4 w-4 mr-1" />
                  Copiado
                </>
              ) : (
                <>
                  <Copy className="h-4 w-4 mr-1" />
                  Copiar
                </>
              )}
            </Button>
          </div>
        </Tabs>
      </div>
      
      <div style={{ height }}>
        <TabsContent value="code" className="m-0 h-full">
          <textarea
            value={value}
            onChange={(e) => onChange(e.target.value)}
            className="w-full h-full p-4 bg-gray-900 text-white font-mono text-sm resize-none focus:outline-none"
            spellCheck={false}
          />
        </TabsContent>
        
        <TabsContent value="preview" className="m-0 h-full">
          <div className="w-full h-full bg-white overflow-auto p-4">
            <div dangerouslySetInnerHTML={{ __html: value }} />
          </div>
        </TabsContent>
      </div>
    </div>
  );
};

export default HtmlEditor;
