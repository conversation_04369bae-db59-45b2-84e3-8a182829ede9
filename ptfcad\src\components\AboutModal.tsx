
import React, { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogDescription, DialogClose } from "@/components/ui/dialog";
import { X, Phone } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useIsMobile } from '@/hooks/use-mobile';

interface AboutModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const AboutModal = ({ isOpen, onClose }: AboutModalProps) => {
  const isMobile = useIsMobile();

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[1800px] w-[95vw] sm:w-[95vw] max-h-[90vh] overflow-y-auto p-0 gap-0 bg-gradient-to-br from-black/95 via-[#202020]/95 to-black/95 border-none">
        <DialogClose asChild>
          <Button
            variant="ghost"
            size="icon"
            onClick={(e) => {
              e.preventDefault();
              onClose();
            }}
            className="absolute right-3 top-3 z-[100] bg-black/60 hover:bg-black/80 text-white"
          >
            <X className="h-4 w-4" />
          </Button>
        </DialogClose>
        <div className="p-3 sm:p-8 w-full">
          <DialogHeader className="mb-3 sm:mb-6 relative">
            <h2 className="text-lg sm:text-2xl font-bold text-white tracking-tight">SOBRE MIM</h2>
            <DialogDescription className="text-white/60 text-xs sm:text-base">
              Conheça mais sobre minha experiência, visão e abordagem em modelagem 3D para odontologia digital.
            </DialogDescription>
          </DialogHeader>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-12">
            {/* Left column with profile photo */}
            <div className="bg-black/40 backdrop-blur-sm rounded-lg overflow-hidden h-full flex items-center justify-center">
              <div className="w-full h-full rounded-lg bg-gradient-to-br from-black/50 via-[#202020]/30 to-black/60 flex flex-col items-center justify-center text-center p-3 sm:p-8">
                <img
                  src="/images/WhatsApp Image 2025-05-15 at 03.09.49.jpeg"
                  alt="Jonhnatas Lima"
                  className="w-32 h-32 sm:w-56 sm:h-56 rounded-full object-cover mb-3 sm:mb-6 border-2 border-white/30 shadow-lg"
                />
                <h3 className="text-lg sm:text-xl font-medium text-white mb-1 sm:mb-2">JONHNATAS LIMA</h3>
                <p className="text-xs sm:text-sm text-white/60 mb-3 sm:mb-6">Especialista em Modelagem 3D para Odontologia</p>

                <div className="space-y-1 sm:space-y-2">
                  <p className="text-xs sm:text-sm text-white/80">✉️ <EMAIL></p>
                  <p className="text-xs sm:text-sm text-white/80">📱 +55 (31) 9 9069-7788</p>
                </div>

                <Button
                  variant="outline"
                  className="mt-3 sm:mt-6 bg-black/50 hover:bg-[#202020]/70 text-white border-gray-800/50 text-xs sm:text-sm py-1 h-auto"
                  onClick={() => window.open("https://wa.me/5531990697788", "_blank")}
                >
                  <Phone className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" /> Entre em Contato via WhatsApp
                </Button>
              </div>
            </div>

            {/* Right column with text content */}
            <div className="bg-black/40 backdrop-blur-sm rounded-lg p-3 sm:p-6">
              <div className="space-y-3 sm:space-y-6 text-justify">
                <div>
                  <h3 className="text-base sm:text-lg font-medium text-white mb-2 sm:mb-3">IDENTIDADE</h3>
                  <p className="text-xs sm:text-sm text-white/80 leading-relaxed">
                    Me chamo Jonhnatas Lima, tenho 30 anos, e acho fascinante quando a odontologia digital resolve casos, entrega função, respeita estética e promove conforto para o paciente final.
                  </p>
                  <p className="text-xs sm:text-sm text-white/80 leading-relaxed mt-1 sm:mt-2">
                    Meu portfólio foi criado para mostrar o que realmente faço:
                    visualização 3D direta, visualizadores HTML interativos com todas as malhas do projeto, imagens clínicas e informações técnicas.
                    Aqui, você encontra clareza, transparência e compromisso com a qualidade.
                  </p>
                </div>

                <div>
                  <h3 className="text-base sm:text-lg font-medium text-white mb-2 sm:mb-3">FORMAÇÃO E EXPERIÊNCIA</h3>
                  <p className="text-xs sm:text-sm text-white/80 leading-relaxed">
                    Atuo com modelagem 3D aplicada à odontologia há mais de 3 anos, utilizando o software EXOCAD como principal ferramenta.
                    Nesse período, desenvolvi um forte domínio da plataforma e de seus recursos, atuando diretamente em laboratório com casos clínicos diversos, desde unitários simples até reabilitações parciais com múltiplas exigências funcionais e estéticas.
                  </p>
                  <p className="text-xs sm:text-sm text-white/80 leading-relaxed mt-1 sm:mt-2">
                    Meu foco é desenvolver soluções digitais com precisão, funcionalidade real e acabamento estético compatível com a exigência clínica. Todo o meu trabalho é pensado para ir além da forma — priorizando função, oclusão e previsibilidade de resultados.
                  </p>
                </div>

                <div>
                  <h3 className="text-base sm:text-lg font-medium text-white mb-2 sm:mb-3">ÁREAS DE ATUAÇÃO</h3>
                  <p className="text-xs sm:text-sm text-white/80 leading-relaxed">
                    Tenho experiência prática com os seguintes tipos de projeto:
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-x-3 gap-y-0.5 sm:gap-y-1 mt-1 sm:mt-2">
                    <div className="flex items-start">
                      <span className="text-white/60 mr-1 sm:mr-2">•</span>
                      <p className="text-xs sm:text-sm text-white/80">Coroas sobre preparo e implante</p>
                    </div>
                    <div className="flex items-start">
                      <span className="text-white/60 mr-1 sm:mr-2">•</span>
                      <p className="text-xs sm:text-sm text-white/80">Planejamentos diagnósticos com smile design</p>
                    </div>
                    <div className="flex items-start">
                      <span className="text-white/60 mr-1 sm:mr-2">•</span>
                      <p className="text-xs sm:text-sm text-white/80">Coopings e abutments</p>
                    </div>
                    <div className="flex items-start">
                      <span className="text-white/60 mr-1 sm:mr-2">•</span>
                      <p className="text-xs sm:text-sm text-white/80">Placas de bruxismo</p>
                    </div>
                    <div className="flex items-start">
                      <span className="text-white/60 mr-1 sm:mr-2">•</span>
                      <p className="text-xs sm:text-sm text-white/80">Lentes de contato dental, onlays e restaurados</p>
                    </div>
                    <div className="flex items-start">
                      <span className="text-white/60 mr-1 sm:mr-2">•</span>
                      <p className="text-xs sm:text-sm text-white/80">Pontes fixas sobre preparo ou implante</p>
                    </div>
                    <div className="flex items-start">
                      <span className="text-white/60 mr-1 sm:mr-2">•</span>
                      <p className="text-xs sm:text-sm text-white/80">Provisórios convencionais e casca de ovo</p>
                    </div>
                    <div className="flex items-start">
                      <span className="text-white/60 mr-1 sm:mr-2">•</span>
                      <p className="text-xs sm:text-sm text-white/80">Protocolos</p>
                    </div>
                  </div>
                  <p className="text-xs sm:text-sm text-white/80 leading-relaxed mt-2 sm:mt-3">
                    Cada caso é planejado de forma individual, buscando respeitar as necessidades clínicas específicas de cada paciente — com equilíbrio entre função e estética.
                  </p>
                </div>

                <div>
                  <h3 className="text-base sm:text-lg font-medium text-white mb-2 sm:mb-3">TECNOLOGIAS DOMINADAS</h3>
                  <div className="flex flex-wrap gap-1 sm:gap-2 mb-2 sm:mb-4">
                    <span className="px-2 sm:px-3 py-1 sm:py-1.5 bg-black/60 text-[10px] sm:text-xs text-white rounded-full border border-gray-800/40">EXOCAD</span>
                    <span className="px-2 sm:px-3 py-1 sm:py-1.5 bg-black/60 text-[10px] sm:text-xs text-white rounded-full border border-gray-800/40">CAD/CAM</span>
                    <span className="px-2 sm:px-3 py-1 sm:py-1.5 bg-black/60 text-[10px] sm:text-xs text-white rounded-full border border-gray-800/40">INLAB</span>
                    <span className="px-2 sm:px-3 py-1 sm:py-1.5 bg-black/60 text-[10px] sm:text-xs text-white rounded-full border border-gray-800/40">IMPRESSÃO 3D</span>
                    <span className="px-2 sm:px-3 py-1 sm:py-1.5 bg-black/60 text-[10px] sm:text-xs text-white rounded-full border border-gray-800/40">DISSILICATO</span>
                    <span className="px-2 sm:px-3 py-1 sm:py-1.5 bg-black/60 text-[10px] sm:text-xs text-white rounded-full border border-gray-800/40">SMILE DESIGN</span>
                    <span className="px-2 sm:px-3 py-1 sm:py-1.5 bg-black/60 text-[10px] sm:text-xs text-white rounded-full border border-gray-800/40">PLANEJAMENTO FUNCIONAL</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AboutModal;
