
import React, { useState } from 'react';
import { Filter, ChevronDown, ChevronUp } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useIsMobile } from '@/hooks/use-mobile';

interface FilterBarProps {
  categories: string[];
  selectedCategory: string;
  onSelectCategory: (category: string) => void;
}

const FilterBar = ({ categories, selectedCategory, onSelectCategory }: FilterBarProps) => {
  const isMobile = useIsMobile();
  const [isExpanded, setIsExpanded] = useState(false);

  // Organize categories: "TODOS" first, then alphabetically
  const organizedCategories = React.useMemo(() => {
    const todos = categories.filter(cat => cat === "TODOS");
    const rest = categories.filter(cat => cat !== "TODOS").sort();
    return [...todos, ...rest];
  }, [categories]);

  // Reorganize categories for mobile - optimize first row
  const splitCategories = React.useMemo(() => {
    // Always keep "TODOS" in the first row
    // For the first row, select categories with shorter names to fit better
    const shortCategories = ["TODOS", "COROAS", "PONTES", "LENTES", "MODELOS"];
    const firstRow = organizedCategories.filter(cat => shortCategories.includes(cat));
    const secondRow = organizedCategories.filter(cat => !shortCategories.includes(cat));
    return { firstRow, secondRow };
  }, [organizedCategories]);

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  // Desktop version
  if (!isMobile) {
    return (
      <div className="fixed top-24 left-6 z-10 py-3 pointer-events-auto">
        <div className="flex items-center space-x-3 bg-black/30 backdrop-blur-md rounded-lg px-3 py-2">
          <Filter className="h-4 w-4 text-white/60" />
          <div className="flex items-center">
            {organizedCategories.map((category, idx) => (
              <React.Fragment key={category}>
                {idx > 0 && <span className="text-white/30 mx-1">|</span>}
                <button
                  onClick={() => onSelectCategory(category)}
                  className={cn(
                    "px-1 py-1 text-xs uppercase transition-colors",
                    selectedCategory === category
                      ? 'text-white font-medium'
                      : 'text-white/70 hover:text-white'
                  )}
                  data-category={category}
                >
                  {category}
                </button>
              </React.Fragment>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Mobile version
  return (
    <div className="fixed top-20 left-2 sm:left-4 z-10 py-3 pointer-events-auto">
      <div className={cn(
        "bg-black/30 backdrop-blur-md rounded-lg transition-all duration-300",
        isExpanded ? "opacity-100" : "opacity-80 hover:opacity-100",
        !isExpanded ? "min-w-[200px]" : "" // Garantir largura mínima quando recolhido para acomodar categorias longas
      )}>
        <div className="px-2 py-2">
          <div className="flex flex-col">
            {/* Primeira linha com ícone, seta, categoria selecionada e expansão horizontal */}
            <div className="relative h-6">
              {/* Área fixa com ícone, seta e categoria selecionada */}
              <div className="absolute left-0 top-0 flex items-center z-10">
                <button
                  onClick={toggleExpanded}
                  className="flex items-center"
                  aria-label={isExpanded ? "Recolher filtros" : "Expandir filtros"}
                >
                  <Filter className="h-[17px] w-[17px] text-white/70" />
                  {isExpanded ? (
                    <ChevronUp className="h-3 w-3 text-white/60 ml-1" />
                  ) : (
                    <ChevronDown className="h-3 w-3 text-white/60 ml-1" />
                  )}
                </button>

                {/* Categoria selecionada visível apenas quando recolhido */}
                {!isExpanded && (
                  <span className="text-[10px] uppercase text-white font-medium ml-2 whitespace-nowrap max-w-[150px] overflow-hidden text-ellipsis">
                    {selectedCategory}
                  </span>
                )}
              </div>

              {/* Expansão horizontal da primeira linha */}
              {isExpanded && (
                <div className="absolute left-[40px] top-0 overflow-x-auto hide-scrollbar transition-all duration-300">
                  <div className="flex items-center">
                    {splitCategories.firstRow.map((category, idx) => (
                      <React.Fragment key={category}>
                        {idx > 0 && <span className="text-white/30 mx-0.5">|</span>}
                        <button
                          onClick={() => {
                            onSelectCategory(category);
                            setIsExpanded(false); // Recolher o filtro ao selecionar uma categoria
                          }}
                          className={cn(
                            "px-1 py-0.5 text-[10px] uppercase transition-colors whitespace-nowrap",
                            selectedCategory === category
                              ? 'text-white font-medium'
                              : 'text-white/70 hover:text-white'
                          )}
                          data-category={category}
                        >
                          {category}
                        </button>
                      </React.Fragment>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Segunda linha (apenas se houver categorias para a segunda linha) */}
            {isExpanded && splitCategories.secondRow.length > 0 && (
              <div className="flex items-center mt-2 ml-6">
                <div className="flex items-center overflow-x-auto hide-scrollbar">
                  {splitCategories.secondRow.map((category, idx) => (
                    <React.Fragment key={category}>
                      {idx > 0 && <span className="text-white/30 mx-0.5">|</span>}
                      <button
                        onClick={() => {
                          onSelectCategory(category);
                          setIsExpanded(false); // Recolher o filtro ao selecionar uma categoria
                        }}
                        className={cn(
                          "px-1 py-0.5 text-[10px] uppercase transition-colors whitespace-nowrap",
                          selectedCategory === category
                            ? 'text-white font-medium'
                            : 'text-white/70 hover:text-white'
                        )}
                        data-category={category}
                      >
                        {category}
                      </button>
                    </React.Fragment>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default FilterBar;
