import { useState, useEffect, useCallback } from 'react';

interface AnalyticsEvent {
  id: string;
  type: string;
  action: string;
  data: Record<string, any>;
  timestamp: number;
  sessionId: string;
  userId?: string;
}

interface UserSession {
  id: string;
  startTime: number;
  endTime?: number;
  pageViews: number;
  events: number;
  userAgent: string;
  referrer: string;
  viewport: {
    width: number;
    height: number;
  };
}

interface AnalyticsData {
  events: AnalyticsEvent[];
  sessions: UserSession[];
  currentSession: UserSession | null;
}

interface UseAnalyticsReturn {
  trackEvent: (type: string, action: string, data?: Record<string, any>) => void;
  trackPageView: (page: string, title?: string) => void;
  trackModelView: (modelId: string, modelTitle: string, duration?: number) => void;
  trackInteraction: (element: string, action: string, data?: Record<string, any>) => void;
  trackError: (error: string, context?: Record<string, any>) => void;
  getAnalytics: () => AnalyticsData;
  exportAnalytics: () => string;
  clearAnalytics: () => void;
  sessionStats: {
    duration: number;
    pageViews: number;
    events: number;
  };
}

const STORAGE_KEY = 'dental-portfolio-analytics';
const SESSION_TIMEOUT = 30 * 60 * 1000; // 30 minutos

// Gerar ID único
const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

// Obter ou criar sessão
const getOrCreateSession = (): UserSession => {
  const stored = localStorage.getItem(STORAGE_KEY);
  let data: AnalyticsData = {
    events: [],
    sessions: [],
    currentSession: null
  };

  if (stored) {
    try {
      data = JSON.parse(stored);
    } catch (error) {
      console.warn('Erro ao carregar analytics:', error);
    }
  }

  // Verificar se há sessão ativa
  const now = Date.now();
  if (data.currentSession && (now - data.currentSession.startTime) < SESSION_TIMEOUT) {
    return data.currentSession;
  }

  // Criar nova sessão
  const newSession: UserSession = {
    id: generateId(),
    startTime: now,
    pageViews: 0,
    events: 0,
    userAgent: navigator.userAgent,
    referrer: document.referrer,
    viewport: {
      width: window.innerWidth,
      height: window.innerHeight
    }
  };

  return newSession;
};

export const useAnalytics = (): UseAnalyticsReturn => {
  const [currentSession, setCurrentSession] = useState<UserSession>(() => getOrCreateSession());
  const [analytics, setAnalytics] = useState<AnalyticsData>(() => {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (stored) {
      try {
        return JSON.parse(stored);
      } catch (error) {
        console.warn('Erro ao carregar analytics:', error);
      }
    }
    return {
      events: [],
      sessions: [],
      currentSession: null
    };
  });

  // Salvar dados no localStorage
  const saveAnalytics = useCallback((data: AnalyticsData) => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
    } catch (error) {
      console.warn('Erro ao salvar analytics:', error);
    }
  }, []);

  // Atualizar sessão atual
  useEffect(() => {
    const updatedAnalytics = {
      ...analytics,
      currentSession
    };
    setAnalytics(updatedAnalytics);
    saveAnalytics(updatedAnalytics);
  }, [currentSession, saveAnalytics]);

  // Finalizar sessão ao sair
  useEffect(() => {
    const handleBeforeUnload = () => {
      if (currentSession) {
        const updatedSession = {
          ...currentSession,
          endTime: Date.now()
        };
        
        const updatedAnalytics = {
          ...analytics,
          sessions: [...analytics.sessions.filter(s => s.id !== currentSession.id), updatedSession],
          currentSession: null
        };
        
        saveAnalytics(updatedAnalytics);
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [currentSession, analytics, saveAnalytics]);

  // Rastrear evento genérico
  const trackEvent = useCallback((type: string, action: string, data: Record<string, any> = {}) => {
    const event: AnalyticsEvent = {
      id: generateId(),
      type,
      action,
      data: {
        ...data,
        url: window.location.href,
        userAgent: navigator.userAgent,
        viewport: {
          width: window.innerWidth,
          height: window.innerHeight
        }
      },
      timestamp: Date.now(),
      sessionId: currentSession.id
    };

    const updatedSession = {
      ...currentSession,
      events: currentSession.events + 1
    };

    const updatedAnalytics = {
      ...analytics,
      events: [...analytics.events, event],
      currentSession: updatedSession
    };

    setCurrentSession(updatedSession);
    setAnalytics(updatedAnalytics);
    saveAnalytics(updatedAnalytics);

    // Log em desenvolvimento
    if (process.env.NODE_ENV === 'development') {
      console.log('📊 Analytics Event:', { type, action, data });
    }
  }, [currentSession, analytics, saveAnalytics]);

  // Rastrear visualização de página
  const trackPageView = useCallback((page: string, title?: string) => {
    const updatedSession = {
      ...currentSession,
      pageViews: currentSession.pageViews + 1
    };

    setCurrentSession(updatedSession);

    trackEvent('page_view', 'view', {
      page,
      title: title || document.title,
      referrer: document.referrer
    });
  }, [currentSession, trackEvent]);

  // Rastrear visualização de modelo 3D
  const trackModelView = useCallback((modelId: string, modelTitle: string, duration?: number) => {
    trackEvent('model_view', 'view', {
      modelId,
      modelTitle,
      duration,
      timestamp: Date.now()
    });
  }, [trackEvent]);

  // Rastrear interações
  const trackInteraction = useCallback((element: string, action: string, data: Record<string, any> = {}) => {
    trackEvent('interaction', action, {
      element,
      ...data
    });
  }, [trackEvent]);

  // Rastrear erros
  const trackError = useCallback((error: string, context: Record<string, any> = {}) => {
    trackEvent('error', 'error_occurred', {
      error,
      context,
      stack: new Error().stack
    });
  }, [trackEvent]);

  // Obter dados de analytics
  const getAnalytics = useCallback((): AnalyticsData => {
    return analytics;
  }, [analytics]);

  // Exportar analytics
  const exportAnalytics = useCallback((): string => {
    const exportData = {
      ...analytics,
      exportedAt: Date.now(),
      version: '1.0'
    };
    return JSON.stringify(exportData, null, 2);
  }, [analytics]);

  // Limpar analytics
  const clearAnalytics = useCallback(() => {
    const clearedData: AnalyticsData = {
      events: [],
      sessions: [],
      currentSession: currentSession
    };
    setAnalytics(clearedData);
    saveAnalytics(clearedData);
  }, [currentSession, saveAnalytics]);

  // Estatísticas da sessão atual
  const sessionStats = {
    duration: Date.now() - currentSession.startTime,
    pageViews: currentSession.pageViews,
    events: currentSession.events
  };

  // Auto-track de page view inicial
  useEffect(() => {
    trackPageView(window.location.pathname);
  }, []);

  // Auto-track de erros JavaScript
  useEffect(() => {
    const handleError = (event: ErrorEvent) => {
      trackError(event.message, {
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno
      });
    };

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      trackError('Unhandled Promise Rejection', {
        reason: event.reason
      });
    };

    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    return () => {
      window.removeEventListener('error', handleError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, [trackError]);

  return {
    trackEvent,
    trackPageView,
    trackModelView,
    trackInteraction,
    trackError,
    getAnalytics,
    exportAnalytics,
    clearAnalytics,
    sessionStats
  };
};
