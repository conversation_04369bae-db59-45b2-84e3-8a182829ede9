/**
 * Utilitário para ajustar a visualização de arquivos HTML do EXOCAD
 * 
 * Este script contém funções para modificar a aparência e comportamento
 * dos visualizadores 3D do EXOCAD quando carregados em iframes.
 */

/**
 * Modifica a cor de fundo do visualizador 3D do EXOCAD
 * @param iframe Referência ao elemento iframe
 * @param backgroundColor Cor de fundo desejada (hexadecimal ou rgba)
 */
export const setExocadViewerBackground = (
  iframe: HTMLIFrameElement | null, 
  backgroundColor: string = 'transparent'
): void => {
  if (!iframe || !iframe.contentWindow) return;
  
  try {
    // Tenta acessar o documento dentro do iframe
    const iframeDocument = iframe.contentWindow.document;
    
    // Verifica se conseguiu acessar o documento
    if (!iframeDocument) return;
    
    // Cria um script para ser injetado no iframe
    const script = iframeDocument.createElement('script');
    script.textContent = `
      // Função para modificar a cor de fundo do renderer
      function setRendererBackground() {
        // Verifica se o objeto renderer existe
        if (window.renderer) {
          // Define a cor de fundo como transparente
          window.renderer.setClearColor('${backgroundColor}', 1);
          console.log('EXOCAD viewer background color set to: ${backgroundColor}');
        } else {
          // Se o renderer ainda não foi inicializado, tenta novamente em 500ms
          setTimeout(setRendererBackground, 500);
        }
      }
      
      // Inicia a tentativa de modificar o fundo
      setRendererBackground();
    `;
    
    // Adiciona o script ao documento do iframe
    iframeDocument.head.appendChild(script);
    
  } catch (error) {
    console.error('Error setting EXOCAD viewer background:', error);
  }
};

/**
 * Observa quando um iframe é carregado e aplica a modificação de cor de fundo
 * @param iframeRef Referência React ao elemento iframe
 * @param backgroundColor Cor de fundo desejada
 */
export const setupExocadViewerBackgroundObserver = (
  iframeRef: React.RefObject<HTMLIFrameElement>,
  backgroundColor: string = 'transparent'
): (() => void) => {
  // Função para verificar e modificar o iframe
  const checkAndModifyIframe = () => {
    if (iframeRef.current) {
      setExocadViewerBackground(iframeRef.current, backgroundColor);
    }
  };
  
  // Configura um intervalo para tentar modificar o iframe periodicamente
  const intervalId = setInterval(checkAndModifyIframe, 1000);
  
  // Retorna uma função para limpar o intervalo quando não for mais necessário
  return () => clearInterval(intervalId);
};
