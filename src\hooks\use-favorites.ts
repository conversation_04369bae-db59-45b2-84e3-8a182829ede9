import { useState, useEffect, useCallback } from 'react';
import { toast } from '@/components/ui/sonner';

interface FavoriteItem {
  id: string;
  title: string;
  thumbnail: string;
  type: string;
  addedAt: number;
}

interface UseFavoritesReturn {
  favorites: FavoriteItem[];
  isFavorite: (id: string) => boolean;
  addToFavorites: (item: Omit<FavoriteItem, 'addedAt'>) => void;
  removeFromFavorites: (id: string) => void;
  toggleFavorite: (item: Omit<FavoriteItem, 'addedAt'>) => void;
  clearAllFavorites: () => void;
  getFavoritesByType: (type: string) => FavoriteItem[];
  favoritesCount: number;
  exportFavorites: () => string;
  importFavorites: (data: string) => boolean;
}

const STORAGE_KEY = 'dental-portfolio-favorites';
const MAX_FAVORITES = 50; // Limite máximo de favoritos

export const useFavorites = (): UseFavoritesReturn => {
  const [favorites, setFavorites] = useState<FavoriteItem[]>([]);

  // Carregar favoritos do localStorage na inicialização
  useEffect(() => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        if (Array.isArray(parsed)) {
          // Validar estrutura dos dados
          const validFavorites = parsed.filter(item => 
            item && 
            typeof item.id === 'string' && 
            typeof item.title === 'string' &&
            typeof item.addedAt === 'number'
          );
          setFavorites(validFavorites);
        }
      }
    } catch (error) {
      console.warn('Erro ao carregar favoritos:', error);
      // Limpar dados corrompidos
      localStorage.removeItem(STORAGE_KEY);
    }
  }, []);

  // Salvar favoritos no localStorage sempre que a lista mudar
  useEffect(() => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(favorites));
    } catch (error) {
      console.warn('Erro ao salvar favoritos:', error);
      toast.error('Erro ao salvar favoritos');
    }
  }, [favorites]);

  // Verificar se um item é favorito
  const isFavorite = useCallback((id: string): boolean => {
    return favorites.some(fav => fav.id === id);
  }, [favorites]);

  // Adicionar aos favoritos
  const addToFavorites = useCallback((item: Omit<FavoriteItem, 'addedAt'>) => {
    if (isFavorite(item.id)) {
      toast.info('Este caso já está nos seus favoritos');
      return;
    }

    if (favorites.length >= MAX_FAVORITES) {
      toast.error(`Máximo de ${MAX_FAVORITES} favoritos atingido`);
      return;
    }

    const newFavorite: FavoriteItem = {
      ...item,
      addedAt: Date.now()
    };

    setFavorites(prev => [newFavorite, ...prev]);
    toast.success('Adicionado aos favoritos!', {
      description: item.title
    });
  }, [favorites.length, isFavorite]);

  // Remover dos favoritos
  const removeFromFavorites = useCallback((id: string) => {
    const item = favorites.find(fav => fav.id === id);
    
    setFavorites(prev => prev.filter(fav => fav.id !== id));
    
    if (item) {
      toast.success('Removido dos favoritos', {
        description: item.title
      });
    }
  }, [favorites]);

  // Toggle favorito
  const toggleFavorite = useCallback((item: Omit<FavoriteItem, 'addedAt'>) => {
    if (isFavorite(item.id)) {
      removeFromFavorites(item.id);
    } else {
      addToFavorites(item);
    }
  }, [isFavorite, addToFavorites, removeFromFavorites]);

  // Limpar todos os favoritos
  const clearAllFavorites = useCallback(() => {
    setFavorites([]);
    toast.success('Todos os favoritos foram removidos');
  }, []);

  // Obter favoritos por tipo
  const getFavoritesByType = useCallback((type: string): FavoriteItem[] => {
    return favorites.filter(fav => fav.type === type);
  }, [favorites]);

  // Exportar favoritos como JSON
  const exportFavorites = useCallback((): string => {
    const exportData = {
      favorites,
      exportedAt: Date.now(),
      version: '1.0'
    };
    return JSON.stringify(exportData, null, 2);
  }, [favorites]);

  // Importar favoritos de JSON
  const importFavorites = useCallback((data: string): boolean => {
    try {
      const parsed = JSON.parse(data);
      
      if (!parsed.favorites || !Array.isArray(parsed.favorites)) {
        toast.error('Formato de dados inválido');
        return false;
      }

      // Validar estrutura dos favoritos
      const validFavorites = parsed.favorites.filter((item: any) => 
        item && 
        typeof item.id === 'string' && 
        typeof item.title === 'string' &&
        typeof item.addedAt === 'number'
      );

      if (validFavorites.length === 0) {
        toast.error('Nenhum favorito válido encontrado');
        return false;
      }

      // Mesclar com favoritos existentes (evitar duplicatas)
      const existingIds = new Set(favorites.map(fav => fav.id));
      const newFavorites = validFavorites.filter((item: FavoriteItem) => 
        !existingIds.has(item.id)
      );

      if (newFavorites.length === 0) {
        toast.info('Todos os favoritos já existem');
        return true;
      }

      setFavorites(prev => [...newFavorites, ...prev]);
      toast.success(`${newFavorites.length} favoritos importados com sucesso`);
      return true;

    } catch (error) {
      console.warn('Erro ao importar favoritos:', error);
      toast.error('Erro ao importar favoritos');
      return false;
    }
  }, [favorites]);

  return {
    favorites,
    isFavorite,
    addToFavorites,
    removeFromFavorites,
    toggleFavorite,
    clearAllFavorites,
    getFavoritesByType,
    favoritesCount: favorites.length,
    exportFavorites,
    importFavorites
  };
};

// Hook para estatísticas de favoritos
export const useFavoritesStats = () => {
  const { favorites } = useFavorites();

  const stats = {
    total: favorites.length,
    byType: favorites.reduce((acc, fav) => {
      acc[fav.type] = (acc[fav.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>),
    mostRecent: favorites[0] || null,
    oldest: favorites[favorites.length - 1] || null,
    addedThisWeek: favorites.filter(fav => 
      Date.now() - fav.addedAt < 7 * 24 * 60 * 60 * 1000
    ).length,
    addedThisMonth: favorites.filter(fav => 
      Date.now() - fav.addedAt < 30 * 24 * 60 * 60 * 1000
    ).length
  };

  return stats;
};
