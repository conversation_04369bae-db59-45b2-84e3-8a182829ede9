import { useState, useEffect, useCallback } from 'react';

interface UsePulseAnimationOptions {
  interval?: number; // Intervalo entre pulsos em milissegundos
  initialDelay?: number; // Delay inicial antes do primeiro pulso
  duration?: number; // Duração da animação de pulso
  enabled?: boolean; // Se a animação está habilitada
}

export const usePulseAnimation = ({
  interval = 12000,
  initialDelay = 3000,
  duration = 1200,
  enabled = true
}: UsePulseAnimationOptions = {}) => {
  const [isPulsing, setIsPulsing] = useState(false);
  const [pulseKey, setPulseKey] = useState(0);

  const triggerPulse = useCallback(() => {
    if (!enabled) return;
    
    setIsPulsing(true);
    setPulseKey(prev => prev + 1);
    
    // Para a animação após a duração especificada
    setTimeout(() => {
      setIsPulsing(false);
    }, duration);
  }, [enabled, duration]);

  useEffect(() => {
    if (!enabled) return;

    // Primeira animação após o delay inicial
    const initialTimeout = setTimeout(triggerPulse, initialDelay);

    // Animações subsequentes no intervalo especificado
    const intervalId = setInterval(triggerPulse, interval);

    return () => {
      clearTimeout(initialTimeout);
      clearInterval(intervalId);
    };
  }, [triggerPulse, interval, initialDelay, enabled]);

  // Função para pausar/retomar a animação
  const pauseAnimation = useCallback(() => {
    setIsPulsing(false);
  }, []);

  // Função para forçar um pulso imediato
  const forcePulse = useCallback(() => {
    triggerPulse();
  }, [triggerPulse]);

  return {
    isPulsing,
    pulseKey,
    pauseAnimation,
    forcePulse
  };
};
