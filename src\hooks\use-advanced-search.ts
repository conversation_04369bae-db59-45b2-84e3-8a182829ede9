import { useState, useMemo, useCallback } from 'react';
import { projectsData } from '@/data/projectsData';

interface SearchFilters {
  query: string;
  type: string[];
  material: string[];
  region: string[];
  difficulty: string[];
  dateRange: {
    start: Date | null;
    end: Date | null;
  };
  sortBy: 'relevance' | 'title' | 'type' | 'date';
  sortOrder: 'asc' | 'desc';
}

interface SearchResult {
  item: any;
  score: number;
  matchedFields: string[];
}

const defaultFilters: SearchFilters = {
  query: '',
  type: [],
  material: [],
  region: [],
  difficulty: [],
  dateRange: {
    start: null,
    end: null
  },
  sortBy: 'relevance',
  sortOrder: 'desc'
};

// Extrair metadados dos projetos
const extractMetadata = () => {
  const types = new Set<string>();
  const materials = new Set<string>();
  const regions = new Set<string>();
  const difficulties = new Set<string>();

  projectsData.forEach(project => {
    // Tipos
    if (project.type) types.add(project.type);

    // Extrair materiais do htmlContent
    const materialMatches = project.htmlContent?.match(/Material:\s*([^<\n]+)/gi);
    materialMatches?.forEach(match => {
      const material = match.replace(/Material:\s*/i, '').trim();
      if (material) materials.add(material);
    });

    // Extrair regiões do título e descrição
    const regionPatterns = [
      /(\d+)/g, // números de dentes
      /(superior|inferior)/gi,
      /(anterior|posterior)/gi,
      /(molar|pré-molar|canino|incisivo)/gi
    ];

    const textToAnalyze = `${project.title} ${project.description}`;
    regionPatterns.forEach(pattern => {
      const matches = textToAnalyze.match(pattern);
      matches?.forEach(match => regions.add(match.toLowerCase()));
    });

    // Dificuldades baseadas no tipo e complexidade
    if (project.type === 'PONTES') difficulties.add('Alta');
    else if (project.type === 'COROAS') difficulties.add('Média');
    else if (project.type === 'RESTAURADOS') difficulties.add('Baixa');
    else difficulties.add('Variável');
  });

  return {
    types: Array.from(types).sort(),
    materials: Array.from(materials).sort(),
    regions: Array.from(regions).sort(),
    difficulties: Array.from(difficulties).sort()
  };
};

export const useAdvancedSearch = () => {
  const [filters, setFilters] = useState<SearchFilters>(defaultFilters);
  const [isSearching, setIsSearching] = useState(false);

  // Metadados extraídos
  const metadata = useMemo(() => extractMetadata(), []);

  // Função de busca com pontuação
  const searchItems = useCallback((searchFilters: SearchFilters): SearchResult[] => {
    if (!searchFilters.query && 
        searchFilters.type.length === 0 && 
        searchFilters.material.length === 0 &&
        searchFilters.region.length === 0 &&
        searchFilters.difficulty.length === 0) {
      return projectsData.map(item => ({
        item,
        score: 1,
        matchedFields: []
      }));
    }

    const results: SearchResult[] = [];

    projectsData.forEach(project => {
      let score = 0;
      const matchedFields: string[] = [];

      // Busca por texto
      if (searchFilters.query) {
        const query = searchFilters.query.toLowerCase();
        const searchableText = `${project.title} ${project.description} ${project.htmlContent}`.toLowerCase();
        
        // Pontuação por relevância
        if (project.title.toLowerCase().includes(query)) {
          score += 10;
          matchedFields.push('title');
        }
        if (project.description.toLowerCase().includes(query)) {
          score += 5;
          matchedFields.push('description');
        }
        if (project.htmlContent?.toLowerCase().includes(query)) {
          score += 2;
          matchedFields.push('content');
        }

        // Busca por palavras individuais
        const queryWords = query.split(' ').filter(word => word.length > 2);
        queryWords.forEach(word => {
          if (searchableText.includes(word)) {
            score += 1;
          }
        });
      }

      // Filtro por tipo
      if (searchFilters.type.length > 0) {
        if (searchFilters.type.includes(project.type)) {
          score += 3;
          matchedFields.push('type');
        } else if (searchFilters.query) {
          // Se há busca por texto mas tipo não coincide, reduzir score
          score *= 0.5;
        } else {
          // Se só filtro por tipo e não coincide, excluir
          return;
        }
      }

      // Filtro por material
      if (searchFilters.material.length > 0) {
        const hasMatchingMaterial = searchFilters.material.some(material =>
          project.htmlContent?.toLowerCase().includes(material.toLowerCase())
        );
        if (hasMatchingMaterial) {
          score += 2;
          matchedFields.push('material');
        } else if (!searchFilters.query && searchFilters.type.length === 0) {
          return;
        }
      }

      // Filtro por região
      if (searchFilters.region.length > 0) {
        const projectText = `${project.title} ${project.description}`.toLowerCase();
        const hasMatchingRegion = searchFilters.region.some(region =>
          projectText.includes(region.toLowerCase())
        );
        if (hasMatchingRegion) {
          score += 2;
          matchedFields.push('region');
        } else if (!searchFilters.query && searchFilters.type.length === 0 && searchFilters.material.length === 0) {
          return;
        }
      }

      // Se chegou até aqui e tem alguma pontuação, incluir nos resultados
      if (score > 0 || (!searchFilters.query && searchFilters.type.length === 0 && searchFilters.material.length === 0 && searchFilters.region.length === 0)) {
        results.push({
          item: project,
          score: Math.max(score, 0.1), // Score mínimo para evitar 0
          matchedFields
        });
      }
    });

    // Ordenação
    results.sort((a, b) => {
      switch (searchFilters.sortBy) {
        case 'title':
          return searchFilters.sortOrder === 'asc' 
            ? a.item.title.localeCompare(b.item.title)
            : b.item.title.localeCompare(a.item.title);
        case 'type':
          return searchFilters.sortOrder === 'asc'
            ? a.item.type.localeCompare(b.item.type)
            : b.item.type.localeCompare(a.item.type);
        case 'relevance':
        default:
          return searchFilters.sortOrder === 'asc' ? a.score - b.score : b.score - a.score;
      }
    });

    return results;
  }, []);

  // Resultados da busca
  const searchResults = useMemo(() => {
    setIsSearching(true);
    const results = searchItems(filters);
    setIsSearching(false);
    return results;
  }, [filters, searchItems]);

  // Atualizar filtros
  const updateFilters = useCallback((newFilters: Partial<SearchFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  // Limpar filtros
  const clearFilters = useCallback(() => {
    setFilters(defaultFilters);
  }, []);

  // Busca rápida por texto
  const quickSearch = useCallback((query: string) => {
    updateFilters({ query });
  }, [updateFilters]);

  // Toggle de filtro
  const toggleFilter = useCallback((filterType: keyof SearchFilters, value: string) => {
    setFilters(prev => {
      const currentFilter = prev[filterType] as string[];
      const newFilter = currentFilter.includes(value)
        ? currentFilter.filter(item => item !== value)
        : [...currentFilter, value];
      
      return { ...prev, [filterType]: newFilter };
    });
  }, []);

  // Estatísticas da busca
  const searchStats = useMemo(() => {
    const totalResults = searchResults.length;
    const hasActiveFilters = filters.query || 
      filters.type.length > 0 || 
      filters.material.length > 0 || 
      filters.region.length > 0 ||
      filters.difficulty.length > 0;

    const typeDistribution = searchResults.reduce((acc, result) => {
      const type = result.item.type;
      acc[type] = (acc[type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalResults,
      hasActiveFilters,
      typeDistribution,
      averageScore: searchResults.length > 0 
        ? searchResults.reduce((sum, result) => sum + result.score, 0) / searchResults.length 
        : 0
    };
  }, [searchResults, filters]);

  return {
    filters,
    searchResults,
    searchStats,
    metadata,
    isSearching,
    updateFilters,
    clearFilters,
    quickSearch,
    toggleFilter
  };
};
