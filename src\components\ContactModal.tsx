
import React from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON>alogTitle, DialogClose } from "@/components/ui/dialog";
import { X, Mail, MapPin, Clock, Copy, ExternalLink, Phone, Instagram, Linkedin, Globe, Calendar } from 'lucide-react';
import { Button } from './ui/button';
import { toast } from '@/components/ui/sonner';
import { Separator } from './ui/separator';

interface ContactModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const ContactModal = ({ isOpen, onClose }: ContactModalProps) => {
  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text);
    toast(`${label} copiado para a área de transferência`, {
      position: "bottom-right",
      duration: 3000,
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="w-[95vw] max-h-[90vh] overflow-y-auto sm:max-w-[750px] p-0 border-none bg-transparent">
        <DialogClose asChild>
          <Button
            variant="ghost"
            size="icon"
            onClick={(e) => {
              e.preventDefault();
              onClose();
            }}
            className="absolute right-3 top-3 z-[100] bg-black/60 hover:bg-black/80 text-white"
          >
            <X className="h-4 w-4" />
          </Button>
        </DialogClose>
        <div className="p-3 sm:p-6 bg-gradient-to-br from-black/95 via-[#202020]/95 to-black/95 backdrop-blur-md rounded-lg">
          <DialogHeader className="relative mb-2">
            <DialogTitle className="text-lg sm:text-2xl font-medium text-white uppercase tracking-tight">Contato</DialogTitle>
          </DialogHeader>

          <div className="mt-3 sm:mt-6">
            {/* Header message */}
            <div className="text-center mb-3 sm:mb-6">
              <h3 className="text-base sm:text-lg font-medium text-white flex flex-col sm:flex-row items-center justify-center gap-1 sm:gap-2 mb-2">
                <span>FORM & FUNCTION</span><span className="hidden sm:inline">/</span><span>JONHNATAS LIMA</span>
              </h3>
              <p className="text-xs sm:text-sm text-white/80 max-w-md mx-auto">
                Especialista em próteses dentárias e soluções digitais para odontologia.
                Atendimento personalizado para profissionais e clínicas.
              </p>
            </div>

            <Separator className="my-4 bg-gray-800/30" />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 sm:gap-4 mb-4 sm:mb-6">
              {/* Email */}
              <div className="bg-black/50 backdrop-blur-sm rounded-lg p-2 sm:p-4 transition-all hover:bg-[#202020]/60 border border-gray-800/40">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2 sm:gap-3 text-white">
                    <Mail className="h-4 w-4 sm:h-5 sm:w-5 text-white/70 flex-shrink-0" />
                    <a href="mailto:<EMAIL>" className="text-xs sm:text-base hover:underline truncate">
                      <EMAIL>
                    </a>
                  </div>
                  <button
                    onClick={() => copyToClipboard("<EMAIL>", "Email")}
                    className="text-white/50 hover:text-white transition-colors p-1 sm:p-1.5 rounded-md hover:bg-white/5"
                  >
                    <Copy className="h-3 w-3 sm:h-4 sm:w-4" />
                  </button>
                </div>
              </div>

              {/* WhatsApp */}
              <div className="bg-black/50 backdrop-blur-sm rounded-lg p-2 sm:p-4 transition-all hover:bg-[#202020]/60 border border-gray-800/40">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2 sm:gap-3 text-white">
                    <Phone className="h-4 w-4 sm:h-5 sm:w-5 text-white/70 flex-shrink-0" />
                    <span className="text-xs sm:text-base truncate">
                      WhatsApp: +55 (31) 9 9069-7788
                    </span>
                  </div>
                  <a
                    href="https://wa.me/5531990697788"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-white/50 hover:text-white transition-colors p-1 sm:p-1.5 rounded-md hover:bg-white/5"
                  >
                    <ExternalLink className="h-3 w-3 sm:h-4 sm:w-4" />
                  </a>
                </div>
              </div>

              {/* Website */}
              <div className="bg-black/50 backdrop-blur-sm rounded-lg p-2 sm:p-4 transition-all hover:bg-[#202020]/60 border border-gray-800/40">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2 sm:gap-3 text-white">
                    <Globe className="h-4 w-4 sm:h-5 sm:w-5 text-white/70 flex-shrink-0" />
                    <span className="text-xs sm:text-base truncate">
                      www.jhonn3d.com.br
                    </span>
                  </div>
                  <a
                    href="https://www.jhonn3d.com.br"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-white/50 hover:text-white transition-colors p-1 sm:p-1.5 rounded-md hover:bg-white/5"
                  >
                    <ExternalLink className="h-3 w-3 sm:h-4 sm:w-4" />
                  </a>
                </div>
              </div>

              {/* Instagram */}
              <div className="bg-black/50 backdrop-blur-sm rounded-lg p-2 sm:p-4 transition-all hover:bg-[#202020]/60 border border-gray-800/40">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2 sm:gap-3 text-white">
                    <Instagram className="h-4 w-4 sm:h-5 sm:w-5 text-white/70 flex-shrink-0" />
                    <span className="text-xs sm:text-base truncate">
                      @jhonn.3d
                    </span>
                  </div>
                  <a
                    href="https://instagram.com/jhonn.3d"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-white/50 hover:text-white transition-colors p-1 sm:p-1.5 rounded-md hover:bg-white/5"
                  >
                    <ExternalLink className="h-3 w-3 sm:h-4 sm:w-4" />
                  </a>
                </div>
              </div>
            </div>

            <Separator className="my-4 bg-gray-800/30" />

            {/* Professional Info */}
            <div className="bg-black/50 backdrop-blur-sm rounded-lg p-2 sm:p-4 transition-all hover:bg-[#202020]/60 border border-gray-800/40 mb-3 sm:mb-6">
              <h4 className="text-white text-sm sm:text-base font-medium mb-1 sm:mb-2 flex items-center gap-1 sm:gap-2">
                <Calendar className="h-3 w-3 sm:h-4 sm:w-4 text-white/70" />
                Agendamento de Consultas
              </h4>
              <p className="text-xs sm:text-sm text-white/80 mb-2 sm:mb-3">
                Para agendamento de consultas e avaliações, entre em contato via WhatsApp ou email.
                Atendemos profissionais e clínicas com horário marcado.
              </p>

            </div>

            <div className="flex justify-center">
              <Button
                variant="default"
                onClick={() => window.open("https://wa.me/5531990697788", "_blank")}
                className="bg-black/70 hover:bg-[#202020]/90 text-white text-xs sm:text-sm py-1 h-auto"
              >
                <Phone className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                Contato via WhatsApp
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ContactModal;
