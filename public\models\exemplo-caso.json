{"id": "exemplo-stl-001", "title": "Exemplo - Prótese Superior e Inferior", "type": "PRÓTESES", "description": "Caso exemplo demonstrando múltiplos modelos STL do EXOCAD", "thumbnail": "/images/exemplo-protese.jpg", "modelUrl": "", "stlModels": [{"id": "superior", "name": "Arcada Superior", "url": "/models/exemplo/superior.stl", "color": "#e3f2fd", "visible": true, "position": [0, 1, 0], "rotation": [0, 0, 0], "scale": [1, 1, 1]}, {"id": "inferior", "name": "Arcada Inferior", "url": "/models/exemplo/inferior.stl", "color": "#f3e5f5", "visible": true, "position": [0, -1, 0], "rotation": [0, 0, 0], "scale": [1, 1, 1]}, {"id": "coroa-11", "name": "Coroa 11", "url": "/models/exemplo/coroa-11.stl", "color": "#e8f5e8", "visible": true, "position": [0.5, 0.5, 0], "rotation": [0, 0, 0], "scale": [1, 1, 1]}, {"id": "coroa-21", "name": "Coroa 21", "url": "/models/exemplo/coroa-21.stl", "color": "#fff3e0", "visible": true, "position": [-0.5, 0.5, 0], "rotation": [0, 0, 0], "scale": [1, 1, 1]}], "galleryImages": ["/images/exemplo-1.jpg", "/images/exemplo-2.jpg", "/images/exemplo-3.jpg"], "htmlContent": "<h2>Caso Exemplo - EXOCAD STL</h2><p>Este é um caso exemplo demonstrando como configurar múltiplos modelos STL exportados do EXOCAD.</p><ul><li>Arcada Superior: Modelo completo da arcada superior</li><li>Arcada Inferior: Modelo completo da arcada inferior</li><li>Coroas individuais: Modelos específicos das coroas 11 e 21</li></ul>", "exocadHtmlUrl": ""}