
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&family=Outfit:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* ===== PALETA DE CORES PROFISSIONAL ODONTOLÓGICA ===== */
:root {
  /* Cores Primárias - Confiança e Profissionalismo */
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-200: #bfdbfe;
  --color-primary-300: #93c5fd;
  --color-primary-400: #60a5fa;
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;
  --color-primary-800: #1e40af;
  --color-primary-900: #1e3a8a;

  /* <PERSON><PERSON> (padr<PERSON>) */
  --background: #ffffff;
  --foreground: #0f172a;
  --card: #ffffff;
  --card-foreground: #0f172a;
  --popover: #ffffff;
  --popover-foreground: #0f172a;
  --primary: #2563eb;
  --primary-foreground: #ffffff;
  --secondary: #f1f5f9;
  --secondary-foreground: #0f172a;
  --muted: #f1f5f9;
  --muted-foreground: #64748b;
  --accent: #f1f5f9;
  --accent-foreground: #0f172a;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: #e2e8f0;
  --input: #e2e8f0;
  --ring: #2563eb;

  /* Cores Secundárias - Inovação e Tecnologia */
  --color-secondary-50: #f5f3ff;
  --color-secondary-100: #ede9fe;
  --color-secondary-200: #ddd6fe;
  --color-secondary-300: #c4b5fd;
  --color-secondary-400: #a78bfa;
  --color-secondary-500: #8b5cf6;
  --color-secondary-600: #7c3aed;
  --color-secondary-700: #6d28d9;
  --color-secondary-800: #5b21b6;
  --color-secondary-900: #4c1d95;

  /* Cores de Accent - Precisão e Tecnologia */
  --color-accent-50: #ecfeff;
  --color-accent-100: #cffafe;
  --color-accent-200: #a5f3fc;
  --color-accent-300: #67e8f9;
  --color-accent-400: #22d3ee;
  --color-accent-500: #06b6d4;
  --color-accent-600: #0891b2;
  --color-accent-700: #0e7490;
  --color-accent-800: #155e75;
  --color-accent-900: #164e63;

  /* Cores Neutras - Elegância e Sofisticação */
  --color-neutral-50: #f8fafc;
  --color-neutral-100: #f1f5f9;
  --color-neutral-200: #e2e8f0;
  --color-neutral-300: #cbd5e1;
  --color-neutral-400: #94a3b8;
  --color-neutral-500: #64748b;
  --color-neutral-600: #475569;
  --color-neutral-700: #334155;
  --color-neutral-800: #1e293b;
  --color-neutral-900: #0f172a;

  /* Cores de Status */
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #3b82f6;

  /* Gradientes Profissionais */
  --gradient-primary: linear-gradient(135deg, var(--color-primary-600) 0%, var(--color-secondary-600) 100%);
  --gradient-accent: linear-gradient(135deg, var(--color-accent-500) 0%, var(--color-primary-500) 100%);
  --gradient-neutral: linear-gradient(135deg, var(--color-neutral-800) 0%, var(--color-neutral-900) 100%);

  /* Sombras Profissionais */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --shadow-glow: 0 0 20px rgb(59 130 246 / 0.3);

  /* Tipografia Responsiva */
  --step-xs: clamp(0.75rem, 0.7rem + 0.25vw, 0.875rem);
  --step-sm: clamp(0.875rem, 0.8rem + 0.375vw, 1rem);
  --step-base: clamp(1rem, 0.9rem + 0.5vw, 1.125rem);
  --step-lg: clamp(1.125rem, 1rem + 0.625vw, 1.25rem);
  --step-xl: clamp(1.25rem, 1.1rem + 0.75vw, 1.5rem);
  --step-2xl: clamp(1.5rem, 1.3rem + 1vw, 1.875rem);
  --step-3xl: clamp(1.875rem, 1.6rem + 1.375vw, 2.25rem);
  --step-4xl: clamp(2.25rem, 1.9rem + 1.75vw, 3rem);
  --step-5xl: clamp(3rem, 2.5rem + 2.5vw, 4rem);
}

/* ===== TEMA ESCURO ===== */
[data-theme="dark"] {
  --background: #0f172a;
  --foreground: #f8fafc;
  --card: #1e293b;
  --card-foreground: #f8fafc;
  --popover: #1e293b;
  --popover-foreground: #f8fafc;
  --primary: #3b82f6;
  --primary-foreground: #ffffff;
  --secondary: #1e293b;
  --secondary-foreground: #f8fafc;
  --muted: #1e293b;
  --muted-foreground: #94a3b8;
  --accent: #1e293b;
  --accent-foreground: #f8fafc;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: #334155;
  --input: #334155;
  --ring: #3b82f6;

  /* Cores específicas do tema escuro */
  --color-neutral-50: #1e293b;
  --color-neutral-100: #334155;
  --color-neutral-200: #475569;
  --color-neutral-300: #64748b;
  --color-neutral-400: #94a3b8;
  --color-neutral-500: #cbd5e1;
  --color-neutral-600: #e2e8f0;
  --color-neutral-700: #f1f5f9;
  --color-neutral-800: #f8fafc;
  --color-neutral-900: #ffffff;

  /* Gradientes para tema escuro */
  --gradient-primary: linear-gradient(135deg, var(--color-primary-500) 0%, var(--color-secondary-500) 100%);
  --gradient-accent: linear-gradient(135deg, var(--color-accent-400) 0%, var(--color-primary-400) 100%);
  --gradient-neutral: linear-gradient(135deg, var(--color-neutral-700) 0%, var(--color-neutral-800) 100%);
}

/* Auto tema baseado na preferência do sistema */
@media (prefers-color-scheme: dark) {
  :root:not([data-theme]) {
    --background: #0f172a;
    --foreground: #f8fafc;
    --card: #1e293b;
    --card-foreground: #f8fafc;
    --popover: #1e293b;
    --popover-foreground: #f8fafc;
    --primary: #3b82f6;
    --primary-foreground: #ffffff;
    --secondary: #1e293b;
    --secondary-foreground: #f8fafc;
    --muted: #1e293b;
    --muted-foreground: #94a3b8;
    --accent: #1e293b;
    --accent-foreground: #f8fafc;
    --destructive: #ef4444;
    --destructive-foreground: #ffffff;
    --border: #334155;
    --input: #334155;
    --ring: #3b82f6;
  }
}

/* ===== ANIMAÇÕES PERSONALIZADAS ===== */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.3); }
  50% { box-shadow: 0 0 30px rgba(59, 130, 246, 0.6); }
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

@keyframes morphing {
  0%, 100% { border-radius: 50%; transform: rotate(0deg) scale(1); }
  25% { border-radius: 25%; transform: rotate(90deg) scale(1.1); }
  50% { border-radius: 10%; transform: rotate(180deg) scale(0.9); }
  75% { border-radius: 25%; transform: rotate(270deg) scale(1.1); }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* ===== CLASSES UTILITÁRIAS PERSONALIZADAS ===== */
@layer utilities {
  /* Gradientes */
  .bg-gradient-primary {
    background: var(--gradient-primary);
  }

  .bg-gradient-accent {
    background: var(--gradient-accent);
  }

  .bg-gradient-neutral {
    background: var(--gradient-neutral);
  }

  .text-gradient-primary {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .text-gradient-accent {
    background: var(--gradient-accent);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  /* Sombras */
  .shadow-glow {
    box-shadow: var(--shadow-glow);
  }

  .shadow-professional {
    box-shadow: var(--shadow-xl);
  }

  /* Animações */
  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite;
  }

  .animate-shimmer {
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
  }

  .animate-morphing {
    animation: morphing 4s ease-in-out infinite;
  }

  .animate-slide-in-up {
    animation: slideInUp 0.6s ease-out;
  }

  .animate-slide-in-right {
    animation: slideInRight 0.6s ease-out;
  }

  .animate-fade-in-scale {
    animation: fadeInScale 0.5s ease-out;
  }

  /* Tipografia Responsiva */
  .text-step-xs { font-size: var(--step-xs); }
  .text-step-sm { font-size: var(--step-sm); }
  .text-step-base { font-size: var(--step-base); }
  .text-step-lg { font-size: var(--step-lg); }
  .text-step-xl { font-size: var(--step-xl); }
  .text-step-2xl { font-size: var(--step-2xl); }
  .text-step-3xl { font-size: var(--step-3xl); }
  .text-step-4xl { font-size: var(--step-4xl); }
  .text-step-5xl { font-size: var(--step-5xl); }

  /* Micro-interações */
  .hover-lift {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
  }

  .hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
  }

  .hover-glow {
    transition: box-shadow 0.3s ease;
  }

  .hover-glow:hover {
    box-shadow: var(--shadow-glow);
  }

  .hover-scale {
    transition: transform 0.2s ease;
  }

  .hover-scale:hover {
    transform: scale(1.02);
  }

  /* Glassmorphism */
  .glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .glass-dark {
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
}

@layer base {
  :root {
    /* Dark theme by default */
    --background: 0 0% 0%;
    --foreground: 0 0% 100%;

    --card: 0 0% 0%;
    --card-foreground: 0 0% 100%;

    --popover: 0 0% 0%;
    --popover-foreground: 0 0% 100%;

    --primary: 0 0% 100%;
    --primary-foreground: 0 0% 0%;

    --secondary: 0 0% 12%;
    --secondary-foreground: 0 0% 100%;

    --muted: 0 0% 12%;
    --muted-foreground: 0 0% 63.9%;

    --accent: 0 0% 12%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 0 0% 12%;
    --input: 0 0% 12%;
    --ring: 0 0% 20%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 0%;
    --sidebar-foreground: 0 0% 100%;
    --sidebar-primary: 0 0% 100%;
    --sidebar-primary-foreground: 0 0% 0%;
    --sidebar-accent: 0 0% 12%;
    --sidebar-accent-foreground: 0 0% 100%;
    --sidebar-border: 0 0% 15%;
    --sidebar-ring: 0 0% 83.9%;

    /* Purple gradient colors */
    --purple-primary: 259 81% 74%;
    --purple-secondary: 267 57% 54%;
    --purple-dark: 276 27% 14%;

    /* Responsive font size steps using clamp() */
    --step-xs: clamp(0.8rem, 0.9vw, 1rem);
    --step-sm: clamp(1rem, 1.2vw, 1.25rem);
    --step-md: clamp(1.2rem, 1.6vw, 1.6rem);
    --step-lg: clamp(1.5rem, 2vw, 2.2rem);
    --step-xl: clamp(2rem, 3vw, 3rem);

    /* Responsive spacing */
    --space-xs: clamp(0.5rem, 1vw, 0.75rem);
    --space-sm: clamp(0.75rem, 1.5vw, 1rem);
    --space-md: clamp(1rem, 2vw, 1.5rem);
    --space-lg: clamp(1.5rem, 3vw, 2rem);
    --space-xl: clamp(2rem, 4vw, 3rem);
  }

  * {
    @apply border-border selection:bg-white/10 selection:text-white;
  }

  body {
    @apply bg-background text-foreground antialiased;
    font-family: 'Outfit', sans-serif;
  }

  /* Scrollbar styling */
  ::-webkit-scrollbar {
    height: 4px;
    width: 4px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-white/30 rounded-full;
  }

  /* Custom classes */
  .gallery-container {
    @apply pb-4;
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted-foreground)) transparent;
  }

  /* Hide scrollbar but keep functionality */
  .hide-scrollbar {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }

  .hide-scrollbar::-webkit-scrollbar {
    display: none;  /* Chrome, Safari and Opera */
    width: 0;
    height: 0;
  }

  /* Mobile carousel scrollbar styling */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
  }

  .scrollbar-thin::-webkit-scrollbar {
    height: 3px;
    width: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.3);
    border-radius: 20px;
  }

  /* Responsive adjustments for different screen sizes */
  @media (max-width: 1440px) {
    h1 {
      font-size: clamp(1.5rem, 4vw, 2.5rem);
    }

    h2 {
      font-size: clamp(1.3rem, 3vw, 2rem);
    }

    .categoria-tags {
      flex-wrap: wrap;
      font-size: 0.9rem;
      gap: 0.5rem;
    }

    .intro-section {
      padding: 1rem 2rem;
      gap: 2vh;
    }
  }

  @media (max-width: 1366px) {
    html {
      font-size: 14px;
    }

    .intro-section h1 {
      font-size: clamp(1.3rem, 3vw, 2rem);
    }

    .card-3d {
      max-width: 90vw;
      height: auto;
    }
  }

  /* Ensure no horizontal overflow on mobile */
  @media (max-width: 768px) {
    html, body {
      overflow-x: hidden;
      width: 100%;
      position: relative;
    }
  }

  .project-item {
    @apply transition-all duration-300;
  }

  /* Viewer container for 3D models - Enhanced for immersive display */
  .viewer-container {
    @apply bg-black dark:bg-black h-full rounded-l-2xl overflow-hidden relative;
  }

  .viewer-container iframe {
    @apply absolute inset-0 w-full h-full scale-105 bg-transparent;
  }

  /* Full screen 3D viewer for cases page */
  .fullscreen-viewer {
    @apply fixed inset-0 w-screen h-screen z-0;
  }

  .fullscreen-viewer iframe {
    @apply w-full h-full scale-110 border-none;
  }

  /* Purple gradient background */
  .purple-gradient {
    @apply bg-gradient-to-br from-[hsl(var(--purple-primary))] to-[hsl(var(--purple-dark))];
  }

  /* Styled HTML content for case details */
  .styled-html-content {
    @apply text-base leading-relaxed;
  }

  .styled-html-content h1 {
    @apply text-2xl font-semibold mb-4 text-white;
  }

  .styled-html-content h2 {
    @apply text-xl font-semibold mb-3 text-white;
  }

  .styled-html-content p {
    @apply mb-4 text-white/70;
  }

  .styled-html-content img {
    @apply rounded-lg my-4 max-w-full h-auto;
  }

  @media (max-width: 768px) {
    .viewer-container {
      @apply rounded-t-2xl rounded-b-none;
    }
  }

  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.6s ease-in-out;
  }

  .animate-fade-up {
    animation: fadeUp 0.6s ease-in-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.5s ease-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes fadeUp {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  /* New animations */
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Custom dialog positioning */
  .custom-dialog-positioning [data-radix-popper-content-wrapper] {
    right: 2vw !important;
    left: auto !important;
    transform: none !important;
    width: 85vw !important;
    max-width: 1260px !important;
  }

  /* Estilo para bloquear eventos de mouse no modal quando o lightbox está aberto */
  body.lightbox-open .DialogContent {
    pointer-events: none;
  }

  /* Estilos para garantir que os botões do lightbox funcionem corretamente */
  .fixed.inset-0.bg-black\/90.z-\[9999\] button {
    pointer-events: auto !important;
  }

  .fixed.inset-0.bg-black\/90.z-\[9999\] {
    pointer-events: auto !important;
  }

  .fixed.inset-0.bg-black\/90.z-\[9999\] * {
    pointer-events: auto !important;
  }

  @keyframes slideInFromRight {
    from {
      opacity: 0;
      transform: translateY(-50%) translateX(15%);
    }
    to {
      opacity: 1;
      transform: translateY(-50%) translateX(0);
    }
  }

  @keyframes slideOutToRight {
    from {
      opacity: 1;
      transform: translateY(-50%) translateX(0);
    }
    to {
      opacity: 0;
      transform: translateY(-50%) translateX(15%);
    }
  }

  @keyframes expandDown {
    from {
      max-height: 0;
      opacity: 0;
    }
    to {
      max-height: 500px;
      opacity: 1;
    }
  }

  @keyframes collapseUp {
    from {
      max-height: 500px;
      opacity: 1;
    }
    to {
      max-height: 0;
      opacity: 0;
    }
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 1;
      transform: scale(1);
    }
    50% {
      opacity: 0.7;
      transform: scale(1.05);
    }
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-10px);
    }
  }

  /* Text underline animation */
  @keyframes scale-x-100 {
    0% {
      transform: scaleX(0);
    }
    100% {
      transform: scaleX(1);
    }
  }

  /* Glass morphism utilities */
  .glass {
    @apply bg-white/5 dark:bg-black/5 backdrop-blur-md border border-white/10 dark:border-white/5;
  }

  .glass-card {
    @apply bg-white/5 dark:bg-black/5 backdrop-blur-md border border-white/10 dark:border-white/5 rounded-lg shadow-sm;
  }

  /* Add new hover effects */
  .hover-scale {
    @apply transition-transform duration-300 hover:scale-105;
  }

  .hover-glow {
    @apply transition-all duration-300;
  }

  .hover-glow:hover {
    filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.5));
  }

  /* Add text effects */
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }

  .text-glow {
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
  }

  /* Intro category styling */
  .intro-category {
    font-size: clamp(0.75rem, 1vw, 1rem);
  }

  /* CTA Button Pulse Animation - Cores do gradiente FORM & FUNCTION */
  @keyframes ctaPulse {
    0% {
      box-shadow: 0 0 0 0 rgba(168, 85, 247, 0.5);
      transform: scale(1);
    }
    50% {
      box-shadow:
        0 0 0 6px rgba(168, 85, 247, 0.15),
        0 0 0 12px rgba(236, 72, 153, 0.1),
        0 0 20px rgba(59, 130, 246, 0.3);
      transform: scale(1.015);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(168, 85, 247, 0.5);
      transform: scale(1);
    }
  }

  @keyframes ctaGradientShift {
    0% {
      background: linear-gradient(135deg, rgba(168, 85, 247, 0.12) 0%, rgba(59, 130, 246, 0.12) 100%);
      border-color: rgba(168, 85, 247, 0.25);
    }
    33% {
      background: linear-gradient(135deg, rgba(236, 72, 153, 0.15) 0%, rgba(168, 85, 247, 0.15) 50%, rgba(59, 130, 246, 0.12) 100%);
      border-color: rgba(236, 72, 153, 0.35);
    }
    66% {
      background: linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(236, 72, 153, 0.15) 50%, rgba(168, 85, 247, 0.12) 100%);
      border-color: rgba(59, 130, 246, 0.35);
    }
    100% {
      background: linear-gradient(135deg, rgba(168, 85, 247, 0.12) 0%, rgba(59, 130, 246, 0.12) 100%);
      border-color: rgba(168, 85, 247, 0.25);
    }
  }

  .cta-pulse-animation {
    animation: ctaPulse 1.2s ease-in-out, ctaGradientShift 1.2s ease-in-out;
  }

  /* Subtle glow effect for the button */
  .cta-glow {
    position: relative;
    overflow: hidden;
  }

  .cta-glow::before {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    background: linear-gradient(135deg,
      rgba(168, 85, 247, 0.4) 0%,
      rgba(236, 72, 153, 0.4) 50%,
      rgba(59, 130, 246, 0.4) 100%);
    border-radius: inherit;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.4s ease;
  }

  .cta-glow.active::before {
    opacity: 1;
  }

  /* Efeito adicional para hover */
  .cta-button:hover {
    box-shadow: 0 0 15px rgba(168, 85, 247, 0.3);
  }
}

@layer components {
  .nav-link {
    @apply text-sm font-medium text-white/60 hover:text-white transition-colors;
  }
}
