
import React, { useState } from 'react';
import { projectsData } from '@/data/projectsData';
import { toast } from '@/components/ui/sonner';
import { Dialog } from '@/components/ui/dialog';

// Import custom hooks
import { useCases } from '@/hooks/use-cases';
import { useIntro } from '@/hooks/use-intro';
import { useModals } from '@/components/cases/ModalsManager';
import { useHybridViewer } from '@/hooks/use-hybrid-viewer';
import { useAnalytics } from '@/hooks/use-analytics';
import { useFavorites } from '@/hooks/use-favorites';
import { usePresentationMode } from '@/hooks/use-presentation-mode';

// Import components
import ModelViewer from '@/components/cases/ModelViewer';
import IntroSection from '@/components/cases/IntroSection';
import CasesContent from '@/components/cases/CasesContent';
import HtmlContentModal from '@/components/cases/HtmlContentModal';
import ContactModal from '@/components/ContactModal';
import AboutModal from '@/components/AboutModal';
import FloatingControls from '@/components/ui/FloatingControls';

const Cases = () => {
  // Custom hooks for state management
  const {
    currentCase, transitioning, modelLoading,
    filterCategory, categories, filteredThumbnails,
    setFilterCategory, goToNextCase, goToPreviousCase,
    handleModelLoad, handleSelectProject
  } = useCases();

  const { introVisible, textAnimation, skipIntro } = useIntro();
  const {
    htmlModalOpen, contactModalOpen, aboutModalOpen,
    setHtmlModalOpen, setContactModalOpen, setAboutModalOpen,
    handleContactOpen, handleAboutOpen
  } = useModals();

  // Hook para visualizador híbrido (habilitado apenas em desenvolvimento)
  const { isEnabled: hybridViewerEnabled } = useHybridViewer({
    enableInDevelopment: true,
    enableInProduction: false,
    defaultEnabled: false
  });

  // Analytics
  const { trackModelView, trackInteraction } = useAnalytics();

  // Favoritos
  const { favoritesCount } = useFavorites();

  // Modo apresentação
  const { startPresentation } = usePresentationMode();

  // Local state for UI elements
  const [infoPanelCollapsed, setInfoPanelCollapsed] = useState(false);

  const toggleInfoPanel = () => {
    setInfoPanelCollapsed(!infoPanelCollapsed);
  };

  return (
    <div className="min-h-screen bg-black overflow-hidden">
      {/* 3D Model Viewer - load in background during intro */}
      <ModelViewer
        modelUrl={currentCase.modelUrl}
        title={currentCase.title}
        modelLoading={modelLoading}
        transitioning={transitioning}
        onLoad={handleModelLoad}
        introVisible={introVisible}
        useHybridViewer={hybridViewerEnabled}
      />

      {/* Intro Section - updated with modelLoaded prop and onSkipIntro callback */}
      <IntroSection
        introVisible={introVisible}
        textAnimation={textAnimation}
        onSkipIntro={skipIntro}
        modelLoaded={!modelLoading}
      />

      {/* Main content layout */}
      <CasesContent
        introVisible={introVisible}
        currentCase={currentCase}
        infoPanelCollapsed={infoPanelCollapsed}
        toggleInfoPanel={toggleInfoPanel}
        openHtmlModal={() => setHtmlModalOpen(true)}
        openContactModal={handleContactOpen}
        openAboutModal={handleAboutOpen}
        categories={categories}
        filterCategory={filterCategory}
        setFilterCategory={setFilterCategory}
        filteredThumbnails={filteredThumbnails}
        onSelectProject={handleSelectProject}
        goToNextCase={goToNextCase}
        goToPreviousCase={goToPreviousCase}
      />

      {/* Modals */}
      <HtmlContentModal
        isOpen={htmlModalOpen}
        onOpenChange={setHtmlModalOpen}
        htmlContent={currentCase.htmlContent}
        exocadHtmlUrl={currentCase.exocadHtmlUrl}
        galleryImages={currentCase.galleryImages}
        title={currentCase.title}
        type={currentCase.type}
        description={currentCase.description}
      />

      <ContactModal
        isOpen={contactModalOpen}
        onClose={() => setContactModalOpen(false)}
      />

      <AboutModal
        isOpen={aboutModalOpen}
        onClose={() => setAboutModalOpen(false)}
      />

      {/* Controles flutuantes */}
      <FloatingControls currentCase={currentCase} />
    </div>
  );
};

export default Cases;
