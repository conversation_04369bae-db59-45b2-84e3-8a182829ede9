import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>, Moon, Monitor, Palette } from 'lucide-react';
import { useTheme } from '@/hooks/use-theme';
import { cn } from '@/lib/utils';

interface ThemeToggleProps {
  variant?: 'button' | 'fab' | 'minimal' | 'dropdown';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  showLabel?: boolean;
}

const ThemeToggle: React.FC<ThemeToggleProps> = ({
  variant = 'button',
  size = 'md',
  className,
  showLabel = false
}) => {
  const { theme, setTheme, actualTheme, toggleTheme, isSystemTheme } = useTheme();
  const [isOpen, setIsOpen] = React.useState(false);

  // Configurações de tamanho
  const sizes = {
    sm: {
      button: "w-8 h-8",
      icon: "w-4 h-4",
      text: "text-xs"
    },
    md: {
      button: "w-10 h-10",
      icon: "w-5 h-5",
      text: "text-sm"
    },
    lg: {
      button: "w-12 h-12",
      icon: "w-6 h-6",
      text: "text-base"
    }
  };

  const sizeConfig = sizes[size];

  // Ícones por tema
  const getThemeIcon = () => {
    if (isSystemTheme) return Monitor;
    return actualTheme === 'dark' ? Moon : Sun;
  };

  const ThemeIcon = getThemeIcon();

  // Opções de tema
  const themeOptions = [
    {
      value: 'light' as const,
      label: 'Claro',
      icon: Sun,
      description: 'Tema claro'
    },
    {
      value: 'dark' as const,
      label: 'Escuro',
      icon: Moon,
      description: 'Tema escuro'
    },
    {
      value: 'system' as const,
      label: 'Sistema',
      icon: Monitor,
      description: 'Seguir sistema'
    }
  ];

  // Renderizar botão simples
  const renderSimpleButton = () => (
    <motion.button
      className={cn(
        "flex items-center justify-center rounded-lg transition-all duration-200",
        "bg-background/50 backdrop-blur-sm border border-border",
        "hover:bg-accent hover:text-accent-foreground",
        "focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
        sizeConfig.button,
        className
      )}
      onClick={toggleTheme}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      title={`Tema atual: ${theme === 'system' ? 'Sistema' : theme === 'dark' ? 'Escuro' : 'Claro'}`}
    >
      <motion.div
        key={actualTheme + (isSystemTheme ? '-system' : '')}
        initial={{ rotate: -180, opacity: 0 }}
        animate={{ rotate: 0, opacity: 1 }}
        exit={{ rotate: 180, opacity: 0 }}
        transition={{ duration: 0.3 }}
      >
        <ThemeIcon className={sizeConfig.icon} />
      </motion.div>
    </motion.button>
  );

  // Renderizar FAB
  const renderFAB = () => (
    <motion.button
      className={cn(
        "fixed bottom-32 right-6 rounded-full shadow-lg z-40",
        "bg-primary text-primary-foreground",
        "hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-ring",
        sizeConfig.button,
        className
      )}
      onClick={toggleTheme}
      whileHover={{ scale: 1.1 }}
      whileTap={{ scale: 0.9 }}
      initial={{ scale: 0, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      transition={{ 
        type: "spring", 
        stiffness: 260, 
        damping: 20,
        delay: 0.7 
      }}
    >
      <motion.div
        key={actualTheme + (isSystemTheme ? '-system' : '')}
        initial={{ rotate: -180, opacity: 0 }}
        animate={{ rotate: 0, opacity: 1 }}
        transition={{ duration: 0.3 }}
      >
        <ThemeIcon className={sizeConfig.icon} />
      </motion.div>
    </motion.button>
  );

  // Renderizar versão minimal
  const renderMinimal = () => (
    <motion.button
      className={cn(
        "flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors",
        sizeConfig.text,
        className
      )}
      onClick={toggleTheme}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
    >
      <motion.div
        key={actualTheme + (isSystemTheme ? '-system' : '')}
        initial={{ rotate: -90, opacity: 0 }}
        animate={{ rotate: 0, opacity: 1 }}
        transition={{ duration: 0.2 }}
      >
        <ThemeIcon className={sizeConfig.icon} />
      </motion.div>
      {showLabel && (
        <span>
          {theme === 'system' ? 'Sistema' : theme === 'dark' ? 'Escuro' : 'Claro'}
        </span>
      )}
    </motion.button>
  );

  // Renderizar dropdown
  const renderDropdown = () => (
    <div className="relative">
      <motion.button
        className={cn(
          "flex items-center justify-center rounded-lg transition-all duration-200",
          "bg-background/50 backdrop-blur-sm border border-border",
          "hover:bg-accent hover:text-accent-foreground",
          "focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
          sizeConfig.button,
          className
        )}
        onClick={() => setIsOpen(!isOpen)}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        <motion.div
          key={actualTheme + (isSystemTheme ? '-system' : '')}
          initial={{ rotate: -180, opacity: 0 }}
          animate={{ rotate: 0, opacity: 1 }}
          transition={{ duration: 0.3 }}
        >
          <ThemeIcon className={sizeConfig.icon} />
        </motion.div>
      </motion.button>

      {/* Dropdown Menu */}
      <AnimatePresence>
        {isOpen && (
          <>
            {/* Overlay */}
            <motion.div
              className="fixed inset-0 z-40"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={() => setIsOpen(false)}
            />

            {/* Menu */}
            <motion.div
              className="absolute top-full right-0 mt-2 bg-popover border border-border rounded-lg shadow-lg overflow-hidden z-50 min-w-[180px]"
              initial={{ opacity: 0, scale: 0.8, y: -10 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.8, y: -10 }}
              transition={{ duration: 0.2 }}
            >
              {/* Header */}
              <div className="px-3 py-2 border-b border-border">
                <div className="flex items-center gap-2">
                  <Palette className="w-4 h-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Tema</span>
                </div>
              </div>

              {/* Opções */}
              <div className="p-1">
                {themeOptions.map((option, index) => (
                  <motion.button
                    key={option.value}
                    className={cn(
                      "w-full flex items-center gap-3 px-3 py-2 rounded-md transition-colors text-left",
                      "hover:bg-accent hover:text-accent-foreground",
                      theme === option.value && "bg-accent text-accent-foreground"
                    )}
                    onClick={() => {
                      setTheme(option.value);
                      setIsOpen(false);
                    }}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.05 }}
                    whileHover={{ scale: 1.02 }}
                  >
                    <option.icon className="w-4 h-4" />
                    <div className="flex-1">
                      <div className="text-sm font-medium">{option.label}</div>
                      <div className="text-xs text-muted-foreground">
                        {option.description}
                      </div>
                    </div>
                    {theme === option.value && (
                      <motion.div
                        className="w-2 h-2 bg-primary rounded-full"
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ delay: 0.1 }}
                      />
                    )}
                  </motion.button>
                ))}
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  );

  // Renderizar baseado na variante
  switch (variant) {
    case 'fab':
      return renderFAB();
    case 'minimal':
      return renderMinimal();
    case 'dropdown':
      return renderDropdown();
    default:
      return renderSimpleButton();
  }
};

export default ThemeToggle;
