
import React, { useState } from 'react';
import { ChevronLeft, ChevronRight, ChevronUp, ChevronDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import PulsingCTAButton from './PulsingCTAButton';

interface ProjectInfoProps {
  title: string;
  description: string;
  type?: string;
  htmlContent?: string;
  exocadHtmlUrl?: string;
  infoPanelCollapsed: boolean;
  onToggleInfoPanel: () => void;
  onShowHtmlModal: () => void;
  onNavigateNext: () => void;
  onNavigatePrevious: () => void;
  isMobile: boolean;
}

const InfoPanel = ({
  title,
  description,
  type,
  htmlContent,
  exocadHtmlUrl,
  infoPanelCollapsed,
  onToggleInfoPanel,
  onShowHtmlModal,
  onNavigateNext,
  onNavigatePrevious,
  isMobile
}: ProjectInfoProps) => {

  const hasContent = htmlContent || exocadHtmlUrl;
  const [isCollapsing, setIsCollapsing] = useState(false);

  // Função para lidar com o recolhimento com animação
  const handleToggleWithAnimation = () => {
    if (!infoPanelCollapsed) {
      // Se estiver expandido, primeiro anima o recolhimento
      setIsCollapsing(true);
      setTimeout(() => {
        // Depois da animação, realmente recolhe
        onToggleInfoPanel();
        setIsCollapsing(false);
      }, 350); // Duração da animação
    } else {
      // Se estiver recolhido, apenas expande
      onToggleInfoPanel();
    }
  };

  if (isMobile) {
    return (
      <div
        className={cn(
          "fixed right-0 left-0 transition-all duration-350 ease-in-out pointer-events-auto z-20",
          infoPanelCollapsed
            ? "bottom-[calc(60px+4px)] h-8 bg-transparent" // Position right above the carousel (60px height + 4px spacing)
            : "bottom-[60px] pb-2" // Position right above the carousel with expanded panel
        )}
      >
        {infoPanelCollapsed ? (
          <button
            onClick={onToggleInfoPanel}
            className="mx-auto w-full flex items-center justify-center h-8"
            aria-label="Expandir painel de informações"
          >
            <div className="bg-white/5 hover:bg-white/10 px-3 py-1 rounded-full flex items-center transition-colors">
              <h3 className="text-base font-medium mr-2 uppercase text-white truncate max-w-[80vw]">{title}</h3>
              <ChevronUp className="h-4 w-4 text-white flex-shrink-0" />
            </div>
          </button>
        ) : (
          <div
            className="bg-black/5 backdrop-blur-sm p-3 rounded-t-lg mx-2 relative overflow-hidden"
            style={{ animation: isCollapsing ? 'collapseUp 0.35s ease-in-out forwards' : 'expandDown 0.35s ease-in-out forwards' }}>
            <button
              onClick={handleToggleWithAnimation}
              className="absolute top-2 right-2 p-1.5 rounded-full bg-white/5 hover:bg-white/10 transition-colors"
              aria-label="Recolher painel de informações"
            >
              <ChevronDown className="h-5 w-5 text-white" />
            </button>

            <div>
              <h1 className="text-base font-medium mb-2 uppercase text-white">{title}</h1>
              <p className="text-[10px] text-white/60 mb-2 uppercase text-justify leading-tight">{description}</p>

              <div className="mb-2">
                <div className="flex flex-wrap gap-1 items-center">
                  <h3 className="text-[10px] font-medium uppercase text-white/80 mr-1">CATEGORIA:</h3>
                  {type && (
                    <span className="px-1.5 py-0.5 text-[10px] uppercase bg-white/10 hover:bg-white/15 transition-colors rounded-md text-white font-medium">{type}</span>
                  )}
                </div>
              </div>

              {hasContent && (
                <PulsingCTAButton
                  onClick={onShowHtmlModal}
                  size="sm"
                  className="text-[10px] py-1 h-auto"
                  pulseInterval={15000} // 15 segundos para mobile (mais tempo)
                />
              )}
            </div>
          </div>
        )}
      </div>
    );
  }

  // Desktop version
  return (
    <div
      className={cn(
        "absolute top-28 right-6 transition-all duration-350 ease-in-out pointer-events-auto",
        infoPanelCollapsed
          ? "w-auto"
          : "w-full md:w-1/4 lg:w-1/5"
      )}
    >
      {infoPanelCollapsed ? (
        <div
          className="flex items-center bg-black/20 backdrop-blur-sm rounded-full"
        >
          <h2 className="text-2xl font-medium text-white ml-4 uppercase">{title}</h2>
          <button
            onClick={handleToggleWithAnimation}
            className="ml-4 w-12 h-12 flex items-center justify-center rounded-full bg-black/20 backdrop-blur-sm hover:bg-black/30 transition-colors"
            aria-label="Expandir painel de informações"
          >
            <ChevronLeft className="h-5 w-5 text-white" />
          </button>
        </div>
      ) : (
        <div
          className="bg-black/20 backdrop-blur-sm p-6 rounded-lg relative overflow-hidden"
          style={{ animation: isCollapsing ? 'collapseUp 0.35s ease-in-out forwards' : 'expandDown 0.35s ease-in-out forwards' }}>
          <button
            onClick={handleToggleWithAnimation}
            className="absolute top-2 right-2 w-8 h-8 flex items-center justify-center rounded-full bg-white/5 hover:bg-white/10 transition-colors"
            aria-label="Recolher painel de informações"
          >
            <ChevronRight className="h-5 w-5 text-white" />
          </button>

          <div>
            <h1 className="text-2xl font-medium mb-4 uppercase text-white">{title}</h1>
            <p className="text-sm text-white/60 mb-6 uppercase leading-relaxed">{description}</p>

            <div className="mb-6">
              <h3 className="text-sm font-medium mb-2 uppercase text-white/80">CATEGORIA</h3>
              <div className="flex flex-wrap gap-2">
                {type && (
                  <span className="px-3 py-1.5 text-xs bg-white/10 hover:bg-white/15 transition-colors rounded-md uppercase text-white font-medium">{type}</span>
                )}
              </div>
            </div>

            <div className="mb-6">
              <h3 className="text-sm font-medium mb-2 uppercase text-white/80">TECNOLOGIAS</h3>
              <div className="flex flex-wrap gap-2">
                <span className="px-2 py-1 text-xs bg-white/5 hover:bg-white/10 transition-colors rounded-sm uppercase text-white">EXOCAD</span>
                <span className="px-2 py-1 text-xs bg-white/5 hover:bg-white/10 transition-colors rounded-sm uppercase text-white">CAD/CAM</span>
                <span className="px-2 py-1 text-xs bg-white/5 hover:bg-white/10 transition-colors rounded-sm uppercase text-white">3D PRINT</span>
              </div>
            </div>

            {hasContent && (
              <PulsingCTAButton
                onClick={onShowHtmlModal}
                size="sm"
                className="mb-2"
                pulseInterval={12000} // 12 segundos para desktop
              />
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default InfoPanel;
